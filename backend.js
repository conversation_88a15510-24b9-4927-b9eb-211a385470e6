require('dotenv').config()

const pino = require('pino')()
const cors = require('cors')
const nocache = require('nocache')
const express = require('express')
const mongoose = require('mongoose')
const moment = require('moment')

const i18next = require('./i18next')

const utils = require('./libs/utils')
const helpers = require('./libs/helpers')

const DevController = require('./controllers/DevController')
const MainController = require('./controllers/MainController')

const MessageDocumentController = require('./controllers/MessageDocumentController')

const LiveChatAuthController = require('./controllers/LiveChatSocket/Auth')

function SendWizardStart(app) {
  const onekbRouter = express.Router()
  onekbRouter.use(express.urlencoded({ extended: true, limit: '150Mb' }))
  onekbRouter.use(express.json({ limit: '150Mb' }))

  // Send Wizaard
  onekbRouter.post('/send-wizard/start', require('./middlewares/AgentApp/CheckAuth'), require('./middlewares/AgentApp/CheckPermission'), require('./middlewares/AgentApp/CheckPackageStatus')(true, 'message_template'), require('./controllers/MessageTemplate/SendWizardStart'))
  onekbRouter.post('/send-wizard/startv2', require('./middlewares/AgentApp/CheckAuth'), require('./middlewares/AgentApp/CheckPermission'), require('./middlewares/AgentApp/CheckPackageStatus')(true, 'message_template'), require('./controllers/MessageTemplate/SendWizardStartV2'))

  app.use('/agent-app/message-template', onekbRouter)
}


Promise.resolve()
  .then(() => {

    pino.info('i18next inited')

    return helpers.retryProcessOnError(() => mongoose.connect(process.env.MONGODB_CONN_STRING))

  })
  .then(() => {

    pino.info('mongodb connected')

    const app = express()

    app.models = {
      AclRole: require('./models/AclRole'),
      AclAction: require('./models/AclAction'),
      AclResource: require('./models/AclResource'),
      AclRoleResource: require('./models/AclRoleResource'),
      AclResourceAction: require('./models/AclResourceAction'),

      Job: require('./models/Job'),
      Log: require('./models/Log'),
      File: require('./models/File'),
      User: require('./models/User'),
      Chat: require('./models/Chat'),
      Token: require('./models/Token'),
      Config: require('./models/Config'),
      Message: require('./models/Message'),
      Channel: require('./models/Channel'),
      Company: require('./models/Company'),
      Container: require('./models/Container'),
      LoginWith: require('./models/LoginWith'),
      WebpImage: require('./models/WebpImage'),
      Integration: require('./models/Integration'),
      FormMessage: require('./models/FormMessage'),
      WebPushToken: require('./models/WebPushToken'),
      WebhookBaseUrl: require('./models/WebhookBaseUrl'),
      UserPermission: require('./models/UserPermission'),
      ChatIntegration: require('./models/ChatIntegration'),
      OnboardingWizard: require('./models/OnboardingWizard')
    }

    app.services = {
      JobService: require('./services/JobService'),
      UserService: require('./services/UserService'),
      ChatService: require('./services/ChatService'),
      QueueService: require('./services/QueueService'),
      IntegrationService: require('./modules/AgentApp/IntegrationService'),
      LogService: require('./services/LogService'),
      TsoftAgentAppService: require('./integrations/Tsoft/AgentApp/TsoftService'),
      TsoftIntegrationService: require('./integrations/Tsoft/TsoftIntegrationService'),
      ShopifyIntegrationService: require('./integrations/Shopify/ShopifyIntegrationService')
    }

    app.set('etag', false)
    app.set('trust proxy', 'loopback')

    app.use(cors())
    app.use(nocache())

    app.use((req, res, next) => {

      req.i18n = {
        language: req.headers['x-app-lang-code'] || 'tr',
        changeLanguage: (value) => {
          req.i18n.language = value
        },
        store: {
          data: i18next.store.data
        }
      }

      req.language = req.i18n.language

      req.t = (value, data = {}) => i18next.t(value, { lng: req.i18n.language, ...data })

      next()
    })

    app.all('/status', (req, res, next) => {

      return helpers.getCpuUsage().then(cpuUsage => {

        const memUsage = process.memoryUsage().heapUsed / 1024 / 1024

        return res.json({
          success: true,
          name: 'Backend',
          data: {
            up_time: helpers.upTime(),
            mem_usage: `${Math.round(memUsage * 100) / 100} MB`,
            cpu_usage: `${cpuUsage} %`,
            nodejs_version: process.versions.node,
            tz_version: process.versions.tz
          }
        })
      })
    })

    app.get('/health', (req, res, next) => {
      return res.modifiedResponse(200, { success: true })
    })

    SendWizardStart(app)

    app.use(express.urlencoded({ extended: true, limit: '100mb' }))
    app.use(express.json({ limit: '100mb' }))

    app.use((req, res, next) => {
      req.trace_id = utils.generateHash(30)
      req.start_time = moment().unix()

      const url = `${req.protocol}://${req.hostname}:${process.env.APP_PORT}`
      const endpoint = req.originalUrl.split('?')[0]

      let bodyData = req.body
      if (req.headers['content-type'] === 'application/json') {
        if (endpoint.includes('auth')) {
          bodyData = {
            data: helpers.encrypt(process.env.APP_SECRET_KEY, '', JSON.stringify(req.body))
          }
        }
      }

      pino.info({
        message: `trace_id generated`,
        trace_id: req.trace_id,
        method: req.method,
        url: `${url}${endpoint}`,
        all_url: `${url}${req.originalUrl}`,
        timestamp: new Date(),
        start_time: req.start_time,
        request_body: JSON.stringify({
          body: bodyData,
          headers: req.headers
        })
      })

      res.modifiedResponse = async function (statusCode, response) {

        let companyId = undefined
        if (req.getState) {
          const stateDto = await req.getState()
          companyId = stateDto.getCompanyId().toString()
        }

        pino.info({
          trace_id: req.trace_id,
          response: JSON.stringify(response),
          company_id: companyId,
          timestamp: new Date(),
          url: `${url}${endpoint}`,
          all_url: `${url}${req.originalUrl}`,
          end_time: moment().unix() - req.start_time // saniye olarak süre veriyor
        })

        return res.status(statusCode).json({
          trace_id: req.trace_id,
          ...response
        })
      }
      next()
    })

    app.post('/dev/webhook-base-url/:id', DevController.ChangeWebHook)

    app.get('/message-document/:id/:hash', MessageDocumentController.Index)

    app.get('/email-confirmed/:id/:hash', require('./controllers/EmailConfirmed'))

    app.get('/ads-medias', require('./controllers/GetAdsMedias'))

    app.use('/agent-app', require('./routes/AgentApp'))
    app.use('/mobile-app', require('./routes/MobileApp'))
    app.use('/agent-app/thinker', require('./routes/Thinker'))
    app.use('/agent-app/message-template', require('./routes/MessageTemplate'))
    app.use('/agent-app/mail', require('./routes/Mail'))
    app.use('/thinker-actions', require('./routes/ThinkerActions'))
    app.use('/api', require('./routes/Api'))
    app.use('/sse', require('./routes/SSE'))
    app.use('/reseller', require('./routes/Reseller'))
    app.use('/tsoft-apps', require('./routes/TsoftApps'))
    app.use('/shopify', require('./routes/Shopify'))

    // Whatsapp Container Token
    app.post('/get-container', require('./controllers/GetContainer')) // @deprecated
    app.post('/get-containers', require('./controllers/GetContainers'))

    // app-socket
    app.post('/app-socket/auth/authenticate', require('./controllers/AgentAppSocket/Auth/Authenticate'))
    app.post('/app-socket/auth/logout', require('./controllers/AgentAppSocket/Auth/Logout'))

    app.post('/log', MainController.Log)
    app.get('/webp-converter/:id', MainController.WebpConverter)
    app.get('/heloscope-image-convertor/:id', MainController.HeloscopeImageConvertor)

    app.use('/site', require('./routes/Site'))

    app.use('/live-chat', require('./routes/LiveChat'))

    // live-chat-socket
    app.post('/live-chat-socket/auth/authenticate', LiveChatAuthController.Authenticate)
    app.post('/live-chat-socket/auth/logout', LiveChatAuthController.Logout)

    app.get('/cdn-proxy', require('./middlewares/AgentApp/CheckAuth'), require('./middlewares/AgentApp/CheckPermission'), require('./controllers/CDNProxy'))

    app.use((err, req, res, next) => {
      helpers.handleError(req, err, res)
    })

    return app

  }).then(async app => {

    const RabbitMQConnection = require('./modules/RabbitMQConnection')
    const RedisConnection = require('./modules/RedisConnection')

    await RabbitMQConnection.connection()
    await RedisConnection.connection()

    const port = process.env.APP_PORT

    app.listen(port, () => pino.info(`backend app listening on port ${port}!`))

  })
  .catch(error => {

    pino.error('BACKEND: mongodb error')
    pino.error(error)

  })
