const enums = {
  CONTAINER_STATUS: {
    OKEY: 'OKEY',
    EMPTY: 'EMPTY'
  },
  service_names: {
    WHATSAPP: 'WHATSAPP',
    FACEBOOK: 'FACEBOOK',
    INSTAGRAM: 'INSTAGRAM'
  },
  channel_types: {
    LIVE_CHAT: 'LIVE_CHAT',
    FACEBOOK_PAGE: 'FACEBOOK_PAGE',
    WHATSAPP_NUMBER: 'WHATSAPP_NUMBER',
    INSTAGRAM_ACCOUNT: 'INSTAGRAM_ACCOUNT',
    TELEGRAM_ACCOUNT: 'TELEGRAM_ACCOUNT',
  },
  channel_providers: {
    FACEBOOK: 'FACEBOOK',
    LIVE_CHAT: 'LIVE_CHAT',
    TEKROM: 'TEKROM',
    CLOUD: 'CLOUD',
  },
  thinker_message_types: {
    text: 'text',
    image: 'image',
    video: 'video',
    interactive: 'interactive',
    audio: 'audio',
    template: 'template',
    document: 'document',
    location: 'location'
  },
  message_types: {
    TEXT: 'TEXT',
    IMAGE_URL: 'IMAGE_URL',
    FILE_URL: 'FILE_URL',
    VIDEO_URL: 'VIDEO_URL',
    AUDIO_URL: 'AUDIO_URL',
    LIST: 'LIST',
    BUTTON: 'BUTTON',
    INSTAGRAM_GENERIC_BUTTON: 'INSTAGRAM_GENERIC_BUTTON',
    FACEBOOK_GENERIC_BUTTON: 'FACEBOOK_GENERIC_BUTTON',
    HUMAN_AGENT: 'HUMAN_AGENT',
    UNSUPPORTED_MESSAGE: 'UNSUPPORTED_MESSAGE',
    ADS_OPEN_THREAD: 'ADS_OPEN_THREAD',
    OPEN_THREAD: 'OPEN_THREAD',
    ONLY_ADS_OPEN_THREAD: 'ONLY_ADS_OPEN_THREAD',

    LIVECHAT_BUTTON: 'LIVECHAT_BUTTON',
    LIVECHAT_LOCATION: 'LIVECHAT_LOCATION',
    LIVECHAT_REPLY_TO_MESSAGE: 'LIVECHAT_REPLY_TO_MESSAGE',

    WHATSAPP_TEMPLATE: 'WHATSAPP_TEMPLATE',
    WHATSAPP_TEMPLATEV2: 'WHATSAPP_TEMPLATEV2',
    WHATSAPP_STICKER_URL: 'WHATSAPP_STICKER_URL',

    WHATSAPP_AUDIO_URL: 'WHATSAPP_AUDIO_URL',
    WHATSAPP_DOCUMENT_URL: 'WHATSAPP_DOCUMENT_URL',
    WHATSAPP_IMAGE_URL: 'WHATSAPP_IMAGE_URL',
    WHATSAPP_VIDEO_URL: 'WHATSAPP_VIDEO_URL',
    WHATSAPP_VOICE_URL: 'WHATSAPP_VOICE_URL',
    WHATSAPP_CONTACTS: 'WHATSAPP_CONTACTS',
    WHATSAPP_LOCATION: 'WHATSAPP_LOCATION',
    WHATSAPP_LOCATION_REQUEST: 'WHATSAPP_LOCATION_REQUEST',
    WHATSAPP_REPLY_TO_MESSAGE: 'WHATSAPP_REPLY_TO_MESSAGE',
    WHATSAPP_INTERACTIVE: 'WHATSAPP_INTERACTIVE',
    WHATSAPP_REFERRAL: 'WHATSAPP_REFERRAL',
    WHATSAPP_REACTION: 'WHATSAPP_REACTION',

    FACEBOOK_IMAGE_URLS: 'FACEBOOK_IMAGE_URLS',
    FACEBOOK_VIDEO_URLS: 'FACEBOOK_VIDEO_URLS',
    FACEBOOK_AUDIO_URLS: 'FACEBOOK_AUDIO_URLS',
    FACEBOOK_FILE_URLS: 'FACEBOOK_FILE_URLS',
    FACEBOOK_FALLBACK_URLS: 'FACEBOOK_FALLBACK_URLS',
    FACEBOOK_STICKER_URLS: 'FACEBOOK_STICKER_URLS',
    FACEBOOK_LOCATION_URLS: 'FACEBOOK_LOCATION_URLS',
    FACEBOOK_IMAGE_URL: 'FACEBOOK_IMAGE_URL', // @deprecated
    FACEBOOK_STICKER_URL: 'FACEBOOK_STICKER_URL', // @deprecated
    FACEBOOK_VIDEO_URL: 'FACEBOOK_VIDEO_URL', // @deprecated
    FACEBOOK_AUDIO_URL: 'FACEBOOK_AUDIO_URL', // @deprecated
    FACEBOOK_FILE_URL: 'FACEBOOK_FILE_URL', // @deprecated
    FACEBOOK_LOCATION: 'FACEBOOK_LOCATION',
    FACEBOOK_FALLBACK: 'FACEBOOK_FALLBACK',
    FACEBOOK_QUICK_REPLY: 'FACEBOOK_QUICK_REPLY',
    FACEBOOK_REPLY_TO_MESSAGE: 'FACEBOOK_REPLY_TO_MESSAGE',
    FACEBOOK_MEDIA_REPLY: 'FACEBOOK_MEDIA_REPLY',
    FACEBOOK_PRODUCT_TEMPLATE: 'FACEBOOK_PRODUCT_TEMPLATE',

    INSTAGRAM_IMAGE_URLS: 'INSTAGRAM_IMAGE_URLS',
    INSTAGRAM_VIDEO_URLS: 'INSTAGRAM_VIDEO_URLS',
    INSTAGRAM_AUDIO_URLS: 'INSTAGRAM_AUDIO_URLS',
    INSTAGRAM_FILE_URLS: 'INSTAGRAM_FILE_URLS',
    INSTAGRAM_SHARE_URLS: 'INSTAGRAM_SHARE_URLS',
    INSTAGRAM_STORY_MENTION_URLS: 'INSTAGRAM_STORY_MENTION_URLS',
    INSTAGRAM_QUICK_REPLY: 'INSTAGRAM_QUICK_REPLY',
    INSTAGRAM_REPLY_TO_STORY: 'INSTAGRAM_REPLY_TO_STORY',
    INSTAGRAM_REPLY_TO_MESSAGE: 'INSTAGRAM_REPLY_TO_MESSAGE',
    INSTAGRAM_MEDIA_REPLY: 'INSTAGRAM_MEDIA_REPLY',
    INSTAGRAM_REELS: 'INSTAGRAM_REELS',
    INSTAGRAM_STORY: 'INSTAGRAM_STORY',
    INSTAGRAM_QUICK_REPLY_ANSWER: 'INSTAGRAM_QUICK_REPLY_ANSWER',
    INSTAGRAM_PRODUCT_MESSAGE: 'INSTAGRAM_PRODUCT_MESSAGE',
    INFOBIP_IMAGE_URL: 'INFOBIP_IMAGE_URL',

    TELEGRAM_UPDATE_SHORT_MESSAGE: "UpdateShortMessage",
    TELEGRAM_UPDATE_NEW_MESSAGE: "UpdateNewMessage",
  },
  app_message_types: {
    TEXT: 'TEXT',
    IMAGE_URL: 'IMAGE_URL',
    VIDEO_URL: 'VIDEO_URL',
    VOICE_URL: 'VOICE_URL',
    AUDIO_URL: 'AUDIO_URL',
    FILE_URL: 'FILE_URL',
    STICKER_URL: 'STICKER_URL',

    IMAGE_URLS: 'IMAGE_URLS',
    VIDEO_URLS: 'VIDEO_URLS',
    FILE_URLS: 'FILE_URLS',
    AUDIO_URLS: 'AUDIO_URLS',
    SHARE_URLS: 'SHARE_URLS',
    STORY_MENTION_URLS: 'STORY_MENTION_URLS',

    LOCATION_URL: 'LOCATION_URL',
    CONTACTS: 'CONTACTS',
    REPLY_MESSAGE: 'REPLY_MESSAGE',
    QUICK_REPLY: 'QUICK_REPLY',
    QUICK_REPLY_ANSWER: 'QUICK_REPLY_ANSWER',

    INTERACTIVE: 'INTERACTIVE',
    TEMPLATE_MESSAGE: 'TEMPLATE_MESSAGE',
    TEMPLATE_MESSAGEV2: 'TEMPLATE_MESSAGEV2',
    MEDIA_REPLY: 'MEDIA_REPLY',
    GENERIC_BUTTON: 'GENERIC_BUTTON',
    UNSUPPORTED_MESSAGE: 'UNSUPPORTED_MESSAGE',
    REPLY_TEMPLATE_MESSAGE: 'REPLY_TEMPLATE_MESSAGE',
    ADS_OPEN_THREAD: 'ADS_OPEN_THREAD',
    ONLY_ADS_OPEN_THREAD: 'ONLY_ADS_OPEN_THREAD',
    REELS: 'REELS',
    STORY: 'STORY',
    REFERRAL: 'REFERRAL',
    INSTAGRAM_REPLY_TO_STORY: 'INSTAGRAM_REPLY_TO_STORY',
    INSTAGRAM_PRODUCT_MESSAGE: 'INSTAGRAM_PRODUCT_MESSAGE',

    LIVECHAT_BUTTON: 'LIVECHAT_BUTTON',
    LIVECHAT_REPLY_MESSAGE: 'LIVECHAT_REPLY_MESSAGE',
    WHATSAPP_LOCATION_REQUEST: 'WHATSAPP_LOCATION_REQUEST',

    FACEBOOK_PRODUCT_TEMPLATE: 'FACEBOOK_PRODUCT_TEMPLATE'
  },
  token_types: {
    SITE_LOGIN: 'SITE_LOGIN',
    APP_LOGIN: 'APP_LOGIN',
    SITE_FORGOT_PASSWORD: 'SITE_FORGOT_PASSWORD',
    APP_FORGOT_PASSWORD: 'APP_FORGOT_PASSWORD',
    TEMP_TOKEN: 'TEMP_TOKEN',
    NOC_LOGIN: 'NOC_LOGIN',
    EMAIL_CONFIRMATION: 'EMAIL_CONFIRMATION'
  },
  login_with_types: {
    EMAIL_PASSWORD: 'EMAIL_PASSWORD'
  },
  message_from_types: {
    AGENT: 'AGENT',
    CUSTOMER: 'CUSTOMER',
    SYSTEM: 'SYSTEM',
  },
  message_send_statuses: {
    QUEUED: 'QUEUED',
    DELETED: 'DELETED',
    SENT: 'SENT',
    DELIVERED: 'DELIVERED',
    SEEN: 'SEEN',
    SENT_FAILED: 'SENT_FAILED', // Whatsapp tarafında gönderilemeyince failed oluyor.
    CANNOT_SEND: 'CANNOT_SEND'
  },
  agent_app_socket_events: {
    CHAT_OWNED_BY_AGENT: 'CHAT_OWNED_BY_AGENT',
    MESSAGE_SENT: 'MESSAGE_SENT',
    MESSAGE_SEEN: 'MESSAGE_SEEN',
    MESSAGE_UNREAD: 'MESSAGE_UNREAD',
    MESSAGE_READ: 'MESSAGE_READ',
    MESSAGE_DELETED: 'MESSAGE_DELETED',
    MESSAGE_DELIVERED: 'MESSAGE_DELIVERED',
    NEW_PUBLIC_MESSAGE_RECEVIED: 'NEW_PUBLIC_MESSAGE_RECEVIED',
    CHAT_MOVED_TO_PUBLIC: 'CHAT_MOVED_TO_PUBLIC',
    MESSAGE_RECEIVED_TO_OWNED_CHAT: 'MESSAGE_RECEIVED_TO_OWNED_CHAT',
    WEB_APP_UPDATED: 'WEB_APP_UPDATED',
    MOBILE_APP_UPDATED: 'MOBILE_APP_UPDATED',
    CHAT_TIMEOUT: 'CHAT_TIMEOUT',
    ORDER_CREATED: 'ORDER_CREATED',
    ITEM_ADDED_TO_CART: 'ITEM_ADDED_TO_CART',
    UPDATE_CART: 'UPDATE_CART',
    ADDRESS_SELECTED: 'ADDRESS_SELECTED',
    CARGO_SELECTED: 'CARGO_SELECTED',
    PAYMENT_OPTION_SELECTED: 'PAYMENT_OPTION_SELECTED',
    MARKED_AS_SEEN: 'MARKED_AS_SEEN',
    CREATE_CUSTOMER_CONFIRMED: 'CREATE_CUSTOMER_CONFIRMED',
    CREATED_CART: 'CREATED_CART',
    MESSAGE_SENT_FAILED: 'MESSAGE_SENT_FAILED',
    CHAT_HIDE: 'CHAT_HIDE',
    INVOICE_ADDRESS_SELECTED: 'INVOICE_ADDRESS_SELECTED',
    DELIVERY_ADDRESS_SELECTED: 'DELIVERY_ADDRESS_SELECTED',
    CUSTOMER_DATA_UPDATED: 'CUSTOMER_DATA_UPDATED',
    CUSTOMER_PAIRED: 'CUSTOMER_PAIRED',
    CUSTOMER_PAIRING_REMOVED: 'CUSTOMER_PAIRING_REMOVED',
    ORDER_STAGE_CHANGED: 'ORDER_STAGE_CHANGED',
    AGENT_DISABLED: 'AGENT_DISABLED',
    CHAT_UNARCHIVED: 'CHAT_UNARCHIVED',
    AGENT_LOGGED_OUT: 'AGENT_LOGGED_OUT',
    MESSAGE_REACTED: 'MESSAGE_REACTED',
    MESSAGE_UNREACTED: 'MESSAGE_UNREACTED',
    MULTI_CHAT_HIDE: 'MULTI_CHAT_HIDE',
    ACTIVE_MULTI_CHAT_HIDE: 'ACTIVE_MULTI_CHAT_HIDE',
    CAMPAIGN_SELECTED: 'CAMPAIGN_SELECTED',
    CHAT_ASSIGNED_FROM_AGENT: 'CHAT_ASSIGNED_FROM_AGENT',
    UPDATE_USER_PROFILE: 'UPDATE_USER_PROFILE',
    CHAT_ASSIGNED_TO_AGENT: 'CHAT_ASSIGNED_TO_AGENT',
    CHAT_UNARCHIVED_TO_AGENT: 'CHAT_UNARCHIVED_TO_AGENT',
    GOOGLE_WINDOW_CLOSE: 'GOOGLE_WINDOW_CLOSE',
    INSTAGRAM_ONBOARDING_COMPLETED: 'INSTAGRAM_ONBOARDING_COMPLETED',
    FORCE_TOOK_CHAT: 'FORCE_TOOK_CHAT',
    MESSAGE_RETRY_SENDED: 'MESSAGE_RETRY_SENDED',
    MESSAGE_RETRY_FAILED: 'MESSAGE_RETRY_FAILED',
    NEW_CHAT_ACTION: 'NEW_CHAT_ACTION',
    CHAT_PINNED: 'CHAT_PINNED',
    CHAT_BLOCKED: 'CHAT_BLOCKED',
    AGENT_MESSAGE_UNREACTED: 'AGENT_MESSAGE_UNREACTED',
    AGENT_MESSAGE_REACTED: 'AGENT_MESSAGE_REACTED',

    // Marketing
    NEW_COMMENT_RECEIVED: 'NEW_COMMENT_RECEIVED',
    MARK_AS_SEEN_COMMENT: 'MARK_AS_SEEN_COMMENT',
    DELETED_POST: 'DELETED_POST',
    DELETED_COMMENT: 'DELETED_COMMENT',

    // Arvia
    ARVIA_ROOM_CLOSED: 'ARVIA_ROOM_CLOSED',

    // Thinker
    THINKER_BOT_STOP: 'THINKER_BOT_STOP',
    THINKER_BOT_STARTED: 'THINKER_BOT_STARTED',
    THINKER_CHAT: 'THINKER_CHAT',
    THINKER_FLOW_TIMEOUT: 'THINKER_FLOW_TIMEOUT',
    THINKER_TIMEOUT_FLOW_START: 'THINKER_TIMEOUT_FLOW_START',
    NEW_THINKER_PUBLIC_MESSAGE_RECEVIED: 'NEW_THINKER_PUBLIC_MESSAGE_RECEVIED',

    // Helobot
    HELOBOT_STARTED: 'HELOBOT_STARTED',
    HELOBOT_STOP: 'HELOBOT_STOP',
    HELOBOT_CHAT: 'HELOBOT_CHAT',
    NEW_HELOBOT_PUBLIC_MESSAGE_RECEVIED: 'NEW_HELOBOT_PUBLIC_MESSAGE_RECEVIED',
    HELOBOT_CREDIT_DONE: 'HELOBOT_CREDIT_DONE',

    ONLINE_WATCHER_CHAT_ASSIGNED_TO_AGENT: 'ONLINE_WATCHER_CHAT_ASSIGNED_TO_AGENT',
    ONLINE_WATCHER_MESSAGE_RECEIVED_TO_OWNED_CHAT: 'ONLINE_WATCHER_MESSAGE_RECEIVED_TO_OWNED_CHAT',
    ONLINE_WATCHER_MESSAGE_SENT: 'ONLINE_WATCHER_MESSAGE_SENT',

    BILLTEKROM_PACKAGE_UPDATED: 'BILLTEKROM_PACKAGE_UPDATED',
    CONVERSATION_LIMIT_UPDATED: 'CONVERSATION_LIMIT_UPDATED'
  },
  live_chat_socket_events: {
    NEW_MESSAGE_RECEVIED: 'NEW_MESSAGE_RECEVIED',
    MESSAGE_SENT: 'MESSAGE_SENT',
  },
  expire_times: {
    TOKEN_EXPIRE_TIME: 1 // days
  },
  file_ext_types: {
    FACEBOOK_FILE: "FACEBOOK_FILE",
    FACEBOOK_USER_IMAGE: "FACEBOOK_USER_IMAGE",
    FACEBOOK_IMAGE: "FACEBOOK_IMAGE",
    INSTAGRAM_USER_IMAGE: "INSTAGRAM_USER_IMAGE"
  },
  ref_entity_types: {
    USER: "USER",
    CHANNEL: "CHANNEL",
    MESSAGE: "MESSAGE"
  },
  user_ext_types: {
    FACEBOOK_PS_USER: 'FACEBOOK_PS_USER',
    INSTAGRAM_ACCOUNT: 'INSTAGRAM_ACCOUNT',
    TEKROM_WHATSAPP_USER: 'TEKROM_WHATSAPP_USER',
    LIVE_CHAT_USER: 'LIVE_CHAT_USER',
    TELEGRAM_USER: 'TELEGRAM_USER'
  },
  form_message_types: {
    TRY: 'TRY',
    CONTACT: 'CONTACT'
  },
  job_types: {
    SEND_MESSAGE: 'SEND_MESSAGE',
    AGENT_APP_BOT_ACTION: 'AGENT_APP_BOT_ACTION',
    NOT_REPLIED: 'NOT_REPLIED',
    CUSTOMER_NOT_REPLIED: 'CUSTOMER_NOT_REPLIED',
    LIVE_CHAT: 'LIVE_CHAT',
    USER_INTEGRATION: 'USER_INTEGRATION',
    BOT_ACTION: 'BOT_ACTION',
    INCOMING_WEBHOOK: 'INCOMING_WEBHOOK',
    DELIVERED_MESSAGE: 'DELIVERED_MESSAGE',
    MESSAGE_SENT: 'MESSAGE_SENT',
    MESSAGE_FAILED: 'MESSAGE_FAILED',
    DELETED_MESSAGE: 'DELETED_MESSAGE',
    MESSAGE_SEEN: 'MESSAGE_SEEN',
    MESSAGE_REACTED: 'MESSAGE_REACTED',
    INCOMING_MESSAGE: 'INCOMING_MESSAGE',
    ONBOARDING_WIZARD_CRON: 'ONBOARDING_WIZARD_CRON',
    CUSTOMER_EVALUATION: 'CUSTOMER_EVALUATION',
    TEAM_MESSAGE: 'TEAM_MESSAGE',
    HELOBOT_TIMEOUT: 'HELOBOT_TIMEOUT',
    ABANDONED_CART_MESSAGE: 'ABANDONED_CART_MESSAGE',

    // Marketing
    INCOMING_COMMENT: 'INCOMING_COMMENT'
  },
  job_statuses: {
    QUEUED: 'QUEUED',
    DONE: 'DONE',
    ERROR: 'ERROR',
    CREATED: 'CREATED',
  },
  log_types: {
    DEBUG: {
      level: 100,
      level_name: 'DEBUG'
    },
    INFO: {
      level: 200,
      level_name: 'INFO'
    },
    NOTICE: {
      level: 250,
      level_name: 'NOTICE'
    },
    WARNING: {
      level: 300,
      level_name: 'WARNING'
    },
    ERROR: {
      level: 400,
      level_name: 'ERROR'
    },
    CRITICAL: {
      level: 500,
      level_name: 'CRITICAL'
    },
    ALERT: {
      level: 550,
      level_name: 'ALERT'
    },
    EMERGENCY: {
      level: 600,
      level_name: 'EMERGENCY'
    },
  },
  log_channels: {
    BACKEND: 'BACKEND',
    WORKER: 'WORKER',
    SITE: 'SITE',
    APP: 'APP',
    WHATSAPP: 'WHATSAPP',
    INTEGRATION: 'INTEGRATION',
    OTHER: 'OTHER'
  },
  acl_roles: {
    COMPANY_USER: 'COMPANY_USER',
    COMPANY_OWNER: 'COMPANY_OWNER',
    SUPER_ADMIN: 'SUPER_ADMIN',
    NOC_USER: 'NOC_USER',
    INSTAGRAM_COMMENT_USER: 'INSTAGRAM_COMMENT_USER',
    FACEBOOK_COMMENT_USER: 'FACEBOOK_COMMENT_USER',
    THINKER_USER: 'THINKER_USER',
    SERVICE_ACCOUNT: 'SERVICE_ACCOUNT'
  },
  acl_resources: {
    CAMPAIGN: 'CAMPAIGN',
    NOC_PANEL: 'NOC_PANEL',
    CHANNEL: 'CHANNEL',
    SITE: 'SITE',
    USER: 'USER',
    APP: 'APP',
    QUICK_REPLY: 'QUICK_REPLY',
    INTEGRATION: 'INTEGRATION',
    THINKER: 'THINKER',
    CHAT_FORCE_TOOK: 'CHAT_FORCE_TOOK'
  },
  acl_actions: {
    UPDATE_PERMISSIONS: 'UPDATE_PERMISSIONS',
    LOGIN: 'LOGIN',
    LOGIN_AS_AGENT: 'LOGIN_AS_AGENT',
    ENABLE: 'ENABLE',
    DISABLE: 'DISABLE',
    EDIT: 'EDIT',
    CREATE: 'CREATE',
    UPDATE: 'UPDATE',
  },
  permission_options: {
    PLEASE_SELECT: {
      title: 'Please Select', // # deprecated
      value: -1
    },
    ALLOW: {
      title: 'Allow', // # deprecated
      value: 1
    },
    DENY: {
      title: 'Deny',// # deprecated
      value: 0
    },
  },
  INTEGRATION_TYPES: {
    TSOFT: 'TSOFT',
    SHOPIFY: 'SHOPIFY',
    HELOSCOPE: 'HELOSCOPE'
  },
  ORDER_STAGES: {
    STAGE_ADD_TO_CART: 'STAGE_ADD_TO_CART',
    STAGE_SELECT_ADDRESS: 'STAGE_SELECT_ADDRESS',
    STAGE_SELECT_CARGO: 'STAGE_SELECT_CARGO',
    STAGE_SELECT_PAYMENT: 'STAGE_SELECT_PAYMENT',
    STAGE_APPROVE_CART: 'STAGE_APPROVE_CART'
  },
  SHOPIFY_ACTIONS: {
    GET_PRODUCT: 'GET_PRODUCT',
    GET_PRODUCTS: 'GET_PRODUCTS',
    CREATE_CUSTOMER: 'CREATE_CUSTOMER',
    ADD_TO_CART: 'ADD_TO_CART',
    GET_CATALOG: 'GET_CATALOG',
    CREATE_CHECKOUT: 'CREATE_CHECKOUT',
    CREATE_CUSTOMER_ASSOCIATE: 'CREATE_CUSTOMER_ASSOCIATE',
    UPDATE_ADDRESS: 'UPDATE_ADDRESS',
    GET_CART: 'GET_CART',
    GET_ADDRESS: 'GET_ADDRESS',
    GET_ADDRESSES: 'GET_ADDRESSES',
    CREATE_CUSTOMER_ACCESS_TOKEN: 'CREATE_CUSTOMER_ACCESS_TOKEN',
    SEND_CHECKOUT_LINK: 'SEND_CHECKOUT_LINK',
    REMOVE_ITEM_FROM_CART: 'REMOVE_ITEM_FROM_CART',
    GET_VARIANT: 'GET_VARIANT',
    GET_CART_ITEM: 'GET_CART_ITEM',
    UPDATE_CART_ITEM: 'UPDATE_CART_ITEM',
    GET_VARIANT1: 'GET_VARIANT1',
    GET_VARIANT2: 'GET_VARIANT2',
    GET_VARIANT3: 'GET_VARIANT3',
    GET_CURRENCY_CODE: 'GET_CURRENCY_CODE',
    GET_PRODUCTS_FOR_CATALOG_FILTER: 'GET_PRODUCTS_FOR_CATALOG_FILTER',
    GET_PRODUCTS_BY_IDS: 'GET_PRODUCTS_BY_IDS',
    SET_CHECKOUT_DISCOUNT: 'SET_CHECKOUT_DISCOUNT',
    REMOVE_CHECKOUT_DISCOUNT: 'REMOVE_CHECKOUT_DISCOUNT',

    ADMIN_APP_PURCHASE_ONE_TIME_CREATE: 'ADMIN_APP_PURCHASE_ONE_TIME_CREATE',
    ADMIN_GET_APP_PURCHASE_ONE_TIME_STATUS: 'ADMIN_GET_APP_PURCHASE_ONE_TIME_STATUS',
    ADMIN_DELETE_SUBSCRIPTION: 'ADMIN_DELETE_SUBSCRIPTION',
    ADMIN_CREATE_SUBSCRIPTION: 'ADMIN_CREATE_SUBSCRIPTION',

    // 2025-01
    ADMIN_CREATE_DRAFT_ORDER: 'ADMIN_CREATE_DRAFT_ORDER',
    ADMIN_GET_CATALOG: 'ADMIN_GET_CATALOG',
    ADMIN_GET_CUSTOMER_ORDERS: 'ADMIN_GET_CUSTOMER_ORDERS',
    ADMIN_GET_DRAFT_ORDER: 'ADMIN_GET_DRAFT_ORDER',
    ADMIN_GET_ORDER: 'ADMIN_GET_ORDER',
    ADMIN_GET_PRODUCTS: 'ADMIN_GET_PRODUCTS',
    ADMIN_UPDATE_DRAFT_ORDER: 'ADMIN_UPDATE_DRAFT_ORDER',
    ADMIN_CREATE_CUSTOMER: 'ADMIN_CREATE_CUSTOMER',
    ADMIN_UPDATE_CUSTOMER: 'ADMIN_UPDATE_CUSTOMER',
    ADMIN_GET_CUSTOMER: 'ADMIN_GET_CUSTOMER',
    ADMIN_GET_CUSTOMERS: 'ADMIN_GET_CUSTOMERS',
    ADMIN_GET_PRODUCTS_BY_ID: 'ADMIN_GET_PRODUCTS_BY_ID',
    ADMIN_GET_PRODUCTS_FOR_CATALOG_FILTER: 'ADMIN_GET_PRODUCTS_FOR_CATALOG_FILTER',
    ADMIN_DELETE_DRAFT_ORDER: 'ADMIN_DELETE_DRAFT_ORDER',
    ADMIN_GET_PRODUCTS_BY_IDS: 'ADMIN_GET_PRODUCTS_BY_IDS',
    ADMIN_GET_DISCOUNT_CODES: 'ADMIN_GET_DISCOUNT_CODES',
    ADMIN_GET_VARIANT1: 'ADMIN_GET_VARIANT1',
    ADMIN_GET_VARIANT2: 'ADMIN_GET_VARIANT2',
    ADMIN_GET_VARIANT3: 'ADMIN_GET_VARIANT3',
    ADMIN_GET_CARGO_OPTIONS: 'ADMIN_GET_CARGO_OPTIONS',
    ADMIN_ADD_TAG: 'ADMIN_ADD_TAG',
    ADMIN_REMOVE_TAG: 'ADMIN_REMOVE_TAG',
    ADMIN_GET_PAYMENT_OPTIONS: 'ADMIN_GET_PAYMENT_OPTIONS',
    ADMIN_CREATE_ORDER: 'ADMIN_CREATE_ORDER',
    ADMIN_ADD_SCRIPT_TAG: 'ADMIN_ADD_SCRIPT_TAG',
    ADMIN_REMOVE_SCRIPT_TAG: 'ADMIN_REMOVE_SCRIPT_TAG',
  },
  HELOSCOPE_ACTIONS: {
    LOGIN: 'LOGIN',
    REGISTER: 'REGISTER',
    GET_CATALOG: 'GET_CATALOG',
    GET_PRODUCT: 'GET_PRODUCT',
    GET_PRODUCTS: 'GET_PRODUCTS',
    GET_ORDERS: 'GET_ORDERS',
    GET_CUSTOMER: 'GET_CUSTOMER',
    GET_CUSTOMERS: 'GET_CUSTOMERS',
    GET_ORDER_STATUS: 'GET_ORDER_STATUS',
    GET_CITIES: 'GET_CITIES',
    GET_COUNTRIES: 'GET_COUNTRIES',
    GET_TOWNS: 'GET_TOWNS',
    ADD_CUSTOMER_ADDRESS: 'ADD_CUSTOMER_ADDRESS',
    GET_CURRENCIES: 'GET_CURRENCIES',
    SET_CURRENCY_CODE: 'SET_CURRENCY_CODE',
    GET_CART: 'GET_CART',
    ADD_TO_CART: 'ADD_TO_CART',
    REMOVE_ITEM_FROM_CART: 'REMOVE_ITEM_FROM_CART',
    DESTROY_CART: 'DESTROY_CART',
    GET_ORDER: 'GET_ORDER',
    SELECT_CARGO_OPTION: 'SELECT_CARGO_OPTION',
    SELECT_PAYMENT_OPTION: 'SELECT_PAYMENT_OPTION',
    SELECT_LANGUAGE: 'SELECT_LANGUAGE',
    COMPLETE_ORDER: 'COMPLETE_ORDER',
    SET_ORDER_NOTE: 'SET_ORDER_NOTE',
    GET_ORDER_NOTE: 'GET_ORDER_NOTE',
    GET_CATEGORIES: 'GET_CATEGORIES',
    GET_CARGO_OPTIONS: 'GET_CARGO_OPTIONS',
    GET_PAYMENT_OPTIONS: 'GET_PAYMENT_OPTIONS',
    GET_CUSTOMER_DATA: 'GET_CUSTOMER_DATA',
    GET_QUOTA: 'GET_QUOTA',
    GET_USE_TRAFFIC: 'GET_USE_TRAFFIC',
    GET_PANEL_INFO: 'GET_PANEL_INFO',
    GET_ADDRESSES: 'GET_ADDRESSES',
    GET_LANGS: 'GET_LANGS',
    GET_DISTRICTS: 'GET_DISTRICTS'
  },
  TSOFT_ACTIONS: {
    LOGIN: 'LOGIN',
    CREATE_CUSTOMER: 'CREATE_CUSTOMER',
    GET_CUSTOMER: 'GET_CUSTOMER',
    GET_CUSTOMERS: 'GET_CUSTOMERS',
    GET_CUSTOMER_ADDRESSES: 'GET_CUSTOMER_ADDRESSES',
    GET_CUSTOMER_ADDRESS: 'GET_CUSTOMER_ADDRESS',
    ADD_CUSTOMER_ADDRESS: 'ADD_CUSTOMER_ADDRESS',
    REMOVE_ADDRESS: 'REMOVE_ADDRESS',
    UPDATE_CUSTOMER_ADDRESS: 'UPDATE_CUSTOMER_ADDRESS',
    GET_CITIES: 'GET_CITIES',
    GET_COUNTRIES: 'GET_COUNTRIES',
    GET_TOWNS: 'GET_TOWNS',
    GET_CART: 'GET_CART',
    ADD_TO_CART: 'ADD_TO_CART',
    DEC_ITEM_FROM_CART: 'DEC_ITEM_FROM_CART',
    INC_ITEM_FROM_CART: 'INC_ITEM_FROM_CART',
    REMOVE_ITEM_FROM_CART: 'REMOVE_ITEM_FROM_CART',
    EMPTY_CART: 'EMPTY_CART',
    GET_ORDER: 'GET_ORDER',
    GET_ORDERS: 'GET_ORDERS',
    CREATE_ORDER: 'CREATE_ORDER',
    CARGO_TRACKING: 'CARGO_TRACKING',
    GET_PRODUCT: 'GET_PRODUCT',
    GET_ORDER_STATUSES: 'GET_ORDER_STATUSES',
    GET_PAYMENT_OPTIONS: 'GET_PAYMENT_OPTIONS',
    GET_ORDER_NOTES: 'GET_ORDER_NOTES',
    ADD_ORDER_NOTE: 'ADD_ORDER_NOTE',
    GET_ADD_ADDRESS_FORM_FIELDS: 'GET_ADD_ADDRESS_FORM_FIELDS',
    GET_ADD_CUSTOMER_FORM_FIELDS: 'GET_ADD_CUSTOMER_FORM_FIELDS',
    GET_VERSION: 'GET_VERSION',
    SET_COUPON_CODE: 'SET_COUPON_CODE',
    GET_SETTING_BY_KEY: 'GET_SETTING_BY_KEY',
    CREATE_GUEST_ORDER: 'CREATE_GUEST_ORDER',
    SEARCH_PRODUCTS: 'SEARCH_PRODUCTS',
    GET_PRODUCTS: 'GET_PRODUCTS',
    UPDATE_ITEM_FROM_CART: 'UPDATE_ITEM_FROM_CART',
    APPLY_CAMPAIGN: 'APPLY_CAMPAIGN',
    GET_DISTRICTS: 'GET_DISTRICTS',
    GET_CATALOG: 'GET_CATALOG',
    GET_CARGO_OPTIONS: 'GET_CARGO_OPTIONS',
    CREATE_ORDE2R: 'CREATE_ORDE2R',
    EDIT_CUSTOMER_ADDRESS: 'EDIT_CUSTOMER_ADDRESS',
    DELETE_CUSTOMER_ADDRESS: 'DELETE_CUSTOMER_ADDRESS',
    SET_CAMPAIGN_DETAIL: 'SET_CAMPAIGN_DETAIL',
    SET_CAMPAIGN_PROMOTION: 'SET_CAMPAIGN_PROMOTION',
    SET_CAMPAIGN_CONDITION_INCLUDE: 'SET_CAMPAIGN_CONDITION_INCLUDE',
    GET_CAMPAIGNS: 'GET_CAMPAIGNS',
    SET_CAMPAIGN_GIFT_CODE: 'SET_CAMPAIGN_GIFT_CODE',
    GET_PRODUCTS_FILTER: 'GET_PRODUCTS_FILTER',
    GET_MODELS: 'GET_MODELS',
    GET_BRANDS: 'GET_BRANDS',
    GET_CATEGORIES: 'GET_CATEGORIES',
    GET_CURRENCY_CODES: 'GET_CURRENCY_CODES',
    GET_LANGS: 'GET_LANGS',
    SET_TRACKING_CODE: 'SET_TRACKING_CODE',
    GET_TRACKING_CODE: 'GET_TRACKING_CODE',
    DELETE_TRACKING_CODE: 'DELETE_TRACKING_CODE',
  },
  TSOFT_BOT_MESSAGE_ACTIONS: {
    FIRST_MESSAGE: 'FIRST_MESSAGE',
    ORDER_STATUS: 'ORDER_STATUS',
    CREATE_ORDER: 'CREATE_ORDER',
    ADD_TO_CART: 'ADD_TO_CART',
    SELECT_VARIANT1: 'SELECT_VARIANT1',
    SELECT_VARIANT2: 'SELECT_VARIANT2',
    SELECT_ADDRESS: 'SELECT_ADDRESS',
    SELECT_CARGO: 'SELECT_CARGO',
    SELECT_PAYMENT_OPTION: 'SELECT_PAYMENT_OPTION',
    CREATE_CUSTOMER_CONFIRMATION: 'CREATE_CUSTOMER_CONFIRMATION',
    CREATE_CART: 'CREATE_CART',
    ADDRESS_CONFIRMATION: 'ADDRESS_CONFIRMATION',
    EMAIL_RECEIVED: 'EMAIL_RECEIVED',
    IS_SAME_DELIVERY_AND_INVOICE_ADDRESS: 'IS_SAME_DELIVERY_AND_INVOICE_ADDRESS',
    SELECT_INVOICE_ADDRESS: 'SELECT_INVOICE_ADDRESS',
    BECOME_MEMBER: 'BECOME_MEMBER',
    DUPLICATE_MEMBER: 'DUPLICATE_MEMBER',
    SELECT_CAMPAIGN: 'SELECT_CAMPAIGN',
    SEND_ORDER_STATUS_MESSAGE: 'SEND_ORDER_STATUS_MESSAGE',
    DELIVERY_ADDRESS_SELECTED: 'DELIVERY_ADDRESS_SELECTED'
  },
  HELOSCOPE_BOT_MESSAGE_ACTIONS: {
    FIRST_MESSAGE: 'FIRST_MESSAGE',
    ORDER_STATUS: 'ORDER_STATUS',
    CREATE_ORDER: 'CREATE_ORDER',
    ADD_TO_CART: 'ADD_TO_CART',
    SELECT_VARIANT1: 'SELECT_VARIANT1',
    SELECT_VARIANT2: 'SELECT_VARIANT2',
    SELECT_ADDRESS: 'SELECT_ADDRESS',
    SELECT_CARGO: 'SELECT_CARGO',
    SELECT_PAYMENT_OPTION: 'SELECT_PAYMENT_OPTION',
    CREATE_CUSTOMER_CONFIRMATION: 'CREATE_CUSTOMER_CONFIRMATION',
    CREATE_CART: 'CREATE_CART',
    ADDRESS_CONFIRMATION: 'ADDRESS_CONFIRMATION',
    EMAIL_RECEIVED: 'EMAIL_RECEIVED',
    IS_SAME_DELIVERY_AND_INVOICE_ADDRESS: 'IS_SAME_DELIVERY_AND_INVOICE_ADDRESS',
    SELECT_INVOICE_ADDRESS: 'SELECT_INVOICE_ADDRESS',
    BECOME_MEMBER: 'BECOME_MEMBER',
    DUPLICATE_MEMBER: 'DUPLICATE_MEMBER',
    SELECT_CAMPAIGN: 'SELECT_CAMPAIGN'
  },
  SHOPIFY_BOT_MESSAGE_ACTIONS: {
    ADD_TO_CART: 'ADD_TO_CART',
    SELECT_VARIANT1: 'SELECT_VARIANT1',
    SELECT_VARIANT2: 'SELECT_VARIANT2',
    SELECT_VARIANT3: 'SELECT_VARIANT3',
    SELECT_ADDRESS: 'SELECT_ADDRESS',
    CREATE_CART: 'CREATE_CART',
    CREATE_ORDER: 'CREATE_ORDER',
    ADDRESS_CONFIRMATION: 'ADDRESS_CONFIRMATION',
    EMAIL_RECEIVED: 'EMAIL_RECEIVED',
    SELECT_CARGO: 'SELECT_CARGO',
    SELECT_PAYMENT_OPTION: 'SELECT_PAYMENT_OPTION'
  },
  HELOSCOPE_ACTION_TYPES: {
    USER: 'USER',
    ADMIN: 'ADMIN'
  },
  SYSTEM_BOT_MESSAGE_ACTION: {
    CUSTOMER_EVALUATION_MESSAGE: 'CUSTOMER_EVALUATION_MESSAGE'
  },
  ERRORS: {
    INVALID_TOKEN: 'INVALID_TOKEN',
    USER_NOT_FOUND: 'USER_NOT_FOUND',
  },
  LIVE_CHAT_ACTIONS: {
    EDIT: 'EDIT',
    NEW: 'NEW'
  },
  ONBOARDING_STEPS: {
    FIRST: 'FIRST',
    ERROR: 'ERROR',
    SELECT_INTEGRATION: 'SELECT_INTEGRATION',
    LIST_INSTAGRAM_ACCOUNTS: 'LIST_INSTAGRAM_ACCOUNTS',
    ONBORDING_COMPLETING: 'ONBORDING_COMPLETING',
    WHATSAPP_NUMBER: 'WHATSAPP_NUMBER',
    SELECT_PACKAGE: 'SELECT_PACKAGE',
    INSTAGRAM_EMBEDDED_SIGNUP: 'INSTAGRAM_EMBEDDED_SIGNUP',
    WHATSAPP_EMBEDDED_SIGNUP: 'WHATSAPP_EMBEDDED_SIGNUP',
    GET_INFORMATIONS: 'GET_INFORMATIONS',
    GET_INTEGRATIONS: 'GET_INTEGRATIONS',
    CAMPAIGN_PACKAGES: 'CAMPAIGN_PACKAGES',
    GET_CHANNEL_SERVICES: 'GET_CHANNEL_SERVICES',
    SEND_CONFIRM_CODE: 'SEND_CONFIRM_CODE',
    STEPS_DONE: 'STEPS_DONE',
    WHATSAPP_CERTIFICATE_PROCESSING: 'WHATSAPP_CERTIFICATE_PROCESSING'
  },
  ONBOARDINGWIZARD_PROCESS: {
    INITIALIZE: 'INITIALIZE',
    IN_PROCESS: 'IN_PROCESS',
    CONFIRMED: 'CONFIRMED',
    WHATSAPP_PROCESSING: 'WHATSAPP_PROCESSING',
    KILL: 'KILL'
  },
  SHOPIFY_PROCESS: {
    IN_PROCESS: 'IN_PROCESS',
    DONE: 'DONE'
  },
  SHOPIFY_STEPS: {
    FIRST: 'FIRST',
    SECOND: 'SECOND',
    DONE: 'DONE',
  },
  arvia_room_status: {
    CREATED: 'CREATED',
    CLOSED: 'CLOSED'
  },
  allowed_message_types: {
    image: [
      'image/jpeg',
      'image/png',
      'image/jpg'
    ],
    file: [
      'application/pdf', //pdf
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx, doc
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // excel
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', //power point
    ],
    video: [
      'audio/mp4', // mp4
      'video/mp4', // mp4
      'application/mp4', // mp4
      'video/ogg',
      'video/x-msvideo',
      'video/quicktime',
      'video/webm',
      'audio/ogg',
      'video/avi'
    ],
    audio: [
      'video/webm',
      'audio/webm',
      'audio/mp4',
      'audio/ogg',
      'audio/wav',
      'audio/wave',
      'audio/acc',
      'audio/aac',
      'audio/x-aac',
      'audio/m4a',
      'audio/x-hx-aac-adts',
      'audio/x-m4a',
      'audio/x-wav',
      'audio/mpeg',
      'video/mp4'
    ]
  },
  AGENT_INTERVIEW_REPORTS: {
    ASSIGNED_TO_ME: 'ASSIGNED_TO_ME',
    ASSIGNED_FROM_AGENT: 'ASSIGNED_FROM_AGENT',
    ASSIGNED_FROM_SYSTEM: 'ASSIGNED_FROM_SYSTEM',
  },
  customer_evaluation_options: {
    archived: 1,
    create_order: 2
  },
  chat_lists: {
    ACTIVED: 'ACTIVED',
    PASSIVED: 'PASSIVED',
    ARCHIVED: 'ARCHIVED',
    BLOCKED: 'BLOCKED',
    THINKER: 'THINKER',
    HELOBOT: 'HELOBOT'
  },
  chat_actions: {
    moved_to_public: 'moved_to_public',
    system_moved_to_public: 'system_moved_to_public',
    chat_hide: 'chat_hide',
    system_chat_hide: 'system_chat_hide',
    block: 'block',
    unblock: 'unblock',
    assign_to_team: 'assign_to_team',
    assign_to_team_agent: 'assign_to_team_agent',
    assign_to_agent: 'assign_to_agent',
    add_chat_tag: 'add_chat_tag',
    delete_chat_tag: 'delete_chat_tag',
    update_user_profile: 'update_user_profile',
    started_flow: 'started_flow',
    stopped_flow: 'stopped_flow',
    received_chat: 'received_chat',
    owner_forced_chat: 'owner_forced_chat',
    customer_assign_to_team: 'customer_assign_to_team',
    user_profile_customer_note: 'user_profile_customer_note',
    user_profile_email: 'user_profile_email',
    user_profile_first_name: 'user_profile_first_name',
    user_profile_last_name: 'user_profile_last_name',
    user_profile_phone_number: 'user_profile_phone_number',
    add_new_address: 'add_new_address',
    edit_address: 'edit_address',
    delete_address: 'delete_address',
    active_multi_chat_hide: 'active_multi_chat_hide',
    selected_address: 'selected_address',
    selected_cargo: 'selected_cargo',
    selected_payment: 'selected_payment',
    deleted_agent_pending_list: 'deleted_agent_pending_list',
    deleted_agent_archived_list: 'deleted_agent_archived_list',
    noc_moved_to_public: 'noc_moved_to_public',
    campaign_code_applied: 'campaign_code_applied',
    campaign_activated: 'campaign_activated',
    campaign_remove: 'campaign_remove',
    archived_by_system: 'archived_by_system',
    thinker_bot_stopped: 'thinker_bot_stopped',
    pinned: 'pinned',
    remove_archive: "remove_archive",
    mark_as_unread: 'mark_as_unread',
    mark_as_read: 'mark_as_read',
    started_helobot: 'started_helobot',
    stopped_helobot: 'stopped_helobot',
    system_stopped_helobot: 'system_stopped_helobot',
    system_stopped_helobot_error: 'system_stopped_helobot_error',
    connected_user: 'connected_user',
    disconnected_user: 'disconnected_user',
    website_action: 'website_action',
    helobot_timeout: 'helobot_timeout',
    thinker_flow_timeout: 'thinker_flow_timeout',
    timeout_flow_start: 'timeout_flow_start',
  },
  overtimes: {
    OVERTIME_ON: 'OVERTIME_ON',
    OVERTIME_OFF: 'OVERTIME_OFF',
    BREAK_TIME_ON: 'BREAK_TIME_ON',
    BREAK_TIME_OFF: 'BREAK_TIME_OFF',
    NONE: 'NONE'
  },
  platforms: {
    META: 'META'
  },
  user_logs: {
    edit_user: 'edit_user',
    new_user: 'new_user',
    delete_user: 'delete_user',
    create_company: 'create_company',
    status_change_company: 'status_change_company',
    edit_company: 'edit_company',
    create_channel: 'create_channel',
    delete_channel: 'delete_channel',
    edit_channel: 'edit_channel',
    archived_channel_chats: 'archived_channel_chats',
    archived_channel_chats_with_date: 'archived_channel_chats_with_date',
    remove_token_for_facebook: 'remove_token_for_facebook',
    remove_credit_line: 'remove_credit_line',
    add_credit_line: 'add_credit_line',
    add_integration: 'add_integration',
    edit_integration: 'edit_integration',
    login: 'login',
    logout: 'logout',
    message_export: 'message_export',
    channel_status: 'channel_status',
    profile_edit: 'profile_edit',
    add_tag: 'add_tag',
    edit_tag: 'edit_tag',
    thinker_register: 'thinker_register',
    helobot_register: 'helobot_register',
    mail_register: 'mail_register',
    message_template_register: 'message_template_register',
    hepsiburada_register: 'hepsiburada_register',
    trendyol_register: 'trendyol_register',
    pazarama_register: 'pazarama_register',
    n11_register: 'n11_register',
  },
  package_types: {
    ANNUAL: 'ANNUAL',
    MONTHLY: 'MONTHLY'
  },
  shopify_app_purchase_one_time_status: {
    ACTIVE: 'ACTIVE',
    PENDING: 'PENDING'
  },
  whatsapp_business_conversation_types: {
    FREE_TIER: 'FREE_TIER',
    REGULAR: 'REGULAR'
  },
  whatsapp_business_message_types: {
    FREE_CUSTOMER_SERVICE: 'FREE_CUSTOMER_SERVICE',
    FREE_ENTRY_POINT: 'FREE_ENTRY_POINT',
    REGULAR: 'REGULAR'
  },
  shopify_free_package_id: '66f51a5076d935434b457067',
  b2b_free_package_id: '6735b75ce1a0b8d8327efc2e',
  tsoft_free_package_id: '67f90688d6f5af6252cff904',
  message_template_codes: {
    '100': 100, // waba kaynaklı durdurma kodu
    '101': 101, // şablon kaynaklı durdurma kodu
    '102': 102, // normal durdurma kodu
    '103': 103, // kredi bittiği için durdurma
  },
  billtekrom_package_ids: [723, 724, 725, 726, 727, 728, 738, 740],
  billtekrom_sub_packages_ids: [734, 735, 736, 737, 741, 742, 743, 748, 749, 750],
  billtekrom_free_package_id: '67b2e420bd4008e0eeefb585',
  shopify_app_webhook_subscription_topics: ['APP_SUBSCRIPTIONS_UPDATE', 'APP_PURCHASES_ONE_TIME_UPDATE', 'APP_UNINSTALLED'],
  shopify_order_webhook_subscription_topics: ['DRAFT_ORDERS_UPDATE', 'ORDERS_CREATE'],
  shopify_admin_api_default_version: '2025-01',
  order_message_image_url: 'https://helorobo.s3.eu-central-1.amazonaws.com/helorobo-siparis.jpg', //-- 300*100
  basket_message_image_url: 'https://helorobo.s3.eu-central-1.amazonaws.com/helorobo-sepet.jpg',
  basket_summary_message_image_url: 'https://helorobo.s3.eu-central-1.amazonaws.com/helorobo-soru.jpg', //-- 300*100
  cargo_message_image_url: 'https://helorobo.s3.eu-central-1.amazonaws.com/helorobo-kargo.jpg',
  helorobo_not_found: 'https://helorobo.s3.eu-central-1.amazonaws.com/helorobo-not-found.png',
  shopify_received_webhooks: {
    created_order: 'orders/create',
    app_uninstalled: 'app/uninstalled',
    app_subscriptions_update: 'app_subscriptions/update'
  },
  shopify_discount_classes: {
    ORDER: 'ORDER',
    PRODUCT: 'PRODUCT',
    SHIPPING: 'SHIPPING'
  },
  shopify_credit_cart_id: 'gid://shopify/PaymentGateway/123456789101', //rastgele yapıldı
  livechat_base_url: 'https://helorobo.tsoftcdn.com/LiveChat.js',
  livechat_test_base_url: 'https://live-chat-widget.helorobo.net/LiveChat.js',
  client_platforms: {
    MOBILE: 'MOBILE',
    WEB: 'WEB',
    SERVICE: 'SERVICE'
  },
  market_types: {
    HEPSIBURADA: 'HEPSIBURADA',
    N11: 'N11',
    TRENDYOL: 'TRENDYOL',
    PAZARAMA: 'PAZARAMA',
    GENERAL: 'GENERAL'
  },
  helobot_platforms: {
    TSOFT: 'TSOFT',
    OTHER: 'OTHER'
  },
  data_set_events: {
    Purchase: 'Purchase', // Sipariş tamamen tamamlandığında, ödeme alındığında
    LeadSubmitted: 'LeadSubmitted', // Reklam mesajlarından müşteri geldiğinde, Mesaj tipi OPEN_THREAD ve ONLY_ADS_OPEN_THREAD ise
    InitiateCheckout: 'InitiateCheckout', // Adres seçildiğinde
    AddToCart: 'AddToCart', // Sepete ürün eklendiğinde
    OrderCreated: 'OrderCreated', // Sipariş tamamlandığında, yani sipariş onaylandığında
    CartAbandoned: 'CartAbandoned' // Unutulan sepet mesajı müşteriye gittiğinde
  },
  meta_ads_create_child_bm_steps: {
    create_child_bm: 'create_child_bm',
    create_system_user: 'create_system_user',
    get_child_bm_system_user_id: 'get_child_bm_system_user_id',
    share_credit_line: 'share_credit_line',
    get_fund_id: 'get_fund_id',
    create_ad_account: 'create_ad_account',
    assign_cbm_su_to_ad_account: 'assign_cbm_su_to_ad_account'
  }
}

module.exports = enums
