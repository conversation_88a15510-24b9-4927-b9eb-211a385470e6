const fs = require('fs');
const crypto = require('crypto')
const os = require("os");
const { default: axios } = require('axios')
const https = require('https')
const Cryptr = require('cryptr')
const moment = require('moment')
const sleep = require('system-sleep')
const FormData = require('form-data')
const createError = require('http-errors')
const BBTag = require('bbcode-parser/bbTag')
const BBCodeParser = require('bbcode-parser')
const CryptoJS = require('crypto-js')
const { createHmac } = require('crypto')
const pino = require('pino')()
const deepDiff = require('deep-diff')
const mongoose = require('mongoose')

const enums = require('./enums')

const ChatPopulate = require('../dtos/ChatPopulate')
const LogService = require('../services/LogService');

const IntegrationService = require('../modules/AgentApp/IntegrationService')

const Chat = require('../models/Chat')
const TimingLog = require('../models/TimingLog')
const Integration = require('../models/Integration')
const AgentSession = require('../models/AgentSession')
const Message = require('../models/Message')
const Team = require('../models/Team')
const Channel = require('../models/Channel')
const TeamHasChannel = require('../models/TeamHasChannel')
const TeamHasAgent = require('../models/TeamHasAgent')

const helpers = {

  handleError: async (req, err, res) => {

    let bodyData = req.body

    if (req.headers['content-type'] === 'application/json') {
      if (req.originalUrl.includes('auth')) {
        bodyData = {
          data: helpers.encrypt(process.env.APP_SECRET_KEY, '', JSON.stringify(req.body))
        }
      }
    }

    const context = {
      trace_id: req.trace_id,
      url: `${req.protocol}://${req.hostname}:${process.env.APP_PORT}${req.originalUrl}`,
      request: JSON.stringify({
        body: bodyData,
        params: req.params,
        query: req.query
      })
    }

    if (typeof req.getState === 'function') {
      const stateDto = await req.getState()
      if (stateDto) {
        // @TODO: KALDIRLACAK
        if (stateDto.getUser().company_id) {
          context.company_id = stateDto.getCompanyId().toString()
        }
        context.user_id = stateDto.getUserId().toString()
      }
    }

      if(err.isMarketingError){

          pino.error({
              ...context,
              status_code: err.response?.status || 500,
              message: `${err.message}: ${err.detail}`
          })
          return res.status(err.response?.status || 500).json({
              trace_id: req.trace_id,
              message: err.message,
              detail: err.detail
          })

      }

    if (err.isAxiosError) {

      const { message, response } = err
      pino.error({
        ...context,
        status_code: err.response?.status || 500,
        message: JSON.stringify(err.response?.data || { message: 'axios hatası' })
      })

      return res.status(err.response?.status || 500).json({
        trace_id: req.trace_id,
        message: response?.data?.error?.message || response?.data?.message || message
      })

    }

    if (err.error) {
      pino.error({
        ...context,
        status_code: 400,
        message: err.error.message
      })

      return res.status(400).json({
        trace_id: req.trace_id,
        message: err.error.message
      })
    }

    if (err.errors) {
      pino.error({
        ...context,
        status_code: 400,
        message: err.errors[0]?.details
      })

      return res.status(400).json({
        trace_id: req.trace_id,
        message: err.errors[0]?.details
      })
    }

    if (typeof err === 'string') {
      pino.error({
        ...context,
        status_code: 400,
        message: err
      })

      return res.status(400).json({
        trace_id: req.trace_id,
        message: err
      })
    }

    pino.error({
      ...context,
      status_code: 400,
      message: err.message || 'Unexpected Error'
    })
    return res.status(400).json({
      trace_id: req.trace_id,
      message: err.message || 'Unexpected Error'
    })

  },

  decrypt: (secretKey, salt, encryptedPassword) => {

    try {

      const cryptr = new Cryptr(secretKey + salt)

      return cryptr.decrypt(encryptedPassword)

    } catch (err) {

      console.error('\n\nDecrypt yapılamadı error:')
      console.log(err)

      LogService.emergency('decrypt hatası helpers::decrypt', enums.log_channels.BACKEND, {
        secret_key: secretKey,
        username: salt,
        password: encryptedPassword
      })

      throw err

    }

  },

  encrypt: (secret_key, salt, password) => {

    try {

      const cryptr = new Cryptr(secret_key + salt)
      return cryptr.encrypt(password)

    } catch (err) {

      console.error('\n\nEncrypt yapılamadı error:')
      console.log(err)

      LogService.emergency('encrypt hatası helpers::encrypt', enums.log_channels.BACKEND, {
        secret_key: secret_key,
        password: password
      })

      throw err

    }

  },

  bbCodeParser: (content) => {

    let bbTags = {}

    bbTags["b"] = BBTag.createTag("b", function (tag, string, attr) {
      return "  *" + string + "* ";
    })

    bbTags['br'] = BBTag.createTag("br", function (tag, string, attr) {
      return "\n"
    })

    const parser = new BBCodeParser(bbTags);

    return parser.parseString(content)

  },

  handleAxiosError: (error, message) => {

    if (createError.isHttpError(error)) {

      if (typeof error.message === 'object') {
        if (error.message.response?.data?.error?.message) {
          throw new createError(error.message.response?.data?.error?.message)
        }
      }

      throw error
    }

    if (error.response) {

      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx

      // console.log(error.response.data)
      // console.log(error.response.status)
      // console.log(error.response.headers)

      if (message) {
        throw new createError(error.response.status, message)
      }

      throw new createError(error.response.status, error.response.data.message)

    }

    if (error.request) {

      if (message) {
        throw new createError(500, message)
      }

      throw new createError(500, 'İstek yapılamadı.')

    }

    // Something happened in setting up the request that triggered an Error

    if (message) {
      throw new createError(500, message)
    }

    throw new createError(500, error.toString())

  },

  retryProcessOnError: promise => {

    // Promise herseferinde tekrar tekrar then yapıyormu ?
    return new Promise(async (resolve, reject) => {

      while (true) {

        try {

          let result = await promise()

          return resolve(result)

        } catch (err) {
          pino.info({
            message: 'retryProcessOnError',
            timestamp: new Date(),
            error: 'Error. Retrying ... :::' + err.toString()
          })
          sleep(3000)
        }

      }

    })

  },

  /**
   *
   * @param file Multerden gelen file
   * @param config axiosConfigi boş olmaması gerekir içerisinde url method olması önerilir.
   * @param body
   * @return {Object} axios configi dönderiyor
   */
  addFileToAxiosConfigForWhatsApp: (file, config, body) => {

    if (!config) {
      throw new createError.BadRequest('Config verilmedi')
    }

    config.headers['Content-Type'] = `image/jpeg`
    config.data = fs.readFileSync(file.path)

    return config

    // eski kodlar, yukarısını irfan ekledi

    if (!config) {
      throw new createError.BadRequest('Config verilmedi')
    }

    let form = new FormData();
    form.append('avatar', fs.createReadStream(file.path), { filename: file.originalname, contentType: false });

    if (body) {
      form.append('data', JSON.stringify(body))
    }

    config.headers['Content-Type'] = `multipart/form-data;boundary=${form._boundary}`
    config.data = form

    return config

  },

  serial: funcs =>
    funcs.reduce((promise, func) =>
      promise.then(result => func().then(Array.prototype.concat.bind(result))), Promise.resolve([])),

  getHtmlBbCodeParser: () => {

    return new BBCodeParser({
      'BR': BBTag.createTag('BR', (tag, content, attr) => {
        return '<br />';
      }),
      'SPACE': BBTag.createTag('SPACE', (tag, content, attr) => {
        return ''.padEnd(content, ' ')
      }),
      'UNDERLINE': BBTag.createTag('UNDERLINE', (tag, content, attr) => {
        return ''.padEnd(content, '_')
      }),
      'B': BBTag.createTag('B', (tag, content, attr) => {
        return "<b>" + content + "</b>"
      }),
      'S': BBTag.createTag('S', (tag, content, attr) => {
        return "<s>" + content + "</s>"
      }),
      'I': BBTag.createTag('I', (tag, content, attr) => {
        return "<i>" + content + "</i>"
      })
    })

  },

  /**
   *
   * @param {string} fullname
   * @returns {{surname: (string|*), name: (string|*)}}
   */
  parseFullName: (fullname) => {

    let nameArray = fullname.split(' ')

    return {
      name: nameArray.length > 2 ? `${nameArray[0]} ${nameArray[1]}` : nameArray[0],
      surname: nameArray.length === 1 ? '' : nameArray.pop(),
    }

  },

  GetChatPopulate: (req, chatId) => {

    return Chat.findById(chatId).populate('channel_id').then(chat => {

      if (!chat) {
        throw new createError.NotFound(req.t('App.errors.conversation.not_found'))
      }

      if (!chat.channel.is_active) {
        throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
      }

      if (!chat.channel.integration_id) {
        throw new createError.NotFound(req.t('Global.errors.integration.not_found'))
      }

      return Integration.findOne({ _id: chat.channel.integration_id, deleted_at: { $exists: false } }).then(integration => {

        if (!integration) {
          throw new createError.NotFound(req.t('Global.errors.integration.not_found'))
        }

        return IntegrationService.getOrCreateChatIntegration(chat, integration).then(chatIntegration => {

          return new ChatPopulate(chat.channel, chat, integration, chatIntegration)

        })


      })

    })

  },

  isTrue: (item) => {
    return [true, 'true', 1, '1', 'on'].includes(item)
  },

  getName: (user) => {
    let title = user.title

    if (user.first_name) {
      title = user.first_name

      if (user.last_name) {
        title += ' ' + user.last_name
      }
    }

    return title || user.name
  },

  saveStartTimingLog: (chat, action) => {

    const log = new TimingLog()

    log.chat_id = chat ? chat._id : undefined
    log.action = action
    log.started_at = new Date()

    return log.save()

  },

  saveEndTimingLog(log, statusCode) {

    log.ended_at = new Date()
    log.status_code = statusCode
    log.time_diff = parseInt(moment().diff(log.started_at, 'second'))

    return log.save()
  },

  checkReallyWebpImage: (file) => {
    if (file.includes('WEBP')) {
      return true
    }
    return false
  },

  isWebpImage: (url) => {
    const splattedArray = url.split(".")
    const imageType = splattedArray[splattedArray.length - 1]
    return imageType === 'webp';
  },

  getBbCodeProviderParser(content) {

    return new BBCodeParser({
      'BR': BBTag.createTag('BR', (tag, content, attr) => {
        return "\n";
      }),
      'SPACE': BBTag.createTag('SPACE', (tag, content, attr) => {
        return ''.padEnd(content, ' ')
      }),
      'UNDERLINE': BBTag.createTag('UNDERLINE', (tag, content, attr) => {
        return ''.padEnd(content, '_')
      }),
      'B': BBTag.createTag('B', (tag, content, attr) => {
        return "" + content + ""
      }),
      'S': BBTag.createTag('S', (tag, content, attr) => {
        return "" + content + ""
      }),
      'I': BBTag.createTag('I', (tag, content, attr) => {
        return "_" + content + "_"
      })
    }).parseString(content).replace('&amp;', '&')

  },

  getSocketCode: (req, chat, companyId) => {

    return Promise.resolve().then(() => {

      if (chat.owner_user_id) {

        return req.app.models.User.findById(chat.owner_user_id).then(agent => {

          return agent.vSocketCode

        })

      }

      return req.app.models.Company.findById(companyId).then(company => {

        return company.vSocketCode

      })

    })

  },

  upTime: () => {
    const uptime = process.uptime()

    const isFormat = (number) => {
      return (number < 10 ? '0' : '') + number
    }

    let hours = Math.floor(uptime / (60 * 60))
    let minutes = Math.floor(uptime % (60 * 60) / 60)
    let seconds = Math.floor(uptime % 60)

    return isFormat(hours) + ':' + isFormat(minutes) + ':' + isFormat(seconds)
  },

  getCpuUsage: () => {

    const startMeasure = () => {

      const cpus = os.cpus()

      let totalIdle = 0
      let totalTick = 0

      for (let i = 0, len = cpus.length; i < len; i++) {

        const cpu = cpus[i]

        for (type in cpu.times) {
          totalTick += cpu.times[type]
        }

        totalIdle += cpu.times.idle
      }

      return {
        idle: totalIdle / cpus.length,
        total: totalTick / cpus.length
      }
    }
    const { idle, total } = startMeasure()
    return new Promise((resolve, reject) => {

      setTimeout(() => {

        const endMeasure = startMeasure()

        const idleDifference = endMeasure.idle - idle
        const totalDifference = endMeasure.total - total
        const percentageCPU = 100 - ~~(100 * idleDifference / totalDifference)
        resolve(percentageCPU)

      }, 100)

    })

  },


  getCustomerName: name => {

    name = name.split(' ')

    if (name.length === 1) {

      return {
        first_name: name[0],
        last_name: ''
      }

    }

    let first_name = []

    for (let i = 0; i < name.length - 1; i++) {

      first_name.push(name[i])
    }

    first_name = first_name.join(' ')

    const last_name = name[name.length - 1]

    return {
      first_name: first_name || '',
      last_name: last_name || ''
    }

  },

  axiosRequest: config => {

    Object.assign(config, {
      httpsAgent: new https.Agent({
        rejectUnauthorized: false
      })
    })

    return axios.request(config)

  },

  replacedPhoneNumber: (number) => {

    let numberData = number.split(' ')

    const getCc = numberData[0].substring(1, numberData[0].length)

    // numaranın CC si silindi
    numberData.shift()

    return {
      cc: getCc,
      phone_number: numberData.join('')
    }

  },

  encryptPassword: (password, key) => {
    return CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(password), key).toString()
  },

  decryptPassword: (password, key) => {
    return CryptoJS.AES.decrypt(password, key).toString(CryptoJS.enc.Utf8)
  },

  sleepFunction: (time) => {
    return new Promise((res, rej) => {
      setTimeout(() => res(), time)
    })
  },

  checkEmail: (email) => {
    const mailRegex = new RegExp(/^([a-z0-9_\.\-])+\@(([a-z0-9\-])+\.)+([a-z0-9]{2,4})+$/)

    if (!mailRegex.test(email)) {
      return false
    }

    return true
  },

  isButtonMessage: (channelType) => {
    return ![enums.channel_types.WHATSAPP_NUMBER, enums.channel_types.INSTAGRAM_ACCOUNT, enums.channel_types.LIVE_CHAT].includes(channelType)
  },

  getTeamIds: async (channelId) => {
    const teams = await TeamHasChannel.find({ channel_id: channelId }).populate('team_id')
    return teams.filter(a => a.team_id.status && !a.team_id.deleted_at).map(a => a.team_id.id)
  },

  getAgentsTeamsChannels: async (agentId) => {
    const agentTeams = await TeamHasAgent.find({ agent_id: agentId }).populate('team_id')

    let activeTeams = agentTeams.filter(a => a.team_id?.status && !a.team_id?.deleted_at).map(a => a.team_id?._id)

    const channelTeams = await TeamHasChannel.find({ team_id: { $in: activeTeams } }).populate('channel_id')

    const teams = []
    for (const item of channelTeams) {
      if (item.channel?.deleted_at) {
        continue
      }

      if (item.channel?.is_active === false) {
        continue
      }

      const isTeam = agentTeams.find(a => a.team_id?.id === item.team_id.toString())

      if (isTeam) {
        teams.push(isTeam)
      }
    }

    const channels = []
    for (const item of channelTeams.map(a => a?.channel).filter(a => !a?.deleted_at || null)) {
      const isChannel = channels.find(a => a._id.toString() === item._id.toString())
      if (!isChannel) {
        channels.push(item)
      }
    }

    return {
      channels: channels,
      teams: teams.map(a => a.team_id?._id)
    }
  },

  getFirstWelcomeMessage: (req, channelSettings) => {
    let chatText = ''
    if (channelSettings.getIsActiveDefaultWelcomeMessages()) {
      chatText = req.t('App.integration.first_message')
    } else {
      if (channelSettings.getWelcomeMessages()) {

        chatText = channelSettings.getWelcomeMessageContentByLangCode('tr')

        if (req.language === 'en') {
          chatText = channelSettings.getWelcomeMessageContentByLangCode('en')
        }
      }
    }

    return chatText
  },

  //@DEPRECATED
  // getAdsActionMessage: (req, channelSettings) => {
  //   let chatText = ''
  //
  //   if (channelSettings.getAdsActionMessages()) {
  //
  //     chatText = channelSettings.getAdsActionContentByLangCode('tr')
  //
  //     if (req.language === 'en') {
  //       chatText = channelSettings.getAdsActionContentByLangCode('en')
  //     }
  //   }
  //
  //   return chatText
  // },

  getUnarchiveMessage: (req, channelSettings) => {
    let chatText = ''
    if (channelSettings.getIsActiveDefaultUnarchiveMessages()) {
      chatText = req.t('App.integration.first_message')
    } else {
      if (channelSettings.getUnarchiveMessages()) {

        chatText = channelSettings.getUnarchiveMessageContentByLangCode('tr')

        if (req.language === 'en') {
          chatText = channelSettings.getUnarchiveMessageContentByLangCode('en')
        }

        if (req.language === 'fr') {
          chatText = channelSettings.getUnarchiveMessageContentByLangCode('fr')
        }

        if (req.language === 'ar') {
          chatText = channelSettings.getUnarchiveMessageContentByLangCode('ar')
        }
      }
    }

    return chatText
  },

  getArchiveMessage: (req, langCode, channelSettings) => {
    let chatText = ''

    if (channelSettings.getIsActiveSystemDefaultArchiveMessages()) {
      chatText = req.t('App.message.customer_not_replied_message')
    } else {
      if (channelSettings.getUnarchiveMessages()) {
        chatText = channelSettings.getArchiveMessageContentByLangCode(langCode)
      }
    }

    return chatText
  },

  getManuelArchiveMessage: (req, langCode, channelSettings) => {
    let chatText = ''

    if (channelSettings.getIsActiveManuelDefaultArchiveMessages()) {
      chatText = req.t('App.message.customer_not_replied_message')
    } else {
      chatText = channelSettings.getManuelArchiveMessageContentByLangCode(langCode)
    }

    return chatText
  },

  createdHmac: (secretHash, secretId, timestamp) => {
    return createHmac('sha256', `${secretHash}`).update(`hash=${secretId}&timestamp=${timestamp}`).digest('hex')
  },

  getReferralMedia: (data) => {
    if ('image' in data) { // example: {id: 'asdasda'}
      return {
        type: 'image',
        data: data.image
      }
    }
    if ('video' in data) {
      return {
        type: 'video',
        data: data.video
      }
    }
    return undefined
  },

  clearEmptyKeys: (obj) => {
    let clearedObject = {}

    Object.entries(obj).forEach(([key, value]) => {
      if (!value || value === '') {
        return
      }

      if (Array.isArray(value)) {
        if (value.length === 0) {
          return
        }
      }

      clearedObject[key] = value
    })

    return clearedObject
  },

  checkPassword: (req, password) => {
    let errorText = []

    if (!/[A-Z]/.test(password)) {
      errorText.push(req.t('App.errors.user.password_uppercase'))
    }

    if (!/[0-9]/.test(password)) {
      errorText.push(req.t('App.errors.user.password_numeric'))
    }

    if (!/[.,*\-!]/.test(password)) {
      errorText.push(req.t('App.errors.user.password_special'))
    }

    if (password.length < 6) {
      errorText.push(req.t('App.errors.user.password_length'))
    }

    return errorText
  },

  getMediaFileExtention: (contentType) => {
    if (contentType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return 'xlsx'
    }

    if (contentType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return 'docx'
    }

    if (contentType === 'application/vnd.openxmlformats-officedocument.presentationml.presentation') {
      return 'pptx'
    }

    let realExtension = contentType.split('/')[1]

    if (realExtension.includes(';')) {
      realExtension = realExtension.split(';')[0]
    }

    return realExtension
  },

  detectObjectChanges: async (oldObje, newObje) => {
    const differences = deepDiff.diff(oldObje, newObje)
    if (!differences) {
      return
    }

    const logs = differences.map(diff => {
      return {
        key: diff.path.join(),
        old_value: diff.lhs,
        new_value: diff.rhs
      }
    })

    return logs
  },

  packageSettings: (package, isFree = false, allClose = false) => {
    let period = undefined
    if (package.package_type === enums.package_types.ANNUAL) {
      period = moment().add(1, 'years').add(1, 'weeks').toDate()
    } else if (package.package_type === enums.package_types.MONTHLY) {
      period = moment().add(1, 'months').add(1, 'weeks').toDate()
    }

    const modules = [
      'thinker',
      'helobot',
      'trendyol',
      'hepsiburada',
      'mail_service',
      'social_marketing',
      'reports',
      'pazarama',
      'whatsapp',
      'livechat',
      'instagram',
      'facebook',
      'telegram',
      'n11'
    ]

    for (const item of modules) {
      if (package.data[item]) {
        if (item === 'livechat' && isFree === true) {
          package.data[item].status = true
          package.data[item].unlimit = true
        } else {
          if (package.data[item].unlimit === false) {
            if (!allClose) {
              if (isFree) {
                package.data[item].status = true
                package.data[item].expire = moment().add(2, 'weeks').toDate()
              } else {
                if (package.data[item].status) {
                  package.data[item].expire = moment().add(package.data[item].amount, package.data[item].period).toDate()
                }
              }
            } else {
              package.data[item].status = false
            }
          }
        }
      }
    }

    return {
      period: period,
      package: package
    }
  },

  isModuleTimeOut: (module) => {
    if (module.status === true) {
      return false
    }

    if (module.unlimit === true) {
      return false
    }

    if (module.expire) {
      if (!moment().isAfter(moment(module.expire))) {
        return false
      }
    }

    return true
  },

  channelModulesSettings: async (modules, companyId) => {
    const channels = await Channel.find({
      company_id: companyId,
      type: {
        $ne: enums.channel_types.LIVE_CHAT
      },
      deleted_at: {
        $exists: false
      }
    })

    for (const item of channels) {
      let moduleName = ''

      switch (item.type) {
        case enums.channel_types.WHATSAPP_NUMBER:
          moduleName = 'whatsapp'
          break

        case enums.channel_types.FACEBOOK_PAGE:
          moduleName = 'facebook'
          break

        case enums.channel_types.INSTAGRAM_ACCOUNT:
          moduleName = 'instagram'
          break

        case enums.channel_types.TELEGRAM_ACCOUNT:
          moduleName = 'telegram'
          break
      }

      if (!moduleName) {
        continue
      }

      if (modules[moduleName]?.status === true) {
        if (item.is_active === false) {
          if (modules[moduleName].unlimit === true) {
            item.is_active = true
          } else if (modules[moduleName].expire && !moment().isAfter(moment(modules[moduleName].expire))) {
            item.is_active = true
          } else if (modules[moduleName].status === true) {
            item.is_active = true
          } else {
            item.is_active = false
          }
        } else {
          continue
        }
      } else {
        if (item.is_active === true) {
          item.is_active = false
        } else {
          continue
        }
      }

      await item.save()
    }
  },

  createObjectId: (date) => {
    const timestamp = Math.floor(date.getTime() / 1000) // Unix timestamp

    // Zaman damgasını kullanarak ObjectId oluşturma
    return new mongoose.Types.ObjectId(timestamp.toString(16) + "0000000000000000");
  },

  markdownToBBCode: (markdown) => {
    return markdown
      .replace(/\*\*(.*?)\*\*/g, '[B]$1[/B]')   // **bold** -> [b]bold[/b]
      .replace(/__(.*?)__/g, '[B]$1[/B]')       // __bold__ -> [b]bold[/b]
      .replace(/\*(.*?)\*/g, '[I]$1[/I]')       // *italic* -> [i]italic[/i]
      .replace(/_(.*?)_/g, '[I]$1[/I]')         // _italic_ -> [i]italic[/i]
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '[url=$2]$1[/url]')  // [text](url) -> [url=url]text[/url]
      .replace(/\[url=([^\]]+)\](.*?)\[\/url\]/g, '$1 $2')
  },

  removeTurkishCharacters: (text) => {
    return text.normalize("NFD").replace(/[\u0300-\u036f]/g, "")
  },

  checkHmac: (data) => {
    const forHmac = Object.entries(data).map(([key, value]) => {
      if (key != 'hmac') {
        return `${key}=${value}`
      }
      return false
    }).filter(item => item != false).join('&')

    return createHmac('SHA256', process.env.SHOPIFY_SHARED_SECRET_KEY).update(forHmac).digest('hex')
  },

  livechatScriptCode: (channelId, link = false) => {
    let script = `
      var HeloroboChannelId = "${channelId}";
      var supportName = "Müşteri Temsilciniz";
      var widgetSize = "default";
      var widgetPos = { horizontal: "right", vertical: "bottom" };
      var selectedLang = "tr";
      var bottomOffset = "0";
      var horizontalOffset = { right: "20px", left: "0" };
      var hasLoginPage = false;
      var colors = { startPageBackgroundColor: "#0066ff", chatPageBackgroundColor: "#fff", liveSupportTextColor: "#5e6676" };
      var hasFooterAttribution = true;
      var logoUrl = "";
      var iceBreakers = "";
      new LiveChat(HeloroboChannelId, supportName, widgetSize, widgetPos, selectedLang, bottomOffset, hasLoginPage, iceBreakers, colors, hasFooterAttribution, logoUrl, horizontalOffset);
    `

    if (link) {
      script = `
        import '${process.env.PRODUCTION === 'false' ? enums.livechat_test_base_url : enums.livechat_base_url}';
        ${script}
      `
    }

    return script
  },

  getDefaultLiveChatCodeForShopify: (channelId) => {
    let link = enums.livechat_base_url
    if (process.env.PRODUCTION === 'false') {
      link = enums.livechat_test_base_url
    }

    return `
        var script = document.createElement("script");
        script.src = "${link}";
        script.type = 'module';
        script.onload = function () {
          ${helpers.livechatScriptCode(channelId)}
        };
        document.head.appendChild(script);
      `
  },

  shopifyWebhookCheckHmac: (body, shopifyHmac) => {
    const calculatedHmacDigest = crypto.createHmac('sha256', process.env.SHOPIFY_SHARED_SECRET_KEY).update(body).digest('base64')

    return crypto.timingSafeEqual(Buffer.from(calculatedHmacDigest), Buffer.from(shopifyHmac))
  },

  subtractVatFromPrice: (price) => {
    const vat = (Number(process.env.BILLTEKROM_VAT_RATE) / 100) + 1
    return price / vat
  },

  AxiosErrorLog: (traceId, message, data, error) => {
    pino.error({
      timestamp: new Date(),
      trace_id: traceId,
      message: message,
      data: JSON.stringify(data),
      error: JSON.stringify(error?.response?.data || { message: 'İstek Atılamadı' })
    })
  }
}

module.exports = helpers
