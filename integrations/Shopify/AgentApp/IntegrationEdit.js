const createError = require('http-errors')

const enums = require('../../../libs/enums')
const { isTrue } = require('../../../libs/helpers')

module.exports = async (req, integration, userType) => {

  const integrationData = integration.vData

  if (req.method === 'POST') {

    const perpage = req.body.perpage
    const manuelDiscountPermission = req.body.manuel_discount_permission
    const orderCreatedMessage = req.body.order_created_message_settings

    if (!perpage) {
      throw new createError.BadRequest(req.t('App.errors.integration.perpage_not_found'))
    }

    if (parseInt(perpage) > 10 || parseInt(perpage) <= 0) {
      throw new createError.BadRequest(req.t('App.errors.integration.perpage_wrong_value'))
    }


    if (isTrue(orderCreatedMessage.is_active) && !isTrue(orderCreatedMessage.is_default_active)) {

      if (!orderCreatedMessage.messages) {
        throw new createError.BadRequest(req.t('App.errors.integration.welcome_messaging_setting_not_found'))
      }
      if (!orderCreatedMessage.messages.tr) {
        throw new createError.BadRequest(req.t('App.errors.integration.welcome_messaging_setting_tr_not_found'))
      }

    }

    if (userType === enums.acl_roles.COMPANY_OWNER) {
      if ([true, false].includes(manuelDiscountPermission)) {
        integrationData.setManuelDiscountPermissionAll(manuelDiscountPermission)
      }
    }

    integrationData.setOrderCreatedMessages(orderCreatedMessage.messages)
    integrationData.setIsActiveOrderCreatedMessaging(orderCreatedMessage.is_active)
    integrationData.setIsActiveDefaultOrderCreatedMessaging(orderCreatedMessage.is_default_active)

    integrationData.setPerPage(parseInt(perpage))

    integration.data = integrationData.getData()
    integration.markModified('data')

    await integration.save()
    return { success: true }
  }

  const fields = {
    perpage: integrationData.getPerPage(),
    order_created_message_settings: {
      is_active: integrationData.getIsActiveOrderCreatedMessaging(),
      is_default_active: integrationData.getIsActiveDefaultOrderCreatedMessaging() || false,
      messages:
      {
        tr: integrationData.getOrderCreatedMessageContentByLangCode('tr') || req.t('App.success.integration.order_message_manuel', {
          orderId: '{{orderId}}',
          username: '{{username}}',
          orderTotalPrice: '{{orderTotalPrice}}',
          lng: 'tr',
          interpolation: { escapeValue: false }
        }),

        en: integrationData.getOrderCreatedMessageContentByLangCode('en') || req.t('App.success.integration.order_message_manuel', {
          orderId: '{{orderId}}',
          username: '{{username}}',
          orderTotalPrice: '{{orderTotalPrice}}',
          lng: 'en',
          interpolation: { escapeValue: false }
        }),

        fr: integrationData.getOrderCreatedMessageContentByLangCode('fr') || req.t('App.success.integration.order_message_manuel', {
          orderId: '{{orderId}}',
          username: '{{username}}',
          orderTotalPrice: '{{orderTotalPrice}}',
          lng: 'fr',
          interpolation: { escapeValue: false },
        }),

        ar: integrationData.getOrderCreatedMessageContentByLangCode('ar') || req.t('App.success.integration.order_message_manuel', {
          orderId: '{{orderId}}',
          username: '{{username}}',
          orderTotalPrice: '{{orderTotalPrice}}',
          lng: 'ar',
          interpolation: { escapeValue: false }
        }),
        variables: [
          "username",
          "orderTotalPrice",
          "orderId"
        ]
      }
    },
  }

  if (userType === enums.acl_roles.COMPANY_OWNER) {
    fields.manuel_discount_permission = integrationData.getManuelDiscountPermissionAll()
  }

  return { fields: fields }
}