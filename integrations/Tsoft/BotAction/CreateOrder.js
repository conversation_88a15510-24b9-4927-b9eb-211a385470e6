const enums = require('../../../libs/enums')

const QueueService = require('../../../services/QueueService')
const OrderService = require('../../../services/OrderService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')
const TsoftCreateOrderService = require('../../../integrations/Tsoft/AgentApp/CreateOrder')
const CreateOrderForWhatapp = require('./Whatsapp/CreateOrder')
const CreateOrderForLivechat = require('./Livechat/CreateOrder')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateOrderForWhatapp(req, chat, integration, chatIntegration)
  }

  // LIVE_CHAT için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return CreateOrderForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // Sepeti onaylamak istiyorsa alttakilerden birisini seçmesi gerekli
  if (!['1', 'Onayla', 'Approve', 'Approuver', 'يوافق'].includes(botData.customer_message.content.text)) {
    return
  }

  const agent = botData.agent

  // Tsoft tarafında sipariş oluşturuluyor
  const createOrderResponse = await TsoftCreateOrderService(req, integration, chat, chatIntegration, agent.id, chatIntegration.ext_id)

  // Müşteri stage bilgisi güncelleniyor
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_APPROVE_CART)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')
  chatIntegration.has_product_in_cart = false
  await chatIntegration.save()

  // Soket üzerinden sipariş olduşturulduğuna dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_CREATED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      order_code: createOrderResponse.order_code,
    }
  })

  // Soket üzerinden müşteri stage bilgsii değiştiğine dair bilgi gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_APPROVE_CART
    }
  }, req.language)

  OrderService.AfterOrder(
    req,
    chat,
    integration,
    createOrderResponse.price,
    createOrderResponse.currency_code,
    createOrderResponse.order_code,
    createOrderResponse.campaign_ids,
    createOrderResponse.product_count
  )

}