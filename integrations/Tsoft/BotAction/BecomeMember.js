const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')

const SendAddressMessageTsoftForDeliveryAddress = require('../../../integrations/Tsoft/BotAction/SendAddressMessageForDeliveryAddress')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const BecomeMemberForWhatapp = require('./Whatsapp/BecomeMember')
const BecomeMemberForLivechat = require('./Livechat/BecomeMember')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return BecomeMemberForWhatapp(req, chat, integration, chatIntegration)
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return BecomeMemberForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // Müşteri bizim mesajımıza isteidiğimiz gibi cevap vermiş mi kontrol ediyoruz
  if (!['Evet', 'evet', 'Yes', 'yes', 'Onayla', 'Approve', 'Approuver', 'يوافق'].includes(botData.customer_message.content.text)) {
    return
  }

  const agent = botData.agent

  if (!chatIntegration.ext_id) {

    if (!chat.email) {

      const botEmailData = {
        message_type: enums.message_types.TEXT,
        message_data: {
          text: req.t('App.success.ask_form_questions.email'),
          next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED,
          agent_id: agent.id,
          language: req.language,
        }
      }
      // Müşteri maili bizim tarafta kayıtlı değilse müşterinin mailini almamız için mesaj gönderiyoruz
      await ChatService.addAgentMessage(req, chat.id, botEmailData.message_type, botEmailData.message_data, agent.id, undefined, { mark_as_seen_event: true })

      pino.info({
        trace_id: req.trace_id,
        integration_id: integration.id,
        chat_id: chat.id,
        channel_id: chat.channel.id,
        company_id: integration.company_id.toString(),
        bot_message_data: JSON.stringify(botEmailData),
        stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.EMAIL_RECEIVED
      })
      return
    }

  }

  // Müşteri addreslerini alıyoruz.
  const messageData = await SendAddressMessageTsoftForDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // addres bilgisinini olmadığını gösterir. adress yok mesajı gönderilmiştir.
  if (typeof messageData === 'boolean') {
    return
  }

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.success.integration.customer_address_message', {
        interpolation: { escapeValue: false }
      }) + messageData.message_data,
      next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.address_dto,
      bb_code: true,
      hide_image: true,
    }
  }

  // Müşteriye addreslerini gönderiyoruz
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  })

}
