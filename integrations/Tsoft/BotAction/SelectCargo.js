const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const SendPaymentOptionsForTsoft = require('./SendPaymentOptions')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const SelectCargoForWhatapp = require('./Whatsapp/SelectCargo')
const SelectCargoForLivechat = require('./Livechat/SelectCargo')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return SelectCargoForWhatapp(req, chat, integration, chatIntegration)
  }

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return SelectCargoForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  // mesaj içerisinden seçilen kargo bilgisi hangisi diye bakılıyor
  const cargo = botData.bot_data.cargos.find(item => {
    return item.cargo_index == botData.customer_message.vContentText
  })

  if (!cargo) {
    return
  }

  const cargoOptionId = cargo.cargo_option_id

  // müşteri bot üzerinden kargo yöntemini seçti, seçtiği bu bilgileri ilgili kısma kaydedeceğiz
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setCargoOptionId(cargoOptionId)
  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_SELECT_CARGO)

  chatIntegration.data = chatIntegrationData.getData()
  chatIntegration.markModified('data')

  await chatIntegration.save()

  // Soket üzerinden kargo seçildiğine dair mesaj gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CARGO_SELECTED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      cargo_option_id: cargoOptionId, // ek bilgi olarak paylaşıyoruz
    }
  }, req.language)

  // Soket üzerinden müşterinin stage bilgsini gönderiyoruz
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_SELECT_CARGO
    }
  }, req.language)

  // Tsoft tarafından ödeme yöntemleri alınıyor
  const messageData = await SendPaymentOptionsForTsoft(req, chat, integration, chatIntegration, agent.id)

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.success.integration.payment_type', {
        payment_options: messageData.message_data,
        interpolation: { escapeValue: false }
      }),
      bb_code: true,
      next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.payment_dto
    }
  }
  // Ödeme yöntemleri mesaj olarak gönderiliyor.
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_PAYMENT_OPTION
  })
}
