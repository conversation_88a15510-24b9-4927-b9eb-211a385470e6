const pino = require('pino')()

const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const GetBotData = require('../../../../modules/AgentApp/BotAction/GetBotData')

const SendAddressMessageTsoftForDeliveryAddress = require('../../../../integrations/Tsoft/BotAction/SendAddressMessageForDeliveryAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  if (req.t('Global.chat_message.confirm_cart') !== botData.customer_message.content.text) {
    return
  }

  const agent = botData.agent

  // Müşteri Üyeliksiz ise kredi kartı ile ödeme yapamayacağına dair mesaj gönderiliyor
  if (!chatIntegration.ext_id && integration.vData.getCreditCardRequirementForNonMembers()) {

    const botMessageBecomeMember = {
      message_type: enums.message_types.INSTAGRAM_GENERIC_BUTTON,
      message_data: {
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.BECOME_MEMBER,
        bb_code: true,
        title: req.t('Global.chat_message.confirm'),
        subtitle: req.t('App.success.ask_form_questions.whatsapp_become_member'),
        buttons: [
          {
            type: "postback",
            title: req.t('Global.chat_message.confirm'),
            payload: req.t('Global.chat_message.confirm')
          }
        ],
        agent_id: agent.id,
        language: req.language,
      }
    }

    await ChatService.addAgentMessage(req, chat.id, botMessageBecomeMember.message_type, botMessageBecomeMember.message_data, agent.id, undefined, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      company_id: integration.company_id.toString(),
      bot_message_data: JSON.stringify(botMessageBecomeMember),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.BECOME_MEMBER
    })
    return
  }

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  const messageData = await SendAddressMessageTsoftForDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // addres bilgisinini olmadığını gösterir. adress yok mesajı gönderilmiştir.
  if (typeof messageData === 'boolean') {
    return
  }

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.success.integration.customer_address_message', {
        interpolation: { escapeValue: false }
      }) + messageData.message_data,
      next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.address_dto,
      bb_code: true,
      hide_image: true,
    }
  }

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  })
}