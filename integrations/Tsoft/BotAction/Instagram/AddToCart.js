const pino = require('pino')()

const enums = require('./../../../../libs/enums')
const utils = require('./../../../../libs/utils')

const AddToCartDto = require('../../../../dtos/AddToCartDto')
const BotActionAddToCartDto = require('../../../../dtos/BotAction/AddToCartDto')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const TsoftAgentAppService = require('../../../../integrations/Tsoft/AgentApp/TsoftService')

const GetBotData = require('../../../../modules/AgentApp/BotAction/GetBotData')

const MultiProductsShare = require('../../../../modules/AgentApp/MultiProductsShare')

const isAvailable = (value, getProductResponseItem) => {

  let result = false

  getProductResponseItem.variant_data.variants.forEach(variant => {

    if (variant.option1 === value && variant.stock_count > 0) {

      result = variant

    }

  })

  return result

}

const getAvailableVariants = getProductResponseItem => {

  const items = []
  let index = 1

  getProductResponseItem.variant_data.options[0].values.forEach(value => {

    const isAvailableVariant = isAvailable(value, getProductResponseItem)

    if (isAvailableVariant !== false) {

      items.push({
        index: index++,
        name: value,
        price: isAvailableVariant.price,
        variant_id: isAvailableVariant.variant_id
      })

    }

  })

  return items

}

const getVariantItemsMessage = (chat, getProductResponseItem, integrationData) => {

  const items = []

  getAvailableVariants(getProductResponseItem).forEach(variant => {

    const variantName = utils.getMessageEmoji(variant.index, chat.channel.type) + ' [B]' + variant.name + '[/B]'

    if (getProductResponseItem.variant_count === 1) {

      if (variant.price > 0) {
        items.push(variantName + '(' + utils.getCurrencyForIntlNumberFormat(variant.price, getProductResponseItem.currency_code, integrationData.getPricePrecision()) + ')')
      } else {
        items.push(variantName + '(' + utils.getCurrencyForIntlNumberFormat(getProductResponseItem.sell_price, getProductResponseItem.currency_code, integrationData.getPricePrecision()) + ')')
      }

    } else {
      items.push(variantName)
    }

  })

  return items.join('[BR][/BR]')

}

const getVariantTitle = item => {

  return '[B]' + item.variant_data.options[0].name + '[/B]'

}

module.exports = async (req, chat, integration, chatIntegration, data) => {

  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  const agent = botData.agent

  const customerSelectedProduct = botData.customer_message.vContent.text

  // Çoklu Ürün Paylaşımı var mı kontrolü yapılır.
  if (botData.agent_message.vBotData.getMultiShare()) {
    return MultiProductsShare(req, chat, integration, chatIntegration, botData.agent_message, agent)
  }

  // Ürün mesajı bulunuyor
  const productShareMessage = botData.messages.find(message => {

    if (message.from_type !== enums.message_from_types.AGENT) {
      return false
    }

    if (message.content && message.content.bot_data && message.content.bot_data.product_share_counter) {

      const selectedButton = message.vContent.buttons.find(a => a.title === customerSelectedProduct)

      if (selectedButton) {
        if (message.content.bot_data.product_share_counter.toString() === selectedButton.payload) {
          return true
        }
      }
      return false
    }

    return false

  })

  if (!productShareMessage) {
    return false
  }

  if (!productShareMessage.content.bot_data) {
    return false
  }

  let messageData

  // Tsoft tarafından ürün bilgileri alınıyor.
  const getProductResponse = await TsoftAgentAppService.getTsoftProduct(req, integration, chatIntegration, {
    product_id: productShareMessage.content.bot_data.product_id,
    fetch_product_detail: true
  }, chat.vData.getChatLangCode())

  if (!getProductResponse) {
    return
  }

  // Ürün Variantı birden fazla ise variant bilgileri kullanıcıya gönderilir
  if (getProductResponse.item.variant_count > 0) {

    const botActionAddToCartDto = new BotActionAddToCartDto()
    botActionAddToCartDto.setBotData({
      product_id: productShareMessage.content.bot_data.product_id,
      variants: getAvailableVariants(getProductResponse.item)
    })

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart_variant', {
          variant: getVariantItemsMessage(chat, getProductResponse.item, integration.vData),
          variant_title: getVariantTitle(getProductResponse.item),
          interpolation: { escapeValue: false }
        }),
        language: data.language,
        agent_id: agent._id,
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_VARIANT1,
        bb_code: true,
        bot_data: botActionAddToCartDto.getBotData()
      },
      send_data_from_socket: false
    }

  } else {

    const addToCartDto = new AddToCartDto()

    addToCartDto.setChat(chat)
    addToCartDto.setIntegration(integration)
    addToCartDto.setData({ product_id: productShareMessage.content.bot_data.product_id })
    addToCartDto.setExtId(chatIntegration.ext_id)
    addToCartDto.setItemName(getProductResponse.item.title)
    addToCartDto.setAgentId(agent.id)
    addToCartDto.setChatIntegration(chatIntegration)

    // Tsoft tarafında sepete ekleme işlemi yapılıyor
    const response = await TsoftAgentAppService.addToCart(req, addToCartDto, chat.vData.getChatLangCode())

    // Müşteriye gönderilecek mesajın datası
    messageData = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.integration.add_to_cart', {
          item_name: response.item_name,
          interpolation: { escapeValue: false },
        })
      },
      send_data_from_socket: true
    }

  }

  //sepette ürün olduğu bilgisi chat integration'a kaydediliyor.
  chatIntegration.has_product_in_cart = true
  await chatIntegration.save()

  // Kullanıcıya sepetine ürün eklendiğine dair mesaj gönderildi
  await ChatService.addAgentMessage(req, chat.id, messageData.message_type, messageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  // Ürün eğer tek variantlık ise sepete eklendi işlemlerinin yapılması için soketten bilgiler gönderliyor.
  if (messageData.send_data_from_socket) {

    // Müşterinin Stage bilgisi güncelleniyor
    const chatIntegrationData = chatIntegration.vData

    chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

    chatIntegration.data = chatIntegrationData.getData()
    chatIntegration.markModified('data')

    await chatIntegration.save()

    // Soket üzerinden agent tarafına sepete ürün eklendiğine dair bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        item_name: getProductResponse.item.title,
      }
    }, req.language)

    // Soket üzerinden agenta müşterinin stage bilgisi değiştiğine dair bilgi gönderiliyor
    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
      socket_rooms: [agent.vSocketCode],
      data: {
        chat_id: chat.id,
        integration_id: integration.id,
        stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
      }
    }, req.language)
  }

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    bot_message_data: JSON.stringify(messageData)
  })

}
