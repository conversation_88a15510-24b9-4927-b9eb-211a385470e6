const pino = require('pino')()

const enums = require('../../../libs/enums')

const ChatService = require('../../../services/ChatService')
const QueueService = require('../../../services/QueueService')

const GetBotData = require('../../../modules/AgentApp/BotAction/GetBotData')

const CreateCartForWhatapp = require('./Whatsapp/CreateCart')
const CreateCartForInstagram = require('./Instagram/CreateCart')
const CreateCartForLivechat = require('./Livechat/CreateCart')

const SendAddressMessageTsoftForDeliveryAddress = require('../../../integrations/Tsoft/BotAction/SendAddressMessageForDeliveryAddress')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Whatsapp için burası kullanılacak
  if (chat.channel.type === enums.channel_types.WHATSAPP_NUMBER) {
    return CreateCartForWhatapp(req, chat, integration, chatIntegration)
  }
  // Instagram için burası kullanılacak
  if (chat.channel.type === enums.channel_types.INSTAGRAM_ACCOUNT) {
    return CreateCartForInstagram(req, chat, integration, chatIntegration)
  }

  // LIVE_CHAT için burası kullanılacak
  if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
    return CreateCartForLivechat(req, chat, integration, chatIntegration)
  }

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetBotData(chat, 2)

  if (!botData) {
    return
  }

  // Sepeti onaylamak istiyorsa 1 e basması gerekmekte
  if (parseInt(botData.customer_message.content.text) !== 1) {
    return
  }

  const agent = botData.agent

  // Müşteri Üyeliksiz ise kredi kartı ile ödeme yapamayacağına dair mesaj gönderiliyor
  if (!chatIntegration.ext_id && integration.vData.getCreditCardRequirementForNonMembers()) {

    const botMessageBecomeMember = {
      message_type: enums.message_types.TEXT,
      message_data: {
        text: req.t('App.success.ask_form_questions.become_member'),
        next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.BECOME_MEMBER,
        bb_code: true,
        agent_id: agent.id,
        language: req.language,
      }
    }

    await ChatService.addAgentMessage(req, chat.id, botMessageBecomeMember.message_type, botMessageBecomeMember.message_data, agent.id, undefined, { mark_as_seen_event: true })

    pino.info({
      trace_id: req.trace_id,
      integration_id: integration.id,
      chat_id: chat.id,
      channel_id: chat.channel.id,
      company_id: integration.company_id.toString(),
      bot_message_data: JSON.stringify(botMessageBecomeMember),
      stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.BECOME_MEMBER
    })
    return
  }

  // Soket üzerinden Kart oluşturuldu bilgisi gönderiliyor.
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATED_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
    }
  }, req.language)

  const messageData = await SendAddressMessageTsoftForDeliveryAddress(req, chat, integration, chatIntegration, chatIntegration.ext_id)

  // addres bilgisinini olmadığını gösterir. adress yok mesajı gönderilmiştir.
  if (typeof messageData === 'boolean') {
    return
  }

  const botMessageData = {
    message_type: enums.message_types.TEXT,
    message_data: {
      text: req.t('App.success.integration.customer_address_message', {
        interpolation: { escapeValue: false }
      }) + messageData.message_data,
      next_action: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS,
      agent_id: agent.id,
      language: req.language,
      bot_data: messageData.address_dto,
      bb_code: true,
      hide_image: true,
    }
  }

  // Müşteriye addreslerini seçmesi için bot mesajı gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, botMessageData.message_type, botMessageData.message_data, agent.id, undefined, { mark_as_seen_event: true })

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(botMessageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_ADDRESS
  })
}