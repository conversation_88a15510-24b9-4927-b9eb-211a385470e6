const pino = require('pino')()

const enums = require('../../../../libs/enums')

const ChatService = require('../../../../services/ChatService')
const QueueService = require('../../../../services/QueueService')

const TsoftAgentAppService = require('../../../../integrations/Tsoft/AgentApp/TsoftService')

const AddToCartDto = require('../../../../dtos/AddToCartDto')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')


module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if (!botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.TSOFT_BOT_MESSAGE_ACTIONS.SELECT_VARIANT2) {
    return
  }

  const agent = botData.agent

  // BotData içerisinden product_id ve variant_id bilgileri alındı
  const productId = botData.agent_message.vContent.bot_data.product_id
  const variantId = botData.customer_message.vContent.interactive.list_reply.id

  // ID bilgisine göre ürün tsoft tarafından alınıyor.
  const getProductResponse = await TsoftAgentAppService.getTsoftProduct(req, integration, chatIntegration, {
    product_id: productId,
    fetch_product_detail: true
  }, chat.vData.getChatLangCode())

  const addToCartDto = new AddToCartDto()

  addToCartDto.setChat(chat)
  addToCartDto.setIntegration(integration)
  addToCartDto.setData({
    product_id: productId,
    variant_cid: variantId
  })
  addToCartDto.setExtId(chatIntegration.ext_id)
  // addToCartDto.setItemName(getItemName(getProductResponse.item, getVariantData, botData.customer_message))
  addToCartDto.setAgentId(agent.id)
  addToCartDto.setChatIntegration(chatIntegration)

  // Üyelikli veya üyeliksiz olarak tsofta göre sepete ürün ekleme işlemi yapılıyor
  const messageData = await TsoftAgentAppService.addToCart(req, addToCartDto, chat.vData.getChatLangCode())

  // Müşteriye sepetine ürün eklendiğine dair mesaj gönderiliyor
  await ChatService.addAgentMessage(req, chat.id, enums.message_types.TEXT, {
    text: req.t('App.success.integration.add_to_cart', {
      item_name: messageData.item_name,
      interpolation: { escapeValue: false },
    })
  }, agent.id, undefined, { mark_as_seen_event: true })

  // Müşteri stage bilgisi güncelleniyor.
  const chatIntegrationData = chatIntegration.vData

  chatIntegrationData.setStage(enums.ORDER_STAGES.STAGE_ADD_TO_CART)

  chatIntegration.user_data = chatIntegrationData.getData()
  chatIntegration.markModified('chat_integration')

  await chatIntegration.save()

  // Soket üzerinden sepete ürün eklendiğine dair mesaj iletiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ITEM_ADDED_TO_CART,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      item_name: getProductResponse.item.title,
    }
  }, req.language)

  // Müşteri bilgisi soket üzerinden gönderiliyor
  await QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.ORDER_STAGE_CHANGED,
    socket_rooms: [agent.vSocketCode],
    data: {
      chat_id: chat.id,
      integration_id: integration.id,
      stage: enums.ORDER_STAGES.STAGE_ADD_TO_CART
    }
  }, req.language)

  pino.info({
    trace_id: req.trace_id,
    integration_id: integration.id,
    chat_id: chat.id,
    channel_id: chat.channel.id,
    company_id: integration.company_id.toString(),
    bot_message_data: JSON.stringify(messageData),
    stage: enums.TSOFT_BOT_MESSAGE_ACTIONS.STAGE_ADD_TO_CART
  })
}
