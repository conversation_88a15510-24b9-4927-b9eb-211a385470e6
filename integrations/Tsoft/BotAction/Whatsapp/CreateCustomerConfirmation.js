const enums = require('../../../../libs/enums')

const QueueService = require('../../../../services/QueueService')

const GetInteractiveData = require('../../../../modules/AgentApp/BotAction/GetInteractiveData')

module.exports = async (req, chat, integration, chatIntegration) => {

  // Mesajlar içerisinden gerekli olan bot bilgileri alınıyor
  const botData = await GetInteractiveData(chat)

  if ( ! botData) {
    return
  }

  if (botData.agent_message.vContent.next_action !== enums.TSOFT_BOT_MESSAGE_ACTIONS.CREATE_CUSTOMER_CONFIRMATION) {
    return
  }

  // Agentın gönderdiği mesajda buton id si ile müşterinin butona bastığı id ile aynı mı kontrol ediliyor
  const selectedButton = botData.agent_message.vContent.buttons.find(item => item.reply.id === botData.customer_message.vContent.interactive[`${botData.message_type}`].id)

  if ( ! selectedButton) {
    return
  }

  // Soket üzerinden müşteri üyeliği yapılabilir diye bilgi gönderiliyor
  QueueService.publishToAppSocket({
    event: enums.agent_app_socket_events.CREATE_CUSTOMER_CONFIRMED,
    socket_rooms: [botData.agent.vSocketCode],
    data: {
      integration_id: integration.id,
      chat_id: chat.id
    }
  }, req.language)

  chatIntegration.data.create_customer_confirmed = true

  chatIntegration.markModified('data')

  return chatIntegration.save()

}
