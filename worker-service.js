require('dotenv').config()

const pino = require('pino')()
const mongoose = require('mongoose')
const amqp = require('amqp-connection-manager')

const cors = require('cors')
const nocache = require('nocache')
const express = require('express')

const workerService = require('./src/WorkerService/Run')

const helpers = require('./libs/helpers')

const RedisConnection = require('./modules/RedisConnection')
const RabbitMQConnection = require('./modules/RabbitMQConnection')

const ElasticsearchService = require('./services/ElasticsearchService')

let channelWrapper

const main = async () => {

  await helpers.retryProcessOnError(() => mongoose.connect(process.env.MONGODB_CONN_STRING))

  await RabbitMQConnection.connection()

  await RedisConnection.connection()

  // Create a connetion manager
  const connection = amqp.connect([process.env.RABBITMQ_CONN_STRING])

  connection.on('connect', function () {
    pino.info({
      message: 'worker-service rabbitmq connected!',
      timestamp: new Date()
    })
  })

  connection.on('disconnect', function (err) {
    pino.error({
      message: 'worker-service rabbitmq disconnected.',
      timestamp: new Date(),
      error: err.message
    })
  })

  channelWrapper = connection.createChannel()

  // worker queue setup
  channelWrapper.addSetup(function (channel) {

    return Promise.all([
      channel.assertQueue(process.env.RABBITMQ_WORKER_QUEUENAME),
      channel.prefetch(10),
      channel.consume(process.env.RABBITMQ_WORKER_QUEUENAME, onMessage)
    ])

  })

  // fifo worker quorum queue setup
  channelWrapper.addSetup(function (channel) {

    return Promise.all([
      channel.assertQueue(process.env.RABBITMQ_WORKER_FIFO_QUEUENAME, {
        arguments: {
          'x-queue-type': 'quorum'
        }
      }),
      channel.prefetch(50),
      channel.consume(process.env.RABBITMQ_WORKER_FIFO_QUEUENAME, onMessage)
    ])

  })
  // fifo worker message log queue setup
  channelWrapper.addSetup(function (channel) {

    return Promise.all([
      channel.assertQueue(process.env.RABBITMQ_WORKER_FIFO_MESSAGE_LOG_QUEUENAME, {
        arguments: {
          'x-queue-type': 'quorum'
        }
      }),
      channel.prefetch(2),
      channel.consume(process.env.RABBITMQ_WORKER_FIFO_MESSAGE_LOG_QUEUENAME, onMessageLog)
    ])

  })

  // fifo worker chat queue setup
  channelWrapper.addSetup(function (channel) {

    return Promise.all([
      channel.assertQueue(process.env.RABBITMQ_WORKER_FIFO_CHAT_QUEUENAME, {
        arguments: {
          'x-queue-type': 'quorum'
        }
      }),
      channel.prefetch(1),
      channel.consume(process.env.RABBITMQ_WORKER_FIFO_CHAT_QUEUENAME, onChat)
    ])

  })

  //Marketing fifo queue setup
  channelWrapper.addSetup(function (channel) {

    return Promise.all([
      channel.assertQueue(process.env.RABBITMQ_WORKER_MARKETING, {
        arguments: {
          'x-queue-type': 'quorum'
        }
      }),
      channel.prefetch(30),
      channel.consume(process.env.RABBITMQ_WORKER_MARKETING, onMessage)
    ])

  })

  await channelWrapper.waitForConnect()

  CreateHttpServer()
}

const onMessage = async (msg) => {
  const message = JSON.parse(msg.content.toString())

  try {
    await workerService(message)

    channelWrapper.ack(msg)
  } catch (error) {
    pino.error({
      message: 'kuyruktaki mesaj ack yapılamadı',
      timestamp: new Date(),
      error: JSON.stringify(typeof error === 'object' ? error : { message: error }),
      queue_data: JSON.stringify(message)
    })

    channelWrapper.nack(msg)
  }
}

const onMessageLog = async (msg) => {
  if (process.env.PRODUCTION === 'false') {
    return channelWrapper.ack(msg)
  }

  const message = JSON.parse(msg.content.toString())

  try {
    await ElasticsearchService.SendLogMessage(message)

    channelWrapper.ack(msg)
  } catch (error) {
    pino.error({
      message: 'mesaj log kuyruğundaki mesaj ack yapılamadı',
      timestamp: new Date(),
      error: JSON.stringify(typeof error === 'object' ? error : { message: error }),
      queue_data: JSON.stringify(message)
    })

    channelWrapper.nack(msg, false, true)
  }
}

const onChat = async (msg) => {
  if (process.env.PRODUCTION === 'false') {
    return channelWrapper.ack(msg)
  }

  const message = JSON.parse(msg.content.toString())
  const data = message.data

  try {
    if (message.create) {
      await ElasticsearchService.addChat(data)
    } else if (message.update) {
      const chatData = await ElasticsearchService.getChat(message.id)
      if (chatData.length === 1) {
        await ElasticsearchService.updateChat(chatData[0]._id, data)
      } else if (chatData.length === 0) {
        await ElasticsearchService.addChat({
          ...data,
          id: message.id
        })
      }
    }

    channelWrapper.ack(msg)
  } catch (error) {
    pino.error({
      message: 'chat kuyruğundaki mesaj ack yapılamadı',
      timestamp: new Date(),
      error: JSON.stringify(typeof error === 'object' ? error : { message: error }),
      queue_data: JSON.stringify(message)
    })

    channelWrapper.nack(msg, false, true)
  }
}

const CreateHttpServer = () => {
  const app = express()

  app.use(cors())
  app.use(express.urlencoded({ extended: true }))
  app.use(express.json())
  app.use(nocache())

  app.get('/health', (req, res, next) => {
    return res.status(200).json({ success: true })
  })

  const port = 3021
  app.listen(port, () => pino.info(`worker-service listening on port ${port}!`))
}

main()
