PRODUCTION = false

APP_CLUSTER_FORK_COUNT = 4

APP_PORT = replace_app_port

APP_SECRET_KEY = replace_app_secret_key

NOC_TOKEN = replace_noc_token

BASE_URL = replace_base_url

APP_BASE_URL = replace_app_base_url
SITE_BASE_URL = replace_site_base_url

RECAPTCHA_SECRET_KEY = replace_recaptcha_secret_key

MONGODB_CONN_STRING = ******************************************************************:replace_mongo_port/replace_mongo_db

INTEGRATION_BASE_URL = replace_integration_base_url

RABBITMQ_CONN_STRING = amqp://replace_rabbitmq_user:replace_rabbitmq_pass@replace_rabbitmq_host:replace_rabbit_port/

RABBITMQ_SOCKET_QUEUENAME = replace_socket_queuename

RABBITMQ_WORKER_QUEUENAME = replace_worker_queuename

RABBITMQ_WORKER_FIFO_QUEUENAME = replace_fifo_queuename

RABBITMQ_LIVE_CHAT_SOCKET_QUEUENAME = replace_live_chat_queuename

RABBITMQ_WORKER_MARKETING = replace_marketing_queuename

RABBITMQ_WORKER_FIFO_MESSAGE_LOG_QUEUENAME = replace_fifo_message_log_queuename
RABBITMQ_WORKER_FIFO_CHAT_QUEUENAME = replace_fifo_chat_queuename

STATIC_FTP_HOST = replace_static_ftp_host
STATIC_FTP_PORT = replace_static_ftp_port
STATIC_FTP_USER = replace_static_ftp_user
STATIC_FTP_PASS = replace_static_ftp_pass
STATIC_FTP_PATH = replace_static_ftp_path
STATIC_FTP_LOG_PATH = replace_static_log_path

LOCAL_LOG_PATH = replace_local_log_path

STATIC_BASE_URL = replace_static_base_url

WEB_PUSH_PUBLIC_KEY = replace_web_push_public_key
WEB_PUSH_PRIVATE_KEY = replace_web_push_pravite_key

SYSTEM_USER_ID = replace_system_user_id

HELOROBO_APP_ID = replace_helorobo_app_id
HELOROBO_APP_SECRET = replace_helorobo_app_secret

WHATSAPP_NOC_PASSWORD = replace_whatsapp_noc_pass
HELOSCOPE_NOC_PASSWORD = replace_heloscope_noc_pass

FACEBOOK_BSP_ACCESS_TOKEN = replace_facebook_bsp_access_token

GET_CONTAINER_KEY = replace_get_container_key

ADVERMIND_URL = replace_adverminds_url
ADVERMINDS_SESSION_KEY = replace_adverminds_session_key

SENDHEAP_SECRET_KEY = replace_sendheap_secret_key

TSOFT_SECRET_KEY = replace_tsoft_secret_key

SHOPIFY_SHARED_SECRET_KEY = replace_shopify_shared_secret_key
SHOPIFY_CLIENT_ID = replace_shopify_client_id

ARVIA_EMAIL = replace_arvia_email
ARVIA_PASSWORD = replace_arvia_password
ARVIA_URL = replace_arvia_url

TSOFT_APP_TOKEN = replace_tsoft_app_token
TSOFT_BSP_ACCESS_TOKEN = replace_tsoft_bsp_access_token

NILDESK_BASE_URL = replace_nildesk_base_url
NILDESK_API_KEY = replace_nildesk_api_key
NILDESK_SECRET_KEY = replace_nildesk_secret_key

GOOGLE_CLIENT_ID = replace_google_client_id
GOOGLE_CLIENT_SECRET = replace_google_client_secret

CONTAINER_BASE_URL = replace_container_base_url

URL_SHORTER_URL = replace_url_shorter_url

TSOFT_BUSINESS_ID = replace_tsoft_business_id
PARENT_BM_LINE_OF_CREDIT_ID = replace_parent_bm_line_of_credit_id
TSOFT_APP_ID = replace_tsoft_app_id
TSOFT_APP_SECRET_KEY = replace_tsoft_app_secret_key
SHOPIFY_AUTH_KEY = replace_shopify_auth_key
FACEBOOK_APP_ACCESS_TOKEN = replace_facebook_app_access_token
WEBHOOK_URL = replace_webhook_url

SOCKET_URL = replace_socket_url
BACKEND_SOCKET_TOKEN = replace_backend_socket_token

MESSAGE_TEMPLATE_URL = replace_message_template_url
MESSAGE_TEMPLATE_V3_URL = replace_message_template_v3_url
MESSAGE_TEMPLATE_TOKEN = replace_message_template_token

FFMPEG_SERVICE_URL = https://ffmpeg.helorobo.com/api/v1/convert
UPLOAD_SERVICE_URL = https://api.helorobo.com/api

THINKER_API_BASE_URL = https://api.helorobo.com/api/v1/thinker
THINKER_WEBHOOK_BASE_URL = https://api.helorobo.com/api/v1/thinker
THINKER_BASE_URL = https://thinker.helorobo.com

THINKER_WEBHOOK_VERIFY_TOKEN = replace_webhook_verify_token

ELASTICSEARCH_CONN_STRING = replace_elasticsearch_host:replace_elasticsearch_port
ELASTICSEARCH_USERNAME = elasticsearch_username
ELASTICSEARCH_PASSWORD = elasticsearch_password

FIREBASE_API_KEY = firebase_api_key
FIREBASE_AUTH_DOMAIN = firebase_auth_domain
FIREBASE_DATABASE_URL = firebase_database_url
FIREBASE_PROJECT_ID = firebase_project_id
FIREBASE_STORAGE_BUCKET = firebase_storage_bucket
FIREBASE_MESSAGING_SENDER_ID = firebase_messaging_sender_id
FIREBASE_APP_ID = firebase_app_id
FIREBASE_MEASUREMENT_ID = firebase_measurement_id

TELEGRAM_BASE_URL = http://localhost:3058

BILL_TEKROM_TOKEN = 
BILL_TEKROM_BASE_URL = https://helorobo-bill-api.tekrom.com

BILL_TEKROM_API_USER = 
BILL_TEKROM_API_USER_PASSWORD = 

HELOROBO_BSP_ACCESS_TOKEN = helorobo_bsp_access_token

FACEBOOK_GRAPH_API_VERSION = v18.0

TEKROM_CREDIT_LINE_ID = 

HELOROBO_SYSTEM_USER_ID = 
HELOROBO_SYSTEM_USER_TOKEN = 

REDIS_HOST = 
REDIS_PORT =
REDIS_PASSWORD =

MESSAGE_IMPORT_BASE_URL = http://localhost:3446

HEPSIBURADA_API_BASE_URL = https://api-asktoseller-merchant.hepsiburada.com
HEPSIBURADA_USER_AGENT = 

HELOBOT_BASE_URL = https://api.tsoftchatai.com
HELOBOT_REGISTER_TOKEN = 

GOOGLE_SERVICES_EMAIL = 
GOOGLE_SERVICES_PRIVATE_KEY = 

REPORT_SERVICE_BASE_URL = http://localhost:4646

META_PROXY_REQUEST_STATUS = false
META_PROXY_REQUEST_URL =

FIREBASE_CLIENT_EMAIL = 
FIREBASE_PRIVATE_KEY = 

MAIL_SERVICE_BASE_URL = http://localhost:3054

PAZARAMA_GIRIS_BASE_URL = https://isortagimgiris.pazarama.com
PAZARAMA_API_BASE_URL = https://isortagimapi.pazarama.com

GOOGLE_2FA_NAME = HeloRobo App

N11_API_URL = https://api.n11.com

BILLTEKROM_VAT_RATE = 20

TRENDYOL_API_BASE_URL = https://apigw.trendyol.com