{"Global": {"chat_message": {"add": "<PERSON><PERSON>", "add_cart": "Sepete Ekle", "select": "Seç", "options": "Seçenekler", "select_address": "Address Seçiniz", "select_cargo": "<PERSON><PERSON>", "select_payment": "<PERSON><PERSON><PERSON>", "select_campaign": "Kampanya <PERSON>ç<PERSON>z", "confirm": "<PERSON><PERSON><PERSON>", "confirm_cart": "<PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "empty_basket": "Süresi Geçmiş veya Tamamlanmış Bir Sipariş ile İlgili Geriye Dönük İşlem Yapamazsınız.", "no_exist_product": "Gösterilecek Ürün Bulunamadı", "order_status": "Sipariş Durumu", "cart": "Sepete eklemek için on<PERSON>ın", "select_account": "<PERSON><PERSON><PERSON>", "confirm_order": "Siparişi <PERSON>ı<PERSON>", "thanks": "Teşekkürler", "hello": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"two_fa_code_not_found": "2FA kod Bulunamadı. Lütfen 2FA kod giriniz.", "two_fa_code_invalid": "2FA kod Geçersiz. Lütfen Geçerli Bir 2FA kod giriniz.", "two_fa_code_string": "2FA kod Geçersiz Tipte. Lütfen 6 haneli 2FA kod giriniz.", "two_fa_not_found": "twoFA_status bilgisi bulunamadı.", "two_fa_allready_open": "2FA Zaten Açık.", "two_fa_allready_close": "2FA Zaten Kapalı.", "token_required": "Bu işlem için token bilgisi gerekmektedir.", "token_not_found": "Token detay elde edilemedi.", "token_deleted": "<PERSON><PERSON>.", "token_hash_not_some": "Token hash aynı değil.", "token_expired": "Token kullanım zamanı geçmiş.", "page_not_found": "Sayfa bulunamadı.", "not_authorized": "<PERSON>u i<PERSON><PERSON>i yapma yetkiniz bulunmamaktadır.", "package_not_authorized": "Kullandığınız Pakette Bu işlemi yapamazsınız. Lütfen Paket Yükseltin.", "package_expire_not_authorized": "Kullandığınız Pakette Bu işlem için tanımlanan süre doldu. Lütfen Paket Yükseltin.", "package_expire": "Kullandığınız Paketin Süresi Doldu. Lütfen Paketinizi Yenileyin.", "conversation_package": "Lütfen Ek Mesaj Paketi Alın.", "not_permissions": "<PERSON>u i<PERSON><PERSON>i yapma yetkiniz bulunmamaktadır.", "company_not_found": "Şirket bulunamadı.", "company_waba_not_found": "Şirket Waba id bulunamadı.", "channel_waba_not_found": "Kanal Waba id bulunamadı. Lütfen Sistem Yöneticisi İletişime Geçin.", "company_id_not_found": "Şirket id bilgisi gerekmektedir.", "integration": {"address_not_found": "addressId parametresi zorunludur.", "not_found": "Entegrasyon bulunamadı.", "couponcode_not_found": "İndirim kupon kodu bulunamadı"}, "chat": {"pinned_chat_max_count": "En fazla 50 adet müşteri sabitleyebilirsiniz.", "chat_not_found": "chatId parametresi zorunludur."}, "whatsapp_message_error": "Whatsapp tarafında beklenmeyen bir hata meydana geldi", "instagram_message_error": "Instagram tarafında beklenmeyen bir hata meydana geldi", "pagination_not_found": "Sayfalama Bilgisi Bulunamadı", "page_count_not_found": "Sayfa Sayısı bulunamadı.", "perpage_not_found": "Sayfalama Sayısı bulunamadı.", "secret_key_not_found": "Güvenlik Kodu Bulunamadı", "secret_key_error": "Güvenlik Kodu Yanlış", "username_not_found": "<PERSON><PERSON>", "password_not_found": "Şifre Bulunamadı", "chat_list_not_found": "Lütfen Sohbet Listesi Seçin", "billtekrom_account_id_not_found": "Bu Şirkette Billtekrom Hesap ID'si Bulunamadı", "billtekrom_account_not_found": "Bu Şirkette Billtekrom Hesabı Bulunamadı", "status": "status bilgisi bulunamadı", "platform": "platform bilgisi bulunamadı", "not_supported": "Bu özellik desteklenmiyor"}, "form_field": {"first_name": "Ad", "last_name": "Soyad", "fullname": "Ad Soyad", "email": "Email", "phone": "Telefon Numarası", "account_info": "Üyelik Bilgileri", "address_info": "<PERSON><PERSON>", "address1": "<PERSON><PERSON>", "city": "Şehir", "province": "Eyalet", "zip": "Posta Kodu", "country": "<PERSON><PERSON><PERSON>", "company": "Firma", "password": "Şifre", "gender": "Cinsiyet", "birth_date": "Doğum Günü", "birth_month": "Doğum Ayı", "birth_year": "Doğum Yılı", "district": "İlçe", "address": "<PERSON><PERSON>", "mail_notify": "Mail Bildirimi", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "woman": "Kadın", "man": "<PERSON><PERSON><PERSON>", "member_contract": "Üyelik Sözleşmesi", "address_type": "<PERSON><PERSON>", "individual": "<PERSON><PERSON><PERSON><PERSON>", "institutional": "<PERSON><PERSON><PERSON>", "address_title": "<PERSON><PERSON> Başlığı", "identity_number": "Tc Kimlik Numarası", "post_code": "Posta Kodu", "tax_administration": "<PERSON><PERSON><PERSON>", "tax_number": "<PERSON><PERSON><PERSON>", "nationality": "Tc uyruklu değilim", "all": "<PERSON><PERSON><PERSON>"}, "language_code": {"tr": "Türkçe", "ar": "<PERSON><PERSON><PERSON>", "fr": "Fransızca", "de": "Almanca", "en": "İngilizce", "ru": "Rus<PERSON>", "default": "Varsayılan"}}, "Site": {"validators": {"login": {"email_field_required": "Mail alanı eksik.", "mail_lenght": "Mail alanı uzunluğu hatalı", "password_field_required": "Ş<PERSON>re alanı gerekli"}, "add_new_user": {"name_field_required": "<PERSON>sim alanı gerekli.", "name_length": "İsim alanı uzunluğu hatalı.", "email_field_required": "Mail alanı eksik.", "not_valid_email": "Mail alanı hatalı.", "mail_lenght": "Mail alanı uzunluğu hatalı", "password_field_required": "Şifre alanı gerekli.", "password_repeat_required": "Şifre tekrarı alanı gerekli"}, "user_update": {"name_field_required": "<PERSON>sim alanı gerekli.", "name_length": "İsim alanı uzunluğu hatalı"}, "forgot_password": {"email_field_required": "Mail alanı eksik.", "not_valid_email": "Mail alanı hatalı"}, "set_new_password": {"new_password_field_required": "<PERSON>ni Şifre alanı gerekli.", "new_password_length": "Yeni Şifre alanı uzunluğu hatalı.", "new_password_repeat_field_required": "New Şifre tekrarı alanı gerekli.", "new_password_repeat_length": "Yeni Şifre tekrar alanı uzunluğu hatalı.", "passwords_are_not_equal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uyuşmuyor"}, "edit_user": {"name_field_required": "İsim alanı uzunluğu hatalı.", "email_field_required": "Mail alanı eksik.", "new_password_field_required": "<PERSON>ni Şifre alanı gerekli.", "new_password_repeat_field_required": "Yeni Şifre tekrar alanı gerekli.", "passwords_are_not_equal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uyuşmuyor"}, "update_profile": {"name_field_required": "İsim alanı uzunluğu hatalı.", "password_field_required": "Şifre alanı gerekli.", "password_repeat_required": "Şifre tekrarı alanı gerekli.", "passwords_are_not_equal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uyuşmuyor"}, "contact_form_message": {"first_name_required": "<PERSON>sim alanı gerekli.", "name_length": "İsim alanı uzunluğu hatalı.", "last_name_required": "Last <PERSON><PERSON><PERSON> al<PERSON> gerek<PERSON>.", "last_name_length": "Last İsim alanı uzunluğu hatalı.", "email_field_required": "Mail alanı eksik.", "not_valid_email": "Mail alanı hatalı.", "mail_length": "Mail alanı uzunluğu hatalı", "subject_field_required": "Başlık alanı gerekli.", "subject_length": "Başlık uzunluğu hatalı.", "content_field_required": "İçerik alanı gerekli.", "content_field_length": "İçerik alanı uzunluğu hatalı"}, "try_form_message": {"first_name_required": "<PERSON>sim alanı gerekli.", "name_length": "İsim alanı uzunluğu hatalı.", "last_name_required": "Soyad alanı gerekli.", "last_name_length": "Soyad alanı uzunluğu hatalı.", "email_field_required": "Mail alanı eksik.", "not_valid_email": "Mail alanı hatalı.", "mail_length": "Mail alanı uzunluğu hatalı", "phone_field_required": "Telefon alanı gerekli", "phone_length": "Telefon alanı uzunluğu hatalı", "company_name_field_required": "Firma İsim alanı gerekli.", "company_name_length": "Firma İsim alanı uzunluğu hatalı", "business_sector_field_required": "İş Sektörü alanı zorunludur.", "business_sector_length": "İş Sektörü alanı uzunluğu geçerli değil.", "staff_count_field_required": "Personel Sayısı alanı gerekli.", "staff_count_length": "Personel Sayısı alanı uzunluğu hatalı.", "content_field_required": "İçerik alanı gerekli.", "content_field_length": "İçerik alanı uzunluğu hatalı"}, "unhandled_error": "Hata. Lütfen Site yöneticisine başvurunuz.", "channel": {"name_cannot_be_empty": "İsim boş geçilemez.", "type_cannot_be_empty": "Tip boş geçilemez"}}, "errors": {"main": {"captcha": "<PERSON><PERSON> hat<PERSON>.", "row_count": "Lütfen her satırda gösterilecek ürün sayısını 2, 3, 4 ya da 5 olarak ayarlayınız"}, "auth": {"credentials_not_valid": "Girdiğiniz bilgileri kontrol ederek tekrar deneyiniz", "login": {"user_is_passive": "Üyeliğiniz pasif <PERSON>n giriş yapamazsınız.", "user_not_available_in_user_table": "Bağlantı gerçekleştirilemedi lütfen yönetici ile iletişime geçiniz"}, "set_new_password": {"url_is_invalid": "Yeni şifre belirleme bağlantısı geçersiz. Lü<PERSON><PERSON>, Şifremi Unuttum Formu'nu doldurunuz.", "token_user_not_found": "Bilgiler hatalı girildi, lütfen tekrar deney<PERSON>z"}}, "user": {"login": {"not_permitted": "Bu iş<PERSON>i yapmaya yetkiniz yok.", "agent_is_not_in_your_company": "Bu iş<PERSON>i yapmaya yetkiniz yok.", "user_not_active": "Kullanıcı Aktif <PERSON>.", "user_not_permitted": "Kullanıcının App'a giriş yapmaya yetkisi yok.", "user_not_found": "İlişkili kullanıcı bulunamadı"}, "add_new_user": {"user_exist": "Bu e-mail adresi sistemde eklidir. Lütfen başka bir e-mail adresi deneyiniz"}, "enable_user": {"associated_user_not_found": "İlişikili kullanıcı bulunamadı.", "not_in_your_company": "Kullanıcı sizin şirketinizine ait değil.", "already_enabled": "Kullanıcı zaten aktif görünüyor.", "self_edit": "Kendinizi aktif edemezsiniz"}, "disable_user": {"associated_user_not_found": "İlişikili kullanıcı bulunamadı.", "not_in_your_company": "Agent sizin şirketinizine ait değil.", "already_disabled": "Kullanıcı zaten pasif gö<PERSON>ü<PERSON>r.", "self_edit": "Kendinizi pasif ed<PERSON>"}, "edit_user": {"associated_login_not_found": "İlişikili login bilgisi bulunamadı.", "associated_user_not_found": "İlişikili kullanıcı bulunamadı.", "not_in_your_company": "Agent sizin şirketinizine ait değil"}, "edit_permissions": {"cannot_change_owner_permission": "Yöneticinin izinlerini değiştiremezsiniz.", "associated_user_not_found": "İlişikili kullanıcı bulunamadı.", "invalid_self": "<PERSON><PERSON>ü<PERSON>"}}, "dash": {"enable_channel": {"associated_channel_not_found": "Şirkete bağlı kanal bulunmamaktadır.", "not_in_your_company": "Şirkete bağlı kanal bulunmamaktadır.", "already_enabled": "Kanal önceden aktif edilmiş"}, "disable_channel": {"associated_channel_not_found": "Şirkete bağlı kanal bulunmamaktadır.", "not_in_your_company": "Şirkete bağlı kanal bulunmamaktadır.", "already_disabled": "<PERSON><PERSON>den pasif ed<PERSON>"}, "enable_integration": {"already_enabled": "Entegrasyon önceden aktif edilmiş"}, "disable_integration": {"already_disabled": "Entegrasyon önceden pasif edil<PERSON>"}, "update_profile": {"user_not_found": "Kullanıcı bulunamadı.", "old_password_wrong": "Girdiğiniz eski şifre yanlış.", "associated_user_not_found": "İlişikili kullanıcı bulunamadı"}}, "channel": {"unkown_channel_type": "Bilinmeyen Kanal Tipi"}}, "success": {"user": {"added": "Agent <PERSON><PERSON><PERSON><PERSON> ba<PERSON>arılı", "enabled": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "Agent <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "edited": "Agent bi<PERSON><PERSON><PERSON><PERSON>.", "permissions_saved": "İzinler başarı ile kaydedildi"}, "dash": {"profile_updated": "<PERSON>il baş<PERSON><PERSON> ile g<PERSON>.", "logout_success_message": "Başarılı bir şekilde çıkış yapıldı.", "channel_enabled": "Kanal aktifleştirildi.", "channel_disabled": "<PERSON><PERSON> pasifleş<PERSON>di.", "integration_enabled": "Entegrasyon Aktifleştirildi", "integration_disabled": "Entegrasyon <PERSON>"}, "auth": {"set_new_password": "Şifreniz başarı ile değiştirilmiştir."}, "main": {"contact_form_message_sent": "İletişim formu başarı ile gönderildi.", "try_form_message_sent": "Deneme formu başarı ile gönderildi"}}, "create_channel_success": "Kanal'ı başarı ile oluşturuldu.", "edit_channel_success": "Kanal başarı ile değiştirildi.", "create_try_success": "Deneme formunuz başarıyla oluşturulmuştur", "channels": {"edit": {"title_TR": "Karşılama Mesajı", "title_EN": "Karşılama Mesajı ( İngilizce )"}}, "channel_types_live_chat": "Canlı Chat", "permissions": {"options": {"please_select": "<PERSON><PERSON><PERSON><PERSON>", "allow": "<PERSON><PERSON> ver", "deny": "<PERSON><PERSON> verme"}, "edit_permissions": {"user": {"create": "Kullanıcının yeni operator ekleme yetkisini aktif/pasif yapar.", "edit": "Kullanıcıların farklı kullanıcıların özelliklerini değiştirme yetkisini aktif/pasif yapar.", "enable": "Kullanıcıyı aktif yapar böylelikle kullanıcının girişi aktif olur.", "disable": "Kullanıcı pasif ya<PERSON> bö<PERSON> kullanıcının girişi pasif olur.", "edit_permissions": " Kullanıcının yet<PERSON>ini düzenleme yetkisi.", "update": "Kullanıcıların farklı kullanıcıların özelliklerini güncelleme yetkisini aktif/pasif yapar."}, "app": {"login": "Kullanıcının APP uygulamasına girişini aktif/pasif eder.", "login_as_agent": "Kullanıcıya kanal ayarlarını düzenleme yetkisini aktif/pasif yapar."}, "channel": {"create": "Kullanıcıya kanal ayarlarını düzenleme yetkisini aktif/pasif yapar.", "edit": "Kullanıcıya kanal ayarlarını düzenleme yetkisini aktif/pasif yapar.", "update": "Kullanıcıya kanal güncelleme yetkisini aktif/pasif yapar."}, "site": {"login": "Kullanıcıya siteye girişini aktif/pasif yapar."}, "quick_reply": {"update": "Hızlı cevap özelliğini güncelleme aktif/pasif yapar."}, "integration": {"update": "Entegrasyon güncelleme aktif/pasif yapar.", "create": "Entegrasyon oluşturma aktif/pasif yapar."}, "thinker": {"enable": "Thinker'a eri<PERSON><PERSON>i aktif/pasif ya<PERSON>."}, "helobot": {"enable": "He<PERSON><PERSON>'a erişimi aktif/pasif yapar."}, "reports": {"enable": "<PERSON><PERSON><PERSON><PERSON> erişimi aktif/pasif yapar."}, "mail_service": {"enable": "Mail Kutusuna erişimi aktif/pasif yapar."}, "hepsiburada": {"enable": "Hepsib<PERSON><PERSON>'ya erişimi aktif/pasif yapar."}, "trendyol": {"enable": "Trendyol'a erişimi aktif/pasif yapar."}, "pazarama": {"enable": "Pazarama'ya erişimi aktif/pasif yapar."}, "social_marketing": {"enable": "<PERSON><PERSON><PERSON> Medya <PERSON> erişimi aktif/pasif ya<PERSON>."}, "chat_force_took": {"enable": "Sohbeti başka temsilci üzerinden alma yetkisini aktif/pasif ya<PERSON>."}, "message_template": {"enable": "<PERSON><PERSON>na erişimi aktif/pasif ya<PERSON>."}}}, "integration": {"days": {"0": "Pazar", "1": "<PERSON><PERSON><PERSON>", "2": "Salı", "3": "Çarşamba", "4": "Perşembe", "5": "<PERSON><PERSON>", "6": "<PERSON><PERSON><PERSON><PERSON>"}, "small": "Küçük", "medium": "Orta", "big": "Büyük"}}, "App": {"errors": {"campaign": {"not_found": "Kampanya Bulunamadı", "not_member": "Kampanya Sadece Üyeler için Geçerlidir."}, "agent": {"required": "Agent id gönderilmesi zorunludur.", "move_to": "move_to alanı zorunludur."}, "user": {"customer_note_not_found": "Müşteri notu bilgisi bulunamadı", "not_found": "Kullanıcı Bulunamadı", "name_not_found": "Kullanıcı Adı Bulunamadı", "password_invalid": "Şifrenin geçerli olabilmesi için aşağıdaki kurallara uyması gerekir:\n{{text}}", "password_uppercase": "- En az 1 büyük harf içermelidir \n", "password_numeric": "- En az 1 sayısal karakter içermelidir \n", "password_special": "- En az 1 özel karakter (.,*-!) içermelidir \n", "password_length": "- En az 6 karakter olmalıdır", "allready_exists": "Kullanıcı Zaten Var"}, "conversation": {"not_found": "Konuşma bulunamadı.", "does_not_belongs_to_you": "Konuşma size ait değil.", "chat_on_agent": "Konuşma {{name}} is<PERSON><PERSON>n Üzerinde. Lütfen Ondan Talep Edin.", "chat_on_agent_with_team": "Konuşma {{team}} ekininin {{name}} is<PERSON><PERSON> Üzerinde. Lütfen Ondan Talep Edin.", "message_cannot_be_empty": "<PERSON><PERSON> bo<PERSON>önderile<PERSON>z.", "payment_options_could_not_reached": "<PERSON><PERSON><PERSON>şılamadı.", "content_is_required": "Content Bilgisi Zor<PERSON>ludur", "max_1000": "Mesaj <PERSON> en fazla 1000 karakter olabilir", "quick_replies_max_1000": "Hızlı Cevap Uzunluğu en fazla 1000 karakter olabilir", "oowh_max_1000": "Mesai <PERSON> {{lang}} Uzunluğu en fazla 1000 karakter olabilir", "welcome_max_1000": "Karşılama Saati {{lang}} Uzunluğu en fazla 1000 karakter olabilir", "temp_id_not_found": "temp_id bulunamadı", "content_unavaliable": "Geçersiz <PERSON>j <PERSON>", "is_not_array": "Lütfen Toplu olarak Kullanıcı Gönderiniz.", "key_not_found": "Zorunlu Alanları Lütfen Doldurun", "thinker_active": "Konuş<PERSON>u anda bot aktif <PERSON> i<PERSON><PERSON> en<PERSON>.", "chat_received": "{{chat_name}} sohbeti {{name}} kişisi tarafından alındı.", "chats_foward_not_found": "Kişinin üzerdeki sohbetlerin hangi listeye (Bekleyen - Arşiv - Operatöre Atama) alınacağını seçiniz", "unarchive_max_1000": "Sohbet arşivden çıktığında gönderilecek mesaj {{lang}} uzunluğu en fazla 1000 karakter olabilir"}, "channel": {"not_found": "İletişim kanalı bulunamadı.", "is_not_active": "İletişim kanalı aktif değil.", "channel_id_not_found": "Kanal id zorunlu bir parametredir.", "reviewer_id_not_found": "Müşteri id zorunlu bir parametredir.", "wrong_status": "Hatalı Durum Mesajı", "active_timeout": "<PERSON><PERSON><PERSON>an Çıkarma Süresini belirleyiniz", "notification_sound_status": "Bildirim Sesi Durumu Boş Geçilemez", "invalid_type": "Geçersiz Kanal Tipi", "welcome_messaging_setting_not_found": "Karşılama Mesajı bilgisi bulunamadı", "welcome_messaging_setting_en_not_found": "Karşılama Mesajının İngilizce bilgisi bulunamadı", "welcome_messaging_setting_tr_not_found": "Karşılama Mesajının Türkçe bilgisi bulunamadı", "min_one": "En az 1 Seçenek olmalıdır.", "max_ten": "En fazla 10 Seçenek olmalıdır.", "button_text_not_found": "Kategori Adı Eksik. Bütün Kategorileri Giriniz.", "agent_channel_authority": "Bu agent'ın kanala yetkisi bulunmamaktadır.", "instagram_quick_replies": "quick_replies formatı yanlış", "instagram_quick_replies_not_found": "Seçilen Hızlı Cevap Bulunamadı", "instagram_quick_replies_text_not_found": "Başlık boş bırak<PERSON>lmaz", "start_type": "Lütfen Akış Başlatma Seçeneğini Doğru Seçin", "connection_failed": "Bağlantı başarısız.", "unarchive_messaging_setting_not_found": "Sohbet arşivden çıktığında gönderilecek mesaj bilgisi bulunamadı", "archive_messaging_setting_not_found": "Sohbet arşivlenirken gönderilecek mesaj bilgisi bulunamadı", "only_cloud_api": "Bu özellik sadece Cloud API ile bağlı kanallar için geçerlidir", "wigdet_size_invalid": "Widget boyutu bilgisi geçersiz", "name_exists": "Bu isimde bir Livechat kanalı zaten mevcut", "cloud_profile": {"about_option": "Hakkında En Fazla 139 Karakter Olmalıdır.", "address_option": "Address En Fazla 256 <PERSON><PERSON><PERSON>ıdı<PERSON>.", "description_option": "Tanım En Fazla 512 Karakter Olmalıdır.", "vertical_option": "Geçers<PERSON>", "email_option": "Email En Fazla 128 <PERSON><PERSON><PERSON>.", "websites_option": "Website En Fazla 2 tane Olmalıdır ve En Fazla 256 <PERSON><PERSON>er Olmalıdır.", "profile_pic_option": "Geçersiz Resim"}, "words": "<PERSON><PERSON><PERSON>", "auto_tag_status": "auto_tag_status bilgisi geçersiz", "auto_tag_not_found": "Otomatik Etiket Atama Kaydı Bulunamadı", "event_data_set": {"invalid": "Geçersiz Etkinlik Adı '{{event_name}}'"}}, "auth": {"login_as_agent": {"token_is_not_temp": "Sayfa bulunamadı.", "token_not_found": "Token bulunamadı.", "token_invalid": "Token geçersiz.", "user_not_found": "Sepete ürün eklemeden önce üye kaydı yapılamsı gerekmekte.", "user_not_active": "Kullanıcı aktif <PERSON>", "company_not_active": "Firma aktif <PERSON>", "company_not_found": "Böyle bir firma yok.", "login": "Girdiğiniz bilgileri kontrol ederek tekrar giriş yapınız.", "user_not_available_in_user_table": "Bağlantı gerçekleştirilemedi lütfen yönetici ile iletişime geçiniz.", "email_is_not_found": "Email boş bırakılamaz", "jwt_is_not_found": "jwt bulunamadı", "email_invalid": "Lütfen Geçerli Bir Email Adresi Giriniz"}, "forget_password": {"login_not_found": "Girdiğiniz bilgileri kontrol ederek tekrar deneyiniz", "mail_warning": "Lütfen 2 dakika sonra tekrar deneyin"}, "set_new_password": {"token_not_found": "Token bulunamadı.", "hashes_dont_match": "Token geçersiz.", "token_type_not_correct": "Token geçersiz.", "token_expired": "Token süresi do<PERSON>.", "token_used": "Token kullanılmış.", "passwords_should_not_be_empty": "Yeni şifre ve şifre tekrarı alanı boş geçilmemelidir.", "passwords_should_be_equal": "Yeni şifre ve şifre tekrarı alanı aynı olmalıdır.", "could_not_find_assosiciated_user": "Bilgiler ya<PERSON><PERSON><PERSON><PERSON>, lütfen tekrar den<PERSON>z"}}, "message_report": {"start_date_end_date_not_found": "Başlangıç saati ve bitiş saati olmak zorunda."}, "main": {"log": {"type_not_specified": "Log tipi belirtilmedi.", "message_not_specified": "Log mesajı belirtilmedi.", "type_could_not_find": "Log tipi bulunamadı"}}, "dash": {"conversation_is_disabled": "Yöneticiniz tarafından iletişim kanalı pasif edildiği mesaj gönderilemez.", "conversation_belong_to_someone": "Konuşma başkasına ait görünüyor.", "conversation_active_time_expired": "Bu müşteri ile gö<PERSON>üşme, zaman aşımına uğradığı için iletişim kuramazsınız"}, "integration": {"domain_invalid": "<PERSON> adı geçersiz", "currency_code_not_found": "Para birimi uyuşmazlığı var.", "integration_and_channel_company_ids_do_not_match": "Entegrasyon ve Kanal şirketleri eşleşmiyor.", "integration_id_cannot_be_empty": "IntegrationId boş bırakılamaz", "action_cannot_be_empty": "Action boş bırakılamaz", "type_cannot_be_empty": "Type boş bırakılamaz", "integration_page_is_wrong": "Entegrasyon sayfası hatalı", "user_not_found": "Müşterinin üye yapılması gerekmektedir.", "cart_is_empty": "Sepetinizde ürün bulunamadı", "addresses_are_empty": "Paylaşılacak adress bulunamadı.", "chat_id_cannot_be_empty": "ChatId boş bırakılamaz", "cargos_are_empty": "Paylaşılacak kargo bulunamadı.", "quick_reply_not_found": "Quick Reply parametresi bulunamadı", "text_not_found": "Text bulunamadı", "caption_not_found": "Caption bulunamadı", "remittance_name_not_provided": "Havale & EFT bilgisi yazılmalıdır", "remittance_id_not_provided": "Havale & EFT id bilgisi yazılmalıdır", "keys_not_found": "Keys bulunamadı", "keys_limit": "Keys en fazla 3 elemanlı olmalıdır.", "lowest_key_limit": "Keys en az 2 elemanlı olmalıdır.", "max_key_limit": "Keys en fazla 10 elemanlı olmalıdır.", "duplicate_snipped_text": "Aynı mesaj kısayolundan birdan fazla eklenemez!", "duplicate_match": "<PERSON><PERSON>ı kısayoldan birden fazla eklenemez !", "sub_payment_option_not_found": "SubPaymentOptionId bulunamadı.", "stock_not_found": "Stokta olmayan ürünü ekleyemezsiniz.", "address_data_not_found": "Adres bilgisi bulunamadı", "campaign_group_id_not_found": "Kampanya Grup id bilgisi bulunamadı", "payment_option_id_not_found": "Ödeme Seçenek Bilgisi Bulunamadı", "cargo_option_id": "Kargo Seçenek Bilgisi Bulunamadı", "integration_not_found": "Entegrasyon Bilgisi Bulunamadı", "info_not_found": "Bilgi Bulunamadı", "product_not_found": "Ürün Bulunamadı", "share_address_not_found": "Paylaşılmak istenen adres bulunamadı", "pay_at_door_options": "pay_at_door_options bilgisi eksik", "oowh_messages": "oowh_messages bilgisi eksik", "ads_messages": "ads_messages bilgisi eksik", "chronological_order": "Mesai saati aralıklarını sırayla eklemeniz gerekmektedir.", "start_hour_not_greater_than_end": "Başlangıç saati bitiş saatinden büyük olamaz", "start_hour_not_equal_end": "Başlangıç saati ve Bitiş saati eşit olamaz", "maximum_hour_range": "En fazla 3 saat aralığı girilebilir", "order_status_bad_request": "order_status_id 0 olamaz", "is_active_default_oowh_messages": "Mesai saati dışı mesajı gönderilsin seçili değilken default mesai saati mesajını göndermeye çalışıyorsunuz. Önce mesai saati mesajını göndermeyi seçiniz", "working_hours_empty": "<PERSON><PERSON><PERSON><PERSON> her ikisi de dolu veya boş olmalı", "required_seven_for_days": "7 günlük değer girilmeli", "first_name_required": "<PERSON>sim alanı gerekli.", "last_name_required": "İkinci İsim alanı gerekli.", "email_required": "Mail alanı eksik.", "phone_required": "Telefon alanı eksik.", "channel_name_not_found": "Kanal Adı bulunamadı", "the_product_is_out_of_stock": "<PERSON><PERSON>ün Stokta Yok", "wrong_status": "Hatalı Durum Mesajı", "there_is_integration": "Bu Kullanıcı Zaten Üye", "working_hour_start_end": "Lütfen mesai saatlerinin birini dolu diğerini boş bırakmayın.", "perpage_not_found": "Lütfen Önce Sayfalama Adedi Giriniz", "perpage_wrong_value": "Lütfen 1 ve 10 arasında değer giriniz", "next_or_after": "<PERSON><PERSON>e <PERSON> veya Önceki Sayfayı İsteyin", "post_not_found": "Post id bilgisi bulunamadı", "comment_not_found": "Lütfen Yorum Yazınız", "comment_id_not_found": "<PERSON><PERSON> bulu<PERSON>adı", "not_found_variant": "Seçilen Ürünün Variantı bulunamadı", "once_cargo": "<PERSON>nce Kargo <PERSON>", "empty_cart": "Sepet Bulunamadı", "user_email_not_found": "Lütfen Önce Müşterinin Mailini Kaydedin.", "min_count": "En az {{count}} arttırımı yapılabilir", "note_field_length": "Not alanı karakter uzunluğu en fazla 250 olabilir", "no_address": "Lütfen Adres <PERSON>", "page_value": "Sayfa Sayısı Hatalı", "customer_email_required": "Müşteriden İlk Olarak Email Bilgisi İstendi. Email Kaydı Yapınız", "user_registered": "Kullanıcı kayıtlı", "user_paired": "Kullanıcı eşlendi", "user_paired_error": "Kullanıcı Entegrasyonunuzda Bulunamadı. Lütfen Tekrar Eşleştirme Yapın", "user_pairing_removed": "Kullanıcı eşleme kaldırıldı", "user_not_previously_paired": "Bu kullanıcı daha önce eşleştirilmemiş", "phone_number_invalid": "Telefon numarası geçersiz", "email_invalid": "E-mail geçersiz", "name_invalid": "İsim geçersiz", "password_invalid": "Şifre geç<PERSON>iz", "post_or_comment_removed": "İşlem Yapılamıyor. Gönderi veya Yorum Silinmiş Olabilir. Lütfen Meta tarafından kontrol edin.", "cart_isnot_avaliable": "Sepet Aktif <PERSON>", "payment_not_found": "Ödeme Yöntemi Bulunamadı", "livechat_not_added": "LiveChat Sitenize Zaten Eklenmemiş.", "livechat_added": "LiveChat Sitenize Zaten Eklenmiş."}, "settings": {"user_not_found": "User not found", "assign_chat_to_agent_not_found": "Temsilciye sohbet atama bulunamadı", "hide_phone_number_not_found": "Numara gizleme ayarı bulunamadı", "chat_message_read_status_not_found": "<PERSON><PERSON>ın okunma durumu gizleme ayarı bulunamadı", "company_owner_get_force_chat_invalid": "Zorla chat alma değeri geçersiz.", "daily_report_not_found": "Günlük rapor seçeneği bulunamadı.", "weekly_report_not_found": "Haftalık rapor seçeneği bulunamadı.", "monthly_report_not_found": "<PERSON><PERSON><PERSON>k rapor seçeneği bulunamadı.", "export_right_not_found": "Bu kanal için yedek alma hakkınız bulunmamaktadır. {{date}} tarihinde tekrar deneyiniz.", "at_least_one_ads_required": "En azından bir reklam eklemeniz gerekli.", "ads_required": "Reklam seçmeniz gere<PERSON>li.", "duplicate_ads": "Aynı reklamı birden fazla ekleyemezsiniz.", "at_least_one_reply_method_required": "En az bir mesaj seçeneği seçiniz.", "thinker_flow_id_required": "Thinker <PERSON><PERSON><PERSON><PERSON><PERSON> seçmeniz <PERSON>.", "helobot_knowledge_base_id_required": "Helobot veri kaynağı seçmeniz gerekli.", "message_text_required": "<PERSON><PERSON>.", "message_cannot_exceed_1000_characters": "Mesaj 1000 karakteri geçemez. Karakter sayısı emoji yada özel karakter konulursa daha fazla olarak sayılır."}, "ask_form_questions": {"field_key_not_found": "Field Key Bulunamadı"}, "live_chat": {"ext_id_not_found": "Ext Id parametresi zorunludur.", "message_not_found": "<PERSON>j <PERSON>", "name_required": "Lütfen isminizi yazın.", "email_required": "Mailinizi yazın lütfen.", "phone_required": "Telefon numaranızı yazın lütfen.", "user_not_found": "Kullanıcı bulunamadı"}, "cart": {"cannot_add_this_currency_code": "Farklı Bir Para Birimli Ürün Sepete Eklenemez"}, "google": {"error_process": "Hatalı İşlem", "calendar": {"not_found_calendar_id": "Takvim ID hatalı", "not_found_summary": "Başlık Boş Bırakılamaz.", "not_found_description": "Açıklama Boş Bırakılamaz.", "not_found_end_time": "Bitiş tarihi hatalı"}, "drive_account": {"allready_exist": "Bu Kanalda Zaten Hesap Aktif", "not_found": "Önce Google Drive Hesabınızı Bağlayın"}}, "tags": {"tag_not_found": "Etiket Bulunamadı", "already_exists_tag": "Etiket Zaten Kayıtlı", "already_exists_color": "Etiket Rengi Zaten Kayıtlı", "tag_id_not_found": "Etiket id eksik", "ids_not_found": "ID bilgileri eksik", "max_label": "Maximum 250 tane etiket oluşturabilirsiniz"}, "instagram": {"channel_id_invalid": "Kanal ID geçersiz", "status_invalid": "Status geçersiz", "message_invalid": "Soru-cevap içeriği boş bırakılamaz", "persistent_menu_invalid": "Persistent <PERSON><PERSON>", "call_to_actions_invalid": "Call to actions geçersiz", "messages_not_found": "<PERSON><PERSON>", "default_messages_not_found": "Varsayılan mesaj geçersiz", "instagram": "Call to actions geçersiz", "ice_braker_status": "İlk Önce Karşılama Mesajı Kaydı Yapın", "ref_not_found": "<PERSON><PERSON>", "ref_id_not_found": "Ref ID geçersiz", "ref_registered": "<PERSON><PERSON>", "instagram_channel_invalid": "Kanal tipi geçersiz."}, "message": {"temp_id": "temp_id bulunamadı", "pagination_invalid": "Pagination bilgisi geçersiz", "perpage_invalid": "perpage değeri 20 - 100 aralığında olmalıdır", "page_invalid": "page değeri en az 0 olabilir", "text_invalid": "<PERSON><PERSON><PERSON> metni bulu<PERSON>adı", "agent_message_not_found": "Daha ö<PERSON> hiç konuşma olmamış", "not_found": "<PERSON><PERSON>.", "retry_done": "Mesaj Artık Gönderilemez. Tekrar Gönderme Denemesi Bitti", "channel_type_mismatch": "Sadece Aynı Kanal Tiplerinde Mesaj İletmesi Yapılabilir", "agent_only_view": "Sohbet mevcut ancak temsilci yanıt vermemiş."}, "team": {"agent_id_not_found": "Agentın id bilgisi geçersiz", "channel_id_not_found": "Kanalın id bilgisi geçersiz", "team_id_not_found": "Ekip id geçersiz", "channels_not_found": "Kanallar <PERSON>", "agents_not_found": "<PERSON><PERSON>", "team_not_found": "Ekip bulunamadı", "status_not_found": "Status durumu geçersiz", "team_name_not_found": "Ekip adı geçersiz", "channel_not_authorization": "Kanala <PERSON> bulunmamaktadır", "name_already_exists": "<PERSON><PERSON> is<PERSON><PERSON> ka<PERSON>il<PERSON>ş ekip zaten var.", "team_status_not_found": "Ekibin status bilgisi yok", "team_setting_status_inactive": "Ekip ayarı pasif", "chat_on_team": "<PERSON><PERSON><PERSON> {{team_name}} ekibi üzerindedir."}, "trendyol": {"account_available": "Hesap mevcut", "account_not_found": "<PERSON><PERSON><PERSON> b<PERSON>", "text_not_found": "<PERSON><PERSON>", "question_id_not_found": "question id bulunamadı", "name_not_found": "İsim bulunamadı", "supplier_id_not_found": "Supplier Id bulunamadı", "username_not_found": "username bil<PERSON><PERSON> bulu<PERSON>ı", "password_not_found": "password bilgisi bulunamadı"}, "hepsiburada": {"account_available": "Hesap mevcut", "account_not_found": "<PERSON><PERSON><PERSON> b<PERSON>", "text_not_found": "<PERSON><PERSON>", "question_id_not_found": "question id bulunamadı", "name_not_found": "İsim bulunamadı", "supplier_id_not_found": "Supplier Id bulunamadı", "username_not_found": "username bil<PERSON><PERSON> bulu<PERSON>ı", "password_not_found": "password bilgisi bulunamadı", "merchant_id_not_found": "Mağaza id Bulunamadı", "error_auth": "Mağaza ID veya Password Hatalı. Lütfen Bilgilerinizi kontrol edin"}, "helobot": {"account_available": "Hesap mevcut", "account_not_found": "<PERSON><PERSON><PERSON> b<PERSON>", "question_not_found": "question id bulunamadı", "name_not_found": "İsim bulunamadı", "file_urls_not_found": "<PERSON><PERSON><PERSON> b<PERSON>", "kb_id_invalid": "Knowledge base bilgisi geçersiz", "kb_not_found": "Knowledge base bulunamadı", "conversation_id_not_found": "Conversation id bulunamadı", "timeout": "timeout bil<PERSON><PERSON> bulunamadı veya geçersiz olarak girildi", "allready_started": "<PERSON>u sohbet şuan Helobot'a aktarılmış durumda", "not_found": "Bu sohbete ait Helobot akışı bulunamdı"}, "media": {"not_found_media_name": "Media Adı Bulunamadı"}, "location": {"id": "id Bilgisi Bulunamadı", "name": "name Bilgisi Bulunamadı", "title": "name Bilgisi Bulunamadı", "address": "address Bilgisi Bulunamadı", "longitude": "longitude Bilgisi Bulunamadı", "latitude": "latitude Bilgisi Bulunamadı", "selected_location": "Seçilen Konum Bulunamadı", "name_already_exists": "<PERSON><PERSON> is<PERSON><PERSON> ka<PERSON> konum zaten var."}, "overtimes": {"status": "Geçersiz Mesai Durumu", "OVERTIME_ON": "Mesaiye <PERSON>la", "OVERTIME_OFF": "<PERSON><PERSON><PERSON>", "BREAK_TIME_ON": "<PERSON><PERSON>", "BREAK_TIME_OFF": "Molayı Bitir", "overtime_error": "Lütfen Önce Mesaiye başlayın ya da Molayı bitirin."}, "pazarama": {"account_available": "Hesap mevcut", "account_not_found": "<PERSON><PERSON><PERSON> b<PERSON>", "text_not_found": "<PERSON><PERSON>", "client_id_not_found": "client_id bilgisi bulunamadı", "client_secret_not_found": "client_secret bilgisi bulunamadı"}, "n11": {"api_key_not_found": "api_key bilgisi bulu<PERSON>ı", "api_password_not_found": "api_password bilgisi bulu<PERSON>ı"}, "abadoned_cart": {"abandoned_cart_id_not_found": "abandoned_cart_id Bulunamadı", "not_found": "Terkedilmiş Sepet Kaydı Bulunamadı"}, "service_account": {"cannot_delete": "Bu Servis Hesabı Silinemez."}, "market_quick_reply": {"text_not_found": "text <PERSON>il<PERSON><PERSON> Bulunamadı", "type_not_found": "type Bilgisi Bulunamadı ve Yanlış"}}, "mail": {"issue_invalid": "<PERSON>j tipi geçersiz(issue)", "header_invalid": "Başlık geçersiz", "body_invalid": "İçerik kısmı geçersiz", "agent_id_invalid": "agent_id geçersiz", "success_message": "Talebiniz Alınmıştır. Teşekkür Ederiz.", "error_message": "Hata oluştu talep iletilemedi.", "not_verified": "Önce email adresinizi onaylamanız gerekmektedir."}, "channel": {"upload_channel_picture": "<PERSON><PERSON> <PERSON><PERSON>", "change_channel_name": "Kanal İ<PERSON>i <PERSON>", "leave_active_conversation_time": "<PERSON><PERSON><PERSON> (Dakika)", "send_to_archive_time": "Sohbeti Arşive Gönderme", "send_to_archive_time_status": "Müşteri Cevap Vermezse Sohbeti Arşive Gönder", "send_to_archive_time_timeout": "Sohbeti Arşive Gönderme Süresi (Dakika)", "notification_sound_status": "<PERSON><PERSON><PERSON><PERSON>", "welcome_message": "Karşılama Mesajı", "send_welcome_message": "Karşılama mesajı gö<PERSON>in", "is_default_send_welcome_message": "Karşılama mesajı varsayılan olanlar gönderilsin", "welcome_message_item_title": "Karşılama mesajı", "whatsapp_about": "Whatsapp Ha<PERSON>ımda", "send_welcome_message_actions": "Karşılama Mesajı Ataması", "quick_replies": "Hızlı Cevap Ayarlar", "hide_phone_number": "Müşteri telefon numarası agent i<PERSON><PERSON>", "team_message": "Ekip Mesajı Ayarları", "unarchived": "Sohbet arşivden bekleyene geldikten sonra ekip mesajı iletilsin", "after_order": "Sipariş oluştuktan sonra iletilsin (2 saat sonra)", "before_order": "Sipariş oluşmadıysa iletilsin (24 saat sonra)", "snipped_text": "<PERSON>j <PERSON>", "unarchive_message": "Arşivden Çıkarı<PERSON>", "send_message_when_unarchived": "Sohbet Arşivden Çıktığında Mesaj Gönder", "is_default_send_archive_message": "Sohbet arşivden çıktığında varsayılan mesaj g<PERSON>", "unarchive_message_item_title": "Arşivden çıkarma mesajı", "send_unarchive_message_actions": "Arşiv Sonrası Mesajı Ataması", "timeout_for_archived_chat": "Mesaj <PERSON>bilmesi İçin <PERSON>in Arşivde Bekleme Süresi", "send_archive_message_actions": "Sohbet Arşivlenirken Mesaj Ataması", "is_default_send_archiving_message": "Varsayılan mesaj <PERSON>", "archive_message_item_title": "Arşive gönderilme mesajı", "send_message_while_chat_is_archived": "So<PERSON>bet Arşivlenirken Mesaj <PERSON>"}, "integration": {"send_cart_message": {"item": "[BR][/BR]{{emoji}}[B]{{title}} {{variant_message}}[/B][BR][/BR][B]Miktar:[/B] {{count}} {{stock_unit}}[BR][/BR][B]Fiyat:[/B] {{price}}[BR][/BR][B]Tutar:[/B] {{amount}}", "sub_total": "[B]Toplam:[/B] {{total}}", "coupon_total": "[BR][/BR][B]<PERSON><PERSON><PERSON> :[/B][SPACE]2[/SPACE]{{total}}", "campaign_total": "[B]<PERSON><PERSON><PERSON><PERSON> :[/B][SPACE]2[/SPACE]{{total}}", "general_total": "[BR][/BR][B]Genel Toplam:[/B] {{total}}", "approve_message": "<PERSON><PERSON><PERSON> sepetinizi onaylıyorsanız [B]1[/B] yazın lütfen"}, "send_history_message": {"item": "[BR][/BR]{{emoji}} [B]{{title}} ({{variant_message}})[/B][BR][/BR][B]Miktar :[/B][SPACE]17[/SPACE]{{count}} {{stock_unit}}[BR][/BR][B]Fiyat :[/B][SPACE]20[/SPACE]{{price}}[BR][/BR]", "order_code": "[B]Sipariş Kodu:[/B] {{order_code}}[BR][/BR]", "status": "[B]<PERSON><PERSON><PERSON><PERSON>u:[/B] {{status}}[BR][/BR]", "cargo": "[B]Kargo:[/B] {{cargo}}[BR][/BR]", "cargo_tracking_code": "[B]Kargo Takip Kodu:[/B] {{cargo_tracking_code}}[BR][/BR]", "cargo_tracking_url": "[B]Kargo Takip Linki:[/B] {{cargo_tracking_url}}[BR][/BR]", "date": "[B]Tarih:[/B] {{date}}[BR][/BR]", "total_price": "[B]Tutar:[/B] {{total_price}}[BR][/BR]", "order_url": "Sipariş Durumunuzu Buradan Görebilirsiniz.[BR][/BR]{{url}}"}, "send_heloscope_order_message": "Siparişiniz başarıyla oluşturuldu.", "send_product_share_messageV2": "[BR][/BR][B]{{product_title}}[/B][BR][/BR]{{description}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Bu ürünü sepete eklemek istiyorsanız [B]{{product_share_counter}}[/B] yazın.", "send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Fiyat :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Bu ürünü sepete eklemek istiyorsanız [B]{{product_share_counter}}[/B] yazın.", "send_product_share_message_no_action": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Fiyat :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}", "whatsapp_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Fiyat :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]", "livechat_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Fiyat :[/B]{{product_price}}{{product_variant}}[BR][/BR]{{base_url}}", "order_summary_message": "Sayın [B]{{username}}[/B][BR][/BR]Sepetinizdeki Ürünler:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Kargo Bilgileri :[/B][BR][/BR]Kargo Şirketi :[SPACE]10[/SPACE]{{cargo_option_name}}[BR][/BR]Kargo Ücreti :[SPACE]20[/SPACE]{{cargo_option_fee}}[BR][/BR][BR][/BR][B]Genel Toplam :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Fatura Adresi:[/B][BR][/BR][B]Adres :[/B] {{invoice_address}} [BR][/BR][B]İlçe:[/B] {{invoice_district}} [BR][/BR][B]İl :[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Teslimat Adresi :[/B][BR][/BR][B]Adres :[/B] {{delivery_address}} [BR][/BR][B]İlçe:[/B] {{delivery_district}} [BR][/BR][B]İl:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Ödeme yöntemi[/B]: {{payment_type}}[BR][/BR][BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen [B]1[/B] mesajını gönderin.", "shopify_order_summary_message": "<PERSON><PERSON>n [B]{{username}}[/B][BR][/BR]Sepetinizdeki Ürünler:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][B]Vergi :[/B]{{tax}}[BR][/BR][B]Genel Toplam :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adres:[/B]{{address}}[BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen [B]1[/B] mesajını gönderin.", "shopify_whatsapp_order_summary_message": "<PERSON><PERSON>n [B]{{username}}[/B][BR][/BR]Sepetinizdeki Ürünler:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][BR][/BR][B]Vergi :[/B]{{tax}}[BR][/BR][BR][/BR][B]Kargo Ücreti :[/B] {{cargo_option_fee}}[BR][/BR][BR][/BR]{{discounts}}[B]Genel Toplam :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adres:[/B]{{address}}[BR][/BR]", "whatsapp_order_summary_message": "Sayın [B]{{username}}[/B] Sepetinizdeki <PERSON>:[BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Kargo Bilgileri[/B][BR][/BR]Kargo Şirketi: {{cargo_option_name}}[BR][/BR]Kargo Ücreti: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Genel Toplam:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Fatura Adresi[/B][BR][/BR][B]Adres:[/B] {{invoice_address}}[BR][/BR][B]İlçe:[/B] {{invoice_district}}[BR][/BR][B]İl:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Teslimat Adresi[/B][BR][/BR][B]Adres:[/B] {{delivery_address}}[BR][/BR][B]İlçe:[/B] {{delivery_district}}[BR][/BR][B]İl:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Ödeme yöntemi:[/B] {{payment_type}}[BR][/BR][BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen alttaki butona tıklayınız.", "whatsapp_order_summary_message_cart_content": "Sayın [B]{{username}}[/B] Sepetinizdek<PERSON>: {{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Kargo Bilgileri[/B][BR][/BR]Kargo Şirketi: {{cargo_option_name}}[BR][/BR]Kargo Ücreti: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Genel Toplam:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Fatura Adresi:[/B][BR][/BR]{{invoice_address}}[BR][/BR]", "whatsapp_order_summary_message_invoice": "[B]İlçe:[/B] {{invoice_district}}[BR][/BR][B]İl:[/B] {{invoice_city}}[BR][/BR]", "whatsapp_order_summary_message_delivery_address": "[BR][/BR][B]Teslimat Adresi:[/B][BR][/BR]{{delivery_address}}[BR][/BR]", "whatsapp_order_summary_message_delivery": "[B]İlçe:[/B] {{delivery_district}}[BR][/BR][B]İl:[/B] {{delivery_city}}[BR][/BR]", "whatsapp_order_summary_message_approve": "[BR][/BR][B]Ödeme yöntemi:[/B] {{payment_type}}[BR][/BR][BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen alttaki butona tıklayınız.", "whatsapp_order_summary_message_partition": "<PERSON><PERSON>n [B]{{username}}[/B] Sepetinizdeki <PERSON>:[BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}", "whatsapp_order_summary_message_partition_2": "{{additional_cost}}[B]Kargo Bilgileri[/B][BR][/BR]Kargo Şirketi: {{cargo_option_name}}[BR][/BR]Kargo Ücreti: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Genel Toplam:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Fatura Adresi[/B][BR][/BR][B]Adres:[/B] {{invoice_address}}[BR][/BR][B]İlçe:[/B] {{invoice_district}}[BR][/BR][B]İl:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Teslimat Adresi[/B][BR][/BR][B]Adres:[/B] {{delivery_address}}[BR][/BR][B]İlçe:[/B] {{delivery_district}}[BR][/BR][B]İl:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Ödeme yöntemi:[/B] {{payment_type}}[BR][/BR][BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen alttaki butona tıklayınız.", "order_summary_message_partition_2": "{{additional_cost}}[B]Kargo Bilgileri[/B][BR][/BR]Kargo Şirketi: {{cargo_option_name}}[BR][/BR]Kargo Ücreti: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Genel Toplam:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Fatura Adresi[/B][BR][/BR][B]Adres:[/B] {{invoice_address}}[BR][/BR][B]İlçe:[/B] {{invoice_district}}[BR][/BR][B]İl:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Teslimat Adresi[/B][BR][/BR][B]Adres:[/B] {{delivery_address}}[BR][/BR][B]İlçe:[/B] {{delivery_district}}[BR][/BR][B]İl:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Ödeme yöntemi:[/B] {{payment_type}}[BR][/BR][BR][/BR]Yukarıda yer alan bilgiler ile siparişinizi onaylıyorsanız lütfen 1 yazınız.", "send_customer_password_created": "Üyeliğiniz başarıyla oluşturuldu, geçici[B]şifreniz: {{password}}[/B],istediğiniz zaman şifreyi değiştirebilirsiniz.✅", "send_customer_created": "Üyeliğiniz başarıyla oluşturuldu.✅", "send_order_status_message": "Sayın [B]{{customer_name}}[/B] [BR][/BR][B]Sipariş Numarası :[/B]{{order_code}}[BR][/BR][B]Sipariş Tarihi :[/B]{{order_date}}[BR][/BR][B]Sipariş Tutarı :[/B]{{order_total_price}}[BR][/BR][B]Kargo Firması :[/B]{{cargo_option_name}}[BR][/BR][B]Kargo Takip Kodu :[/B]{{cargo_tracking_code}}[BR][/BR][B]Sevkiyat Durumu :[/B]{{order_status}}[BR][/BR]{{cargo_tracking_url}}", "remittance_message": "[BR][/BR][B]IBAN[/B]: {{iban}}", "send_cargo_message": "Kargo seçenekleriniz aşağıda belirtilmiştir. Hangisini seçmek istiyorsanız onun yanındaki numarayı yazınız.[BR][/BR][BR][/BR]{{cargo_options}}", "whatsapp_send_cargo_message": "Kargo seçenekleriniz aşağıda belirtilmiştir. Lütfen birini seçiniz", "send_address_message_caption": "{{emoji}}[B]Adres :[/B] {{address}}[BR][/BR][B]Şehir :[/B] {{city}}[BR][/BR][B]İlçe :[/B] {{town}}", "send_address_message_caption_address": "[BR][/BR]{{emoji}}[B]Adres :[/B] {{address}}", "send_address_message_caption_city": "[BR][/BR][B]Şehir :[/B] {{city}}[BR][/BR][B]İlçe :[/B] {{town}}", "shopify_send_address_message_caption": "{{emoji}}[B]Adres :[/B] {{address}}[BR][/BR][B]<PERSON>lke :[/B] {{country}}[BR][/BR][B]Şehir :[/B] {{city}}[BR][/BR][B]İlçe :[/B] {{province}}", "general_order_note": "[BR][/BR][B]Sipariş Notu :[/B]{{general_order_note}}[BR][/BR]", "first_message": "Merhaba talebiniz alınmıştır. En kısa zamanda sizinle ilgileneceğiz.🛒", "discount_coupone_message": "[BR][/BR][BR][/BR][B]İndirim Fiyatı:[/B] {{discount_price}}[BR][/BR][BR][/BR]", "send_customer_confirmation_message": "Üyelik bilgileriniz aşağıda yer almaktadır.[BR][/BR][BR][/BR]{{customer_data}}{{terms_of_use_url}}{{gdpr_url}}[BR][/BR]Üyelik bilgilerini onaylıyor ve üyemiz olmak istiyorsanız, [B]onaylıyorum[/B] yazmanızı rica ederiz.", "send_payment_url": "Alışverişinizi kredi kartı ile tamamlayabilmek için aşağıdaki linkten ödeme işleminizi tamamlamanızı rica ederiz.[BR][/BR]{{payment_url}}", "yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "cart_approve_message": "<PERSON><PERSON><PERSON> sepetinizi onaylıyorsanız [B]1[/B] yazın lütfen.", "coupon_message": "[B]<PERSON><PERSON><PERSON> :[/B][SPACE]5[/SPACE]{{price_coupon}}[BR][/BR]", "send_out_of_hours_message": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>lorob<PERSON>'ya hoş geldiniz. Mesajınızı aldık ve 24 saat içinde size geri döneceğiz. Bizimle iletişime geçtiğiniz  için teşekkür ederiz.", "instagram_private_reply": "Bize ulaştığın<PERSON>z için teşekkürler, size nasıl yardımcı olabilirim?", "cash": "Nakit", "credit_card": "<PERSON><PERSON><PERSON>", "turkish_out_of_working_hours_message": "Mesai saati dışı mesajı.", "english_out_of_working_hours_message": "Mesai saati dışı mesajı (İngilizce)", "order_not_found_message": "Sipariş durumunu öğrenebileceğiniz bir sipariş mevcut değildir.", "confirm_select_address": "Merhaba aşağıdaki adresinizi onaylıyorsanız lütfen [B]{{index}}[/B] yazın.[BR][/BR]", "send_empty_address_message": "Seçebileceğiniz bir adres bulunmamaktadır. Lütfen adres bilgilerinizi iletin.", "edit": {"kvkk": "KVKK Sözleşmesi", "membership_registration_agreement": "Üyelik Kayıt Sözleşmesi", "customer_required": "Müşteri oluşturma onayı zorunlu olsun", "precision": "Virgülden sonra basamak sayısı", "show_brand_name": "Ürün listesinde marka gösterilsin", "show_product_new": "Ürün listesinde yeni bilgisi gösterilsin", "eft_options": "Havale Eft seçenekleri", "door_payment_options": "Ka<PERSON><PERSON>da Ödeme Ayarları", "order_status": "Sipariş Durumları", "ready_cargo": "<PERSON><PERSON>", "gave_to_cargo": "<PERSON><PERSON><PERSON>", "success_payment": "<PERSON><PERSON>me <PERSON>ld<PERSON>", "out_of_hours_message_settings": "Mesai Saati Dışı Mesaj <PERSON>ı", "out_of_hours_send_message": "Mesai saati dışında mesaj gönder", "out_of_hours_default_send_message": "Mesai saati dışında varsayılan mesajı gönder", "out_of_hours_message": "Mesai saati dışı mesajı.", "out_of_hours": "Mesai <PERSON>leri", "show_is_new": "<PERSON><PERSON>", "show_brand": "Markayı Göster", "perpage": "Ürün <PERSON>şımında Aynı Anda Kaç Ürün Gönderilsin", "order_created_message": "Sipariş oluşturuldu mesajı", "send_order_created_message": "Sipariş oluşturuldu mesajı gönderilsin mi?", "is_default_order_created_message": "Varsayılan sipariş oluşturuldu mesajı gönderilsin mi?", "manuel_discount_permission": "<PERSON>", "all": "<PERSON><PERSON>", "just_company_owner": "<PERSON><PERSON><PERSON>", "default_currency_code": "Varsayılan Para Birimi", "warn_unregister_purchase": "Üyeliksiz Müşteriler İçin Kredi Kartı İle Ödeme Yapamama Onay Mesajı Gönderilsin", "send_product_price_without_vat": "Ürün Paylaşımında Fiyatı KDV Hariç Gönder"}, "duplicate_member": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, sitemizde birden fazla üyeliğiniz bulunmmaktadır aşağıda bilgilerden hangileriyle devam etmek istersiniz ? [BR][/BR]{{duplicate_member}}", "duplicate_member_item": "{{emoji}} Email: {{email}} [BR][/BR] Telefon: {{phone_number}}", "tax": "<PERSON><PERSON><PERSON>", "kdv": "KDV", "arvia": {"created_room": "Görüntülü veya sesli konuşma için a<PERSON>ğıdaki link üzerinden bizimle görüşmeye başlayabilirsiniz.\n{{url}}"}, "only_one_discount": "Sadece 1 İndirim Kuponu Uygulanabilir", "credit_cart": "Kredi Kartı/Banka Kartı", "discounts": "<PERSON><PERSON><PERSON><PERSON>"}, "auth": {"forgot_password": {"form": {"title": "Parolanızı Sıfırlayın", "subtitle": "E-Posta adresini gir", "email": "<PERSON><PERSON><PERSON>", "enter_page": "<PERSON><PERSON><PERSON>"}, "accept": {"title": "<PERSON><PERSON>", "subtitle": "Şifre sıfırlama bağlantısını gönderdik , e-postanızı kontrol edin."}, "mail_template": {"subject_dear": "<PERSON><PERSON><PERSON>", "subject_description": "HeloRobo Web Parola Sıfırlama Maili", "email_confirm_description": "HeloRobo Mail Onaylama", "title": "HeloRobo", "email_confirm": "<PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON>", "description": "Sıfırlama Bağlantısı", "sub_description": "Merhaba Şifre sıfırlama bağlantınız mevcut aşağıdan buton'a tıklayarak şifrenizi değiştirebilirsiniz.", "email_confirm_sub_description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> ad<PERSON> onay<PERSON> için a<PERSON> butona tıklayınız.", "enter_set_new_password": "Şifre <PERSON>ırla", "email_confirm_button": "<PERSON><PERSON>"}, "password_change_request": "<PERSON><PERSON><PERSON><PERSON>", "password_change_link": "Hesabınız için şifre sıfırlama talebinde bulunduğunuzu aldık. Yeni bir şifre belirlemek için lütfen aşağıdaki bağlantıyı kullanın:", "warning_1": "<PERSON><PERSON><PERSON> bu talebi siz ya<PERSON>, lütfen bu e-postayı dikkate almayın. Hesabınız güvende kalacaktır.", "warning_2": "Şifre sıfırlama bağlantısı yalnızca 24 saat geçerlidir. Bu süre içerisinde bağlantıyı kullanarak şifrenizi sıfırlamanız gerekmektedir.", "warning_3": "<PERSON><PERSON><PERSON> bir sorunla ka<PERSON>, destek ekibimizle iletişime geçmekten çekinmeyin."}, "helorobo_bilgi_teknolojileri": "© Helorobo Bilgi Teknolojileri A.Ş"}, "success": {"integration": {"add_to_cart": "{{item_name}} adlı ürün sepetinize eklendi.", "empty_cart": "Sepetinizdeki tüm ürünler silindi.", "dec_item_from_cart": "adlı ürün sayısı azaltıldı.", "inc_item_from_cart": "adlı ürün sayısı artırıldı.", "item_removed_from_cart": "{{item_name}} adlı ürün sepetinizden çıkartıldı.", "order_message": "Siparişiniz başarıyla oluşturuldu.[BR][/BR]Sipariş Numarası :[B]{{orderId}}[/B][BR][/BR]Sayın [B]{{username}}[/B],[BR][/BR]{{orderTotalPrice}} değerindeki siparişinizi aldık.[BR][/BR]2-4 gün içerisinde teslim edilmesi bekleniyor. Siparişinizin durumunu öğrenmek için [B]Sipariş Durumu[/B] mesajını bize göndermeniz halinde, robotumuz sizi süreç ile ilgili bilgilendirecektir.", "order_message_manuel": "Siparişiniz başarıyla oluşturuldu.  Si<PERSON><PERSON><PERSON> :{{orderId}}   Sayın {{username}},  {{orderTotalPrice}} değerindeki siparişinizi aldık.  2-4 gün içerisinde teslim edilmesi bekleniyor. Siparişinizin durumunu öğrenmek için 'Sipariş Durumu' mesajını bize göndermeniz halinde, robotumuz sizi süreç ile ilgili bilgilendirecektir.", "order_message_credit_cart": "Siparişiniz başarıyla oluşturuldu.[BR][/BR]Sipariş Numarası :[B]{{orderId}}[/B][BR][/BR]Sayın [B]{{username}}[/B],[BR][/BR]{{orderTotalPrice}} değerindeki siparişinizi aldık.[BR][/BR] Siparişinizin Tamamlanması için Lütfen Gelen Mesajda Bulunan Link Üzerinden Siparişinizi Tamamlayınız. Daha Sonra Siparişinizin durumunu öğrenmek için [B]Sipariş Durumu[/B] mesajını bize göndermeniz halinde, robotumuz sizi süreç ile ilgili bilgilendirecektir.", "shopify_order_credit_cart_message": "Siparişiniz başarıyla oluşturuldu. Ödeme İşlemine Aşağıdaki linkten devam edebilirsiniz.[BR][/BR]{{url}}", "shopify_order_message": "Siparişiniz başarıyla oluşturuldu. Sipariş Detayına Aşağıdaki linkten ulaşabilirsiniz.[BR][/BR]{{url}}", "customer_address_message": "<PERSON><PERSON><PERSON><PERSON>, adres<PERSON>iniz aşağıda sıralı bir şekilde verilmiştir. Hangi adresi teslimat adresi olarak seçmek istersiniz ?[BR][/BR]", "livechat_customer_address_message": "<PERSON><PERSON><PERSON><PERSON>, ad<PERSON><PERSON><PERSON>z aşağıda sıralı bir şekilde verilmiştir. Hangi adresi teslimat adresi olarak seçmek istersiniz ?", "customer_only_address_message": "<PERSON><PERSON><PERSON><PERSON>, adres<PERSON>z aşağıda iletilmiştir. Bu adresi onaylıyor musunuz?[BR][/BR]", "customer_invoice_address_message": "<PERSON><PERSON><PERSON><PERSON>, adres<PERSON>iniz aşağıda sıralı bir şekilde verilmiştir. Hangi adresi fatura adresi olarak seçmek istersiniz ?[BR][/BR]", "livechat_customer_invoice_address_message": "<PERSON><PERSON><PERSON><PERSON>, ad<PERSON><PERSON><PERSON>z aşağıda sıralı bir şekilde verilmiştir. Hangi adresi fatura adresi olarak seçmek istersiniz ?", "payment_type": "<PERSON><PERSON><PERSON><PERSON>, ödeme seçenekleriniz aşağıda sıralı bir şekilde verilmiştir. Hangi ödeme seçeneğini kullanmak istersiniz? Lütfen [B]numara[/B] olarak belirtiniz.[BR][/BR][BR][/BR]{{payment_options}}", "whatsapp_payment_type": "<PERSON><PERSON><PERSON><PERSON>, ödeme seçenekleriniz seçeneklerde sıralı bir şekilde verilmiştir. Hangi ödeme seçeneğini kullanmak istersiniz? Lütfen birini seçiniz.", "whatsapp_payment_type_continue": "Ödeme seçeneklerinizin devamı burada verilmiştir. Hangi ödeme seçeneğini kullanmak istersiniz? Lütfen birini seçiniz.", "add_to_cart_children": "Ürün opsiyonları aşağıda belirtilmiştir. Hangisini seçmek istiyorsanız onun yanındaki numarayı yazınız.[BR][/BR]{{children_title}}[BR][/BR][BR][/BR]{{children}}[BR][/BR]", "whatsapp_add_to_cart_children": "Mevcut Opsiyonlar: {{children_title}}[BR][/BR][BR][/BR]seçeneklerinden birini seçiniz.", "livechat_add_to_cart_children": "Mevcut Opsiyonlar: {{children_title}}[BR][/BR]Seçeneklerinden birini seçiniz.", "add_to_cart_variant": "Ürün opsiyonları aşağıda belirtilmiştir. Hangisini seçmek istiyorsanız onun yanındaki numarayı yazınız.[BR][/BR]{{variant_title}}[BR][/BR][BR][/BR]{{variant}}[BR][/BR]", "whatsapp_add_to_cart_variant": "Mevcut Opsiyonlar: {{variant_title}}[BR][/BR][BR][/BR]seçeneklerinden birini seçiniz.", "livechat_add_to_cart_variant": "Mevcut Opsiyonlar: {{variant_title}}[BR][/BR]Seçeneklerinden birini seçiniz.", "gdpr_url_message": "[BR][/BR]KVKK sözleşmesini okudum kabul ediyorum.({{gdpr_url}})[BR][/BR]", "terms_of_use_url_message": "[BR][/BR]Üye Kayıt sözleşmesini okudum kabul ediyorum.({{terms_of_use_url}})[BR][/BR]", "has_been_changed_of_product_count": "Sepetinizdeki {{product_name}} ür<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adedi {{number}} o<PERSON><PERSON>", "arvia_chat_message": "Görüntülü veya Sesli Görüşme için <PERSON>ağıdaki Link Üzerinden Bizimle İletişime Geçebilirsiniz.\n{{url}}"}, "ask_form_questions": {"first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nedir ?", "last_name": "Soya<PERSON><PERSON><PERSON><PERSON><PERSON> nedir ?", "identity_number": "Lütfen TC numaranızı giriniz", "birth_date": "Doğum tarihiniz nedir ?", "email": "E-mail adresinizi yazabilir misiniz ?", "title": "<PERSON><PERSON> baş<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>z nedir? (ev, iş vb.)", "fullname": "Adınız ve soyadınız nedir ?", "address": "Adresinizi  yazar mı<PERSON>ı<PERSON> ?", "post_code": "Posta kodunu al<PERSON>r miyim ?", "phone_number": "Telefon numaranızı alabilir miyim ?", "password": "Şifreniz ne olsun istersiniz ?", "district_name": "Lütfen mahallenizi giriniz", "district_code": "İlçeniz nedir ?", "address_type": "<PERSON><PERSON> başlığı yazar mısınız?", "name": "<PERSON><PERSON><PERSON><PERSON>?", "company_name": "Şirket adı nedir?", "tax_office": "Vergi dairenizi yazar mısın<PERSON>?", "tax_number": "Vergi numaranızı yazar mısınız?", "zip": "Lütfen posta kodunuzu giriniz?", "province": "Lütfen eyaletinizi yazınız.", "other_phone": "Başka telefon numarası yazar mısınız?", "town_code": "Ka<PERSON>ba kodunu yazar mısınız?", "is_same_delivery_and_invoce_address": "Teslimat adresiniz başarıyla onaylandı. Bu adresi fatura adresi olarak seçmek istiyorsanız [B]1[/B] e istemiyorsanız [B]2[/B] ye basınız.", "whatsapp_is_same_delivery_and_invoce_address": "Teslimat adresiniz başarıyla onaylandı. Bu adresi fatura adresi olarak seçmek istiyor musunuz?", "gender": "Cinsiyetinizi yazar mısınız?", "invoice_type": "<PERSON>ura tipiniz nedir? (Bireysel veya kurumsal)", "country_name": "Lütfen ülkenizi giriniz", "city_name": "Lütfen ilinizi giriniz", "town_name": "Lütfen ilçenizi giriniz", "become_member": "Üyeliksiz alışveriş yaptığınız için kredi kartı ile ödeme yapamayacaksınız. Yine de devam etmek istiyorsanız [B]Evet[/B] yazınız.", "whatsapp_become_member": "Üyeliksiz alışveriş yaptığınız için kredi kartı ile ödeme yapamayacaksınız. Yine de devam etmek istiyorsanız lütfen onaylayın.", "get_next_page_for_products": "Diğer Ürünleri Görmek için [B]0[/B] Yazınız.", "whatsapp_get_next_page_for_products": "Diğer Ürünleri Görmek için Tıklayınız.", "birth_month": "Doğum ayınız nedir ?", "birth_year": "Doğum yılınız nedir ?", "birth_day1": "<PERSON><PERSON><PERSON> günü<PERSON>üz nedir ?", "mail_notify": "Mail bildirimi almak istermisiniz ?", "kvkk": "Üyelik sözleşmesini kabul ediyormusunuz ?"}}, "campaign": {"send_campaign": "<PERSON><PERSON><PERSON><PERSON>, kampanya seçenekleriniz aşağıda sıralı bir şekilde verilmiştir. Hangi kampanya seçeneğini kullanmak istersiniz? Lütfen [B]numara[/B] olarak belirtiniz. [BR][/BR][BR][/BR]{{campaign_items}}", "whatsapp_send_campaign": "<PERSON><PERSON><PERSON><PERSON>, kampanya seçenekleriniz aşağıda sıralı bir şekilde verilmiştir. Hangi kampanya seçeneğini kullanmak istersiniz?"}, "support": {"solved": "Çözüldü", "pending": "Bekliyor", "unsolved": "Çözülmedi", "in_process": "İşlemde", "normal": "Normal", "danger": "Acil"}, "customer_evaluation": {"evaluation_message": "Hizmetimizden memnun kaldınız mı? Deneyiminizi puanlayarak sizlere daha iyi hizmet sunmamıza yardımcı olabilirsiniz.", "evaluation_message_5": "Çok iyi 😍", "evaluation_message_4": "İyi 😊", "evaluation_message_3": "Orta 🙂", "evaluation_message_2": "Kötü 😑", "evaluation_message_1": "Çok kötü 😤", "evaluation_message_text": "Hizmetimizden memnun kaldınız mı? Deneyiminizi puanlayarak sizlere daha iyi hizmet sunmamıza yardımcı olabilirsiniz.[BR][/BR][BR][/BR]{{rows}}", "evaluation": "<PERSON><PERSON><PERSON>", "customer_evaluation_setting": "Müşteri Geri Bildirim Ayarı", "customer_evaluation_timeout": "Sipariş Sonrasında Müşteri Geri Bildirim Ayarı Süresi (Dakika)", "evaluation_type_error": "Değerlendirme tipi geçersiz.", "evaluation_options": "<PERSON><PERSON><PERSON><PERSON>", "evaluation_option_1": "Satış tamamlandıktan sonra", "evaluation_option_2": "Müşteri arşivlendikten sonra"}, "agent_report": {"not_found": "<PERSON><PERSON>", "date_invalid": "<PERSON><PERSON><PERSON>"}, "thinker": {"now": "Thinker'ı Mesaj Göndererek Başlat", "wait": "Thinker'ı Müşteri Mesaj Gönderdikten Sonra Başlat", "start_type": "Thinker <PERSON><PERSON><PERSON><PERSON>", "flow_id": "Thinker <PERSON><PERSON><PERSON><PERSON>", "status": "Thinker <PERSON><PERSON><PERSON>", "thinker": "Thinker"}, "chat_action": {"action_not_found": "Action bulunamadı", "system": "sistem", "customer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "moved_to_public": "Sohbet {{name}} tarafından be<PERSON>ene alındı", "system_moved_to_public": "Sohbet sistem tarafından bekleyene alındı", "archived_by_system": "Sohbet sistem tarafından arşive alındı", "chat_hide": "Sohbet {{name}} tarafından arşive alındı", "system_chat_hide": "Sohbet {{name}} tarafından arşive alındı", "block": "Sohbet {{name}} tara<PERSON><PERSON><PERSON>n engellendi", "unblock": "So<PERSON><PERSON><PERSON> {{name}} tarafından engeli kaldırıldı", "system_block": "Sohbet {{name}} tara<PERSON><PERSON><PERSON>n engellendi", "assign_to_team": "Sohbet {{name}} tarafından '{{team}}' ekibine atandı", "assign_to_agent": "Sohbet {{name}} tarafından '{{assigned_user}}' operatör<PERSON>ne atandı", "add_chat_tag": "Kişiye {{name}} tarafından '{{tag}}' et<PERSON><PERSON> e<PERSON>", "delete_chat_tag": "Kişinin {{name}} tarafından '{{tag}}' et<PERSON><PERSON> si<PERSON>i", "update_user_profile": "Kişinin {{name}} tarafından profil bilgileri gü<PERSON>di", "started_flow": "Sohbet {{name}} tarafından Thinker'a aktarıldı", "stopped_flow": "<PERSON><PERSON><PERSON><PERSON> {{name}} tarafından Thinker akı<PERSON><PERSON> durduruldu", "received_chat": "Sohbet {{name}} tarafından alındı", "owner_forced_chat": "Sohbet {{name}} tarafından zorla alındı", "customer_assign_to_team": "Sohbet {{name}} tarafından ekibe yönlendirildi", "user_profile_customer_note": "Müşterinin '{{old_customer_note}}' olan not bilgisi {{name}} tarafından '{{new_customer_note}}' o<PERSON><PERSON> gü<PERSON>", "user_profile_email": "Müşterinin '{{old_email}}' olan email bilgisi {{name}} tarafından '{{new_email}}' o<PERSON><PERSON> g<PERSON>", "user_profile_first_name": "Müşterinin '{{old_first_name}}' olan isim bilgisi {{name}} tarafından '{{new_first_name}}' o<PERSON><PERSON>", "user_profile_last_name": "Müşterinin '{{old_last_name}}' olan soyisim bilgisi {{name}} tarafından '{{new_last_name}}' o<PERSON><PERSON>", "user_profile_phone_number": "Müşterinin '{{old_phone_number}}' olan telefon bilgisi {{name}} tarafından '{{new_phone_number}}' o<PERSON><PERSON> g<PERSON>", "add_new_address": "{{name}} tarafından '{{address_title}}' ad<PERSON><PERSON>", "edit_address": "{{name}} tarafından '{{address_title}}' <PERSON><PERSON><PERSON>", "delete_address": "{{name}} tarafından '{{address_title}}' ad<PERSON>i silindi", "active_multi_chat_hide": "{{name}} tarafından sohbet toplu arşivleme özelliği ile arşivlendi", "selected_address": "{{name}} tarafından '{{address_title}}' ad<PERSON><PERSON>", "selected_cargo": "{{name}} tarafından '{{cargo_title}}' ka<PERSON><PERSON> se<PERSON>il<PERSON>", "selected_payment": "{{name}} tarafından '{{payment_title}}' ödeme yöntemi seçildi", "deleted_agent_pending_list": "{{name}} tarafından {{deleted_agent_name}} operatörü silindiği için sohbet bekleyen listesine aktarıldı", "deleted_agent_archived_list": "{{name}} tarafından {{deleted_agent_name}} operatörü silindiği için sohbet arşiv listesine aktarıldı", "noc_moved_to_public": "<PERSON><PERSON><PERSON> bekleyen listesine atıldı", "campaign_code_applied": "{{name}} tarafından '{{coupone_code}}' indirim kodu <PERSON>ı", "campaign_activated": "{{name}} tarafından '{{campaign_name}}' kampanyası uygulandı", "campaign_remove": "{{name}} tarafından '{{campaign_name}}' kampanyası kaldırıldı", "thinker_bot_stopped": "Thinker <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bot durd<PERSON><PERSON>u", "pinned": "{{name}} tara<PERSON><PERSON><PERSON>n sohbet sabit<PERSON>di", "remove_archive": "Sohbet {{name}} tarafından arşivden çıkarıldı", "mark_as_unread": "{{name}} tarafından sohbet okunmadı olarak işaretlendi", "mark_as_read": "{{name}} tarafından sohbet okundu olarak işaretlendi", "started_helobot": "{{name}} tarafından sohbet Helobot'a aktarıldı", "stopped_helobot": "{{name}} tarafından sohbet Helobot'tan <PERSON>ı", "system_stopped_helobot": "Helobot sistem tarafından durd<PERSON>ldu", "system_stopped_helobot_error": "Helobot'ta oluşan '{{error}}' hatası sebebi ile Helobot sistem tarafından durduruldu", "connected_user": "Müşteri Bağlandı", "disconnected_user": "Müşteri Bağlantısı Sonlandı.", "website_action": "Müşteri Şu anda -> '{{website}}' <PERSON><PERSON><PERSON><PERSON>nda", "helobot_timeout": "Helobot timeout se<PERSON><PERSON><PERSON> du<PERSON>", "thinker_flow_timeout": "Thinker <PERSON><PERSON><PERSON><PERSON><PERSON>, zaman aşımına uğradı.", "timeout_flow_start": "Thineker akışı sonlandı ve zaman aşımı akışı başladı."}, "translate": {"text_invalid": "Text geçersiz", "target_language": "Dönüştürülecek dil geçersiz"}, "message": {"customer_not_replied_message": "Uzun süre sizden yanıt alamadığımız için oturumunuz sonlandırılmıştır.", "archived_message_text": "Size nasıl yardımcı olabiliriz?"}, "user_log": {"edit_user": "{{agent_name}} tarafından {{user_name}} kullanıcısı düzenlendi.", "new_user": "{{name}} kullanıcısı eklendi. (Email : {{email}}, Type: {{type}})", "delete_user": "{{name}} kullanıcısı silindi.", "create_company": "{{agent_name}} k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {{name}} is<PERSON><PERSON> ve {{phone_number}} telefon numaralı şirketi oluşturdu.", "status_change_company": "{{agent_name}} k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {{name}} is<PERSON>li şirketin durumunu {{status}} olarak değiştirdi.", "edit_company": "{{agent_name}} kullanıcısı {{company_name}} isimli şirketin bilgilerini değiştirdi.", "create_channel": "{{agent_name}} kullanıcısı {{ext_id}} numaralı kanalı kurdu.", "delete_channel": "{{agent_name}} kullanıcısı {{name}} is<PERSON><PERSON> kanalı sildi.", "edit_channel": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanalın bilgilerini değiştirdi.", "archived_channel_chats": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanalın chatlerini arşivledi.", "archived_channel_chats_with_date": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanal<PERSON>n {{start_date}} ve {{end_date}} tarih arasındaki chatlerini arşivledi.", "remove_token_for_facebook": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanalın access_tokeni ni sildi.", "remove_credit_line": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanalın ödeme yöntemini kaldırdı.", "add_credit_line": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanala ödeme yöntemi ekledi.", "add_integration": "{{agent_name}} k<PERSON><PERSON>ıcısı {{integration_name}} is<PERSON><PERSON> ve {{type}} tipinde entegrasyon ekledi.", "edit_integration": "{{agent_name}} kullanıcısı {{integration_name}} is<PERSON><PERSON> entegra<PERSON>.", "login": "{{agent_name}} kullanıcısı sisteme Giriş yaptı.", "logout": "{{agent_name}} kullanıcısı sistemden Çıkış yaptı.", "message_export": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanal<PERSON>n {{start_date}} ve {{end_date}} tarih arasındaki mesajların export etmeye başladı.", "channel_status": "{{agent_name}} kullanıcısı {{channel_name}} is<PERSON><PERSON> kanalın durumunu {{status}} olarak değiştirdi.", "profile_edit": "{{agent_name}} kullanıcısı kendi bilgilerini güncelledi", "add_tag": "{{agent_name}} kullanıcısı {{tag_name}} is<PERSON><PERSON><PERSON>.", "edit_tag": "{{agent_name}} kullanıcısı {{tag_name}} is<PERSON><PERSON><PERSON>", "thinker_register": "{{agent_name}} kullanıcısı Thinker'ı Aktif etti.", "helobot_register": "{{agent_name}} kullanıcısı Helobot'u Aktif etti.", "mail_register": "{{agent_name}} kullanıcısı Mail Sistemini Aktif etti.", "message_template_register": "{{agent_name}} kullanıcısı Message Template'i Aktif etti.", "hepsiburada_register": "{{agent_name}} kullanıcısı Hepsiburada'yı Aktif etti.", "trendyol_register": "{{agent_name}} kullanıcısı Trendyol'u Aktif etti.", "pazarama_register": "{{agent_name}} kullanıcısı Pazarama'yı Aktif etti.", "n11_register": "{{agent_name}} kullanıcısı N11'i Aktif etti."}}, "Worker": {"run": {"job_not_found": "İş bulunamadı.", "job_data_not_found": "İş datası bulunamadı.", "job_type_not_implemented": "İş tipi implemente edilemedi.", "channel_not_implemented": "Kanal implemente edilmedi"}}, "Noc": {"errors": {"channel": {"id_not_found": "Kanal id bulunamadı", "not_found": "Kanal Bulunamadı", "missing": "Kanal Bilgileri Eksik", "provider_not_found": "Provider bil<PERSON><PERSON> bulu<PERSON>ı", "type_not_found": "Tip bilgisi bulu<PERSON>ı", "name_not_found": "İsim bilgisi Bulunamadı", "provider_not_support": "Şuan için bu kanal provider desteklenmemektedir.", "type_not_support": "<PERSON>uan i<PERSON> bu kanal tipi desteklenmemktedir.", "allready_exist": "Kanal zaten atanmış durumda", "access_token_not_found": "access_token bulunamadı", "ext_id_not_found": "ext_id parametresi bulunamadı", "page_id_not_found": "Say<PERSON> id si bulunamadı", "is_active": "Aktiflik veya Pasiflik Seçiniz", "limit_invalid": "Lütfen geçerli bir limit değeri giriniz.", "unlimit_invalid": "Unlimit bilgisi geçersiz.", "no_right_add_channels": "Yeni kanal ekleme hakkınız bulunmamaktadır.", "credit_line_exists": "Credit line zaten mevcut"}, "container": {"code": "Konteyner Kodu Bulunamadı", "subdomain": "Subdomain Bulunamadı", "not_login": "Konteynıra Giriş Yapılamadı.", "not_found_certificate": "Sertifika Bulunamadı.", "method": "Kod Gönderme Metodu Bulunamadı.", "invaid_method": "Geçersiz Onay Gönderme Methodu.", "exist": "Bu container zaten var", "invalid_phone": "Lütfen Özel Karakter <PERSON>ı<PERSON>", "not_found_phone": "Telefon numarası gönderiniz", "id_not_found": "Konteyner id bulunamadı", "not_found": "Konteyner Bulunamadı", "missing": "Konteyner Bilgileri Eksik", "cant_get_container": "Konteynerlar Alınamadı"}, "company": {"id_not_found": "Şirket id bulunamadı", "not_found": "Şirket bulunamadı", "missing": "Şirket Bilgileri Eksik", "number_allready_using": "Şirket Adı veya Telefon Numarası zaten kullanılıyor. Lütfen başka bir numara veya isim <PERSON>.", "name_not_found": "Şirket Adı zorunludur", "phone_not_found": "Telefon Numarası Zorunludur", "limit_number_small_count_agent": "Girilen limit sayısı mevcut agent say<PERSON><PERSON><PERSON><PERSON><PERSON> küçüktür."}, "integration": {"login": "Belirtilen Sisteme Giriş Yapılamadı", "baseurl": "Lütfen Müşterinin BaseURL bilgisi giriniz. örnek: https://example.com", "username": "Web Servis Kullanıcı Adı Giriniz", "password": "Web Servis Kullanıcı Şifresi Giriniz", "not_found": "Entegrasyon bulunamadı", "missing": "Entegrasyon Bilgileri Eksik", "service_missing": "Entegrasyon Web Servis Bilgileri Eksik", "baseurl_not_found": "<PERSON><PERSON> bulu<PERSON>ı", "type_not_found": "Tip Bulunamadı", "link_error": "Lütfen Link Sonundaki '/' işaretini siliniz", "settings_key_error": "<PERSON><PERSON>lar Anahtarında Hata Oluştu", "login_key": "Login key hatalı"}, "auth": {"login_not_found": "Kullanıcı Bulunamadı", "no_noc_user": "Kullanıcı NOC kullanıcısı değil", "active": "Kullanıcı aktif <PERSON>", "bcrypt_error": "Kullanıcı şifresi bulunamadı", "no_permission": "Kullanıcı yetkisi bulunamadı", "allready_email": "<PERSON><PERSON> <PERSON><PERSON>"}, "company_log": {"setup_id": "id Bilgisi Bulunamadı", "not_found": "Kayıt Bulunamadı", "allready_exist": "Bu Kayıt için İşlemler Zaten Yapılmış"}, "user": {"not_found": "Kullanıcı Bulunamadı", "missing": "Kullanıcı Bilgileri Eksik", "email_regex": "Geçersiz Email <PERSON>", "username_not_found": "Kullanıcı Adı Bulunamadı", "password_not_found": "Şifre Bulunamadı", "email_exist": "Bu <PERSON><PERSON> zaten kullanılıyor", "email_not_found": "<PERSON><PERSON> b<PERSON>"}, "embedded_signup": {"short_live_token_not_found": "Token bulunamadı", "data_not_found": "<PERSON><PERSON><PERSON> bulunamadı", "business_management_not_found": "Business Management id bilgisi Bulunamadı. Yetkileri Kontrol Ediniz", "whatsapp_business_management_not_found": "Whatsapp Business Management id Bilgi bulunamadı. Yetkileri Kontrol Ediniz", "not_found_whatsapp_numbers": "Kullanıcının Whatsapp Business Üzerinde Kayıtlı Numarası Bulunamadı."}, "report": {"period_not_found": "Period Seçiniz"}, "mobile": {"id_not_found": "Version ID bulunamadı.", "changelog_invalid": "Changelog geçersiz.", "version_invalid": "Version geçersiz.", "force_upgrade_invalid": "Force upgrade geçersiz."}, "version": {"name_invalid": "Name alanı geçersiz", "text_invalid": "Text alanı geçersiz", "id_invalid": "Id al<PERSON><PERSON> geçersiz", "version_not_found": "Version kaydı bulunamadı"}, "ads_medias": {"not_found": "<PERSON><PERSON>m Bilgisi bulunamadı", "id_not_found": "id Bilgisi Geçersiz", "name_not_found": "name bilgisi Geçersiz", "url_not_found": "url bilgisi Geçersiz", "redirect_url_not_found": "redirect_url bilgisi Geçersiz", "is_active_not_found": "is_active bilgisi Geçersiz", "new_index_not_found": "new_index bilgisi Geçersiz"}, "dashboard": {"start_date_invalid": "Başlangıç tarihi geçersiz.", "end_date_invalid": "Bitiş tarihi geçersiz.", "date_range": "Tarih aralığı en az 1 gün en fazla 30 gün olabilir.", "sort_invalid": "Sort değeri geçersiz", "sort_field_invalid": "sort field geçersiz", "sort_value_invalid": "sort value geçersiz"}, "message": {"contents_not_found": "Mesaj contenti bulunamadı"}}, "onboarding_wizards": {"long_lived_token": "Uzun Süreli <PERSON>", "connected_instagram_account": "Bağlı Instagram Hesabı", "name": "İsim", "username": "Kullanıcı Adı", "profile_picture_url": "<PERSON>il <PERSON>", "display_phone_number": "Telefon Numarası", "wp_status": "<PERSON><PERSON><PERSON>", "customer_business_name": "Müşteri İşletme Adı", "customer_business_link": "Müşteri İşletme Linki", "certificate": "Ser<PERSON><PERSON><PERSON>"}}, "UnNameUser": "İsimsiz <PERSON>üşteri", "Onboarding": {"errors": {"conversation_count": "Aylık sohbet sayısı geçersiz.", "package_price_not_found": "Paket ücret bilgisi geçersiz.", "name_not_found": "Lütfen İsim Giriniz.", "phone_not_found": "Lütfen Geçerli Bir Telefon Numarası Giriniz.", "invalid_email": "Lütfen Geçerli Bir Email Adresi Giriniz.", "code_not_found": "code Bilgisi Zorunludur.", "onboarding_wizard_id_not_found": "id Bil<PERSON>i <PERSON>ludu<PERSON>.", "embeddedInfo_not_found": "Onboarding Kaydı Bulunamadı.", "onboardingWizard_not_found": "Onboard Kaydı Bulunamadı.", "invalid_privacy_policy": "Gizlilik Politikası Seçimi Zorunludur", "invalid_pdpa": "KVKK Seçimi Zorunludur", "newsletter": "E-Bülten Seçimi Zor<PERSON>ludur", "email_valid": "Bu <PERSON><PERSON> Z<PERSON>n Kullanılıyor", "service_type_not_found": "<PERSON><PERSON>i Bilgisi Zor<PERSON>ludur.", "integration_type_not_found": "Entegrasyon Bilgisi Bulunamadı.", "package_id_not_found": "Paket id Bulunamadı", "features_ids_not_found": "Özellik Bilgileri Bulunamadı", "package_not_found": "Paket Bulunamadı", "id_not_found": "id Bilgisi Bulunamadı", "access_token_not_found": "access_token Bilgisi Bulunamadı", "instagram_account_not_exist": "İnstagram Hesabınız Bulunamadı. Lütfen en az 1 instagram hesabı ve facebook sayfası seçiniz ya da gerekli izinlere onay veriniz.", "facebook_account_does_not_exist": "Facebook Hesabınız Bulunamadı. Lütfen en az 1 facebook sayfası seçiniz ya da gerekli izinlere onay veriniz.", "wrong_instagram_account_selected": "Tekrar bağlamaya çalıştığınız instagram hesabı, kurulu olan instagram hesabından farklıdır. Lütfen {{name}} ismindeki instagram hesabınızı seçin yada destekle iletişime geçin.", "wrong_facebook_account_selected": "Tekrar bağlamaya çalıştığınız facebook hesabı, kurulu olan facebook hesabından farklıdır. Lütfen {{name}} ismindeki facebook hesabınızı seçin yada destekle iletişime geçin.", "token_invalid_or_missing_permission": "Token geçersiz yada yetersiz izinleri var.", "facebook_error_message": "Facebook Hata Mesajı: {{message}}.", "missing_scopes": "<PERSON><PERSON><PERSON>: {{scopes}}.", "missing_granular_scopes": "Eksik Spesifik izinler: {{granularScopes}}.", "company_name_not_found": "Şirket Adı Bulunamadı", "tax_office_not_found": "Vergi Dairesi Bulunamadı", "tax_or_identity_number_not_found": "Vergi Numarası Bulunamadı", "pdpa_not_found": "KVKK Sözleşmesini Kabul Ediniz", "select_package": "Lütfen Paket Seçiniz", "not_get_facebook_token": "Facebook token alınamadı", "not_get_facebook_fields": "Facebook üzerinden bilgileriniz alınamadı", "info_is_not_yet": "Yetersiz Bilgi. Lütfen Önceki Aşamaları Tamamlayınız", "container_problem": "Kontainer <PERSON>ula<PERSON>", "onboarding_wizard_type": "Onay Ko<PERSON> Gönderme Yöntemi Seçiniz", "onboarding_wizard_code": "<PERSON>ay <PERSON>", "phone_number_valid": "Bu Telefon Numarası Zaten Kullanılıyor", "whatsapp_number_not_found": "Whatsapp Numarası Yok", "unauthorized_action": "Yetkisiz İşlem", "allready_exists_company": "Bu Şirket Zaten Kayıtlı", "please_try_again": "<PERSON><PERSON><PERSON><PERSON>", "invalid_token_error": "<PERSON><PERSON>. {{page_name}} Sayfanızı Tekrar Bağlanmayı Deneyin. {{page_name}} Sayfanız ile Bağlantı Kurulumadı.", "session_not_found": "session bulunamadı", "self_onboard_not_found": "Self onboarding yapılmamış", "phone_number_id_invalid": "Telefon numarası id geçersiz", "waba_id_invalid": "Waba id geçersiz", "package_allready_exists": "Aktif Paket Zaten Var."}, "info": {"whatsapp_process": "Kurulumunuz devam etmektedir. 2-4 iş günü içerisinde sizinle iletişime geçilecektir.", "login_data": "<PERSON><PERSON><PERSON><PERSON><PERSON> Helo<PERSON>o tarafında kaydınız başarıyla alınmıştır. Aşağıdaki bilgilerle giriş yapabilirsiniz.<br/><br/>Email: {{email}}<br/>Şifre: {{password}}", "whatsapp_email_header": "HeloRob<PERSON>", "login_header": "HeloRobo Giriş İçin Gerekli Olan Bilgiler"}}, "Package": {"starter": "Başlangıç", "instagram": "Instagram", "tariff": "Aylık", "control_panel": "Helorobo (Yönetim Paneli)", "waba": "WhatsApp Business API", "faba": "Facebook Messenger Kullanıcısı", "facebook_eshop": "Facebook eShops", "instagram_eshop": "Instagram eShops", "message": "1.000 Kısa Mesaj", "template_message": "Ş<PERSON><PERSON>", "shop_integration": "e<PERSON><PERSON><PERSON>", "message_package": "<PERSON><PERSON><PERSON> p<PERSON>", "heloscope": "Heloscope"}, "Advermind": {"token_not_found": "Lütfen Kullanıcı Girişi Yapınız", "accesstoken_not_found": "Advermind Access Token Bulunamadı", "campaign_id_not_found": "Kampanya id Bilgisi Bulunamadı", "campaign_status_not_found": "Kampanya Durum Bilgisi Bulunamadı", "not_found_facebook_page": "Bu İşlemi Yapabilmek için HeloRobo Tarafından Facebook Sayfanızın Kaydı Olmak Zorundadır"}, "Mobile": {"not_found_version": "Version Bilgisi Bulunamadı"}, "Api": {"webhook": {"url_invalid": "<PERSON><PERSON> g<PERSON>", "hash_invalid": "Hash geçersiz"}, "message": {"content_caption_invalid": "Content caption geçersiz", "context_url_invalid": "Content url geçersiz", "type_invalid": "Type geçersiz", "chat_id_invalid": "Konuşma id geçersiz", "channel_id_invalid": "Kanal id geçersiz"}}, "Thinker": {"errors": {"thinker_not_found": "Thinker ka<PERSON><PERSON> b<PERSON><PERSON>", "name_invalid": "Name geçersiz", "message_url_id_invalid": "Message url id geçersiz", "flow_id_invalid": "Flow id geçersiz", "start_type_invalid": "Start type geçersiz", "url_invalid": "<PERSON><PERSON> g<PERSON>", "hash_invalid": "Hash geçersiz", "id_invalid": "id geçersiz", "allready_started": "Bu Müşteri Şu anda Zaten Bir Akışta", "not_found": "Bu Müşteriye ait Aktif Akış Bulunamadı", "channel_not_found": "Lütfen Kanal Seçiniz"}, "stopped": "Akış Durduruldu"}, "MessageTemplate": {"errors": {"message_template_not_found": "Message Template kaydı bulunamadı", "company_admin_required": "Lütfen Önce Şirket Sahibi Oluşturun. <PERSON><PERSON>"}}, "Reseller": {"errors": {"hash_invalid": "hash bilgisi geçersiz", "timestamp_invalid": "timestamp geçersiz", "reseller_key_invalid": "reseller_key bilgisi geçersiz", "company_name_invalid": "company_name bilgisi geçersiz", "company_phone_invalid": "company_phone bilgisi geçersiz", "allready_exists_company": "Bu Şirket Zaten Kayıtlı"}}, "Mail": {"errors": {"mail_not_found": "Mail kaydı bulunamadı"}}}