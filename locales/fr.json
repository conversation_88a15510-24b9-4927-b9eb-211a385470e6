{"Global": {"chat_message": {"add": "Ajouter", "add_cart": "Ajouter un panier", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options": "Options", "select_address": "Sélectionnez l'adresse", "select_cargo": "Sélectionnez <PERSON>", "select_payment": "Sélectionnez Paiement", "select_campaign": "Sélectionnez la campagne", "confirm": "Approuver", "confirm_cart": "Approuver le panier", "next": "Prochain", "yes": "O<PERSON>", "no": "Non", "empty_basket": "Prendre une action rétrospective sur un produit expiré ou acheté.", "no_exist_product": "Aucun produit à afficher", "order_status": "Statut de la commande", "cart": "Confirmer pour ajouter au panier", "select_account": "liste de comptes", "confirm_order": "Confirmer la commande", "thanks": "<PERSON><PERSON><PERSON>", "hello": "Bonjour"}, "errors": {"two_fa_code_not_found": "Code 2FA introuvable. Veuillez saisir un code 2FA.", "two_fa_code_invalid": "Code 2FA invalide. Veuillez saisir un code 2FA valide.", "two_fa_code_string": "Format de code 2FA invalide. Veuillez entrer un code 2FA à 6 chiffres.", "two_fa_not_found": "Les informations twoFA_status sont introuvables.", "two_fa_allready_open": "2FA Déjà Activée.", "two_fa_allready_close": "2FA Déjà Désactivée.", "token_required": "Des informations sur les jetons sont requises pour ce processus.", "token_not_found": "Le détail du jeton n'a pas pu être obtenu.", "token_deleted": "Le jeton a été supprimé.", "token_hash_not_some": "Le hachage de jeton n'est pas le même.", "token_expired": "Le jeton est obsolète.", "page_not_found": "Page non trouvée", "not_authorized": "Vous n'êtes pas autorisé à effectuer cette transaction.", "package_not_authorized": "Vous ne pouvez pas effectuer cette opération avec le paquet que vous utilisez. Veuillez mettre à jour le paquet.", "package_expire_not_authorized": "La période définie pour cette opération dans votre forfait est expirée. Veuillez mettre à niveau votre forfait.", "package_expire": "Votre forfait a expiré. Veuillez renouveler votre forfait.", "not_permissions": "Vous n'êtes pas autorisé à effectuer cette transaction.", "conversation_package": "Veuillez acheter un forfait de messages supplémentaires.", "company_not_found": "Aucune entreprise trouvée.", "company_waba_not_found": "ID Waba de l'entreprise introuvable", "channel_waba_not_found": "ID Waba introuvable de la chaîne. Veuillez contacter l'administrateur système.", "company_id_not_found": "L'identifiant de l'entreprise est requis.", "integration": {"address_not_found": "Le paramètre addressId est obligatoire.", "not_found": "Intégration introuvable.", "couponcode_not_found": "Le code du coupon de réduction n'a pas été trouvé."}, "chat": {"pinned_chat_max_count": "V<PERSON> pouvez <PERSON>ler jusqu'à 50 clients.", "chat_not_found": "Nom d'utilisateur Facebook"}, "whatsapp_message_error": "Une erreur inattendue s'est produite du côté de Whatsapp.", "instagram_message_error": "Une erreur inattendue s'est produite du côté de Instagram.", "pagination_not_found": "Aucune information de pagination trouvée", "page_count_not_found": "Numéro de page introuvable.", "perpage_not_found": "Nombre de pagination introuvable.", "secret_key_not_found": "Aucun code de sécurité trouvé", "secret_key_error": "Code de sécurité incorrect", "username_not_found": "Email non trouvé", "password_not_found": "Mot de passe introuvable", "chat_list_not_found": "Veuillez sélectionner la liste de chat", "billtekrom_account_id_not_found": "ID de compte Billtekrom introuvable dans cette entreprise", "billtekrom_account_not_found": "Aucun compte Billtekrom trouvé dans cette entreprise", "status": "informations sur le statut introuvables", "platform": "informations sur la plateforme introuvables", "not_supported": "Cette fonctionnalité n'est pas prise en charge"}, "form_field": {"first_name": "Nom", "last_name": "Nom de famille", "fullname": "nom complet", "email": "E-mail", "phone": "Numéro de téléphone", "account_info": "Informations sur l'adhésion", "address_info": "Informations sur l'adresse", "address1": "<PERSON><PERSON><PERSON>", "city": "Ville", "province": "Province", "zip": "<PERSON><PERSON><PERSON>", "country": "Pays", "company": "Nom de la Compagnie", "password": "le mot de passe", "gender": "Le genre", "birth_date": "Date d'anniversaire", "birth_month": "<PERSON><PERSON> de naissance", "birth_year": "<PERSON><PERSON> de naissance", "district": "District", "address": "<PERSON><PERSON><PERSON>", "mail_notify": "Notification par courrier", "yes": "O<PERSON>", "no": "Non", "woman": "<PERSON>mme", "man": "<PERSON><PERSON>", "member_contract": "Accord d'ad<PERSON><PERSON><PERSON>", "address_type": "Type d'adresse", "individual": "Individuelle", "institutional": "Institutionnelle", "address_title": "<PERSON><PERSON><PERSON>", "identity_number": "Numéro d'identité", "post_code": "Code postal", "tax_administration": "L'administration des impôts", "tax_number": "Numéro d'identification fiscale", "nationality": "je ne suis pas citoyen turc", "all": "<PERSON>ut"}, "language_code": {"tr": "<PERSON><PERSON>", "ar": "<PERSON><PERSON>", "fr": "Français", "de": "Allemand", "en": "<PERSON><PERSON><PERSON>", "ru": "<PERSON><PERSON>", "default": "Défaut"}}, "Site": {"validators": {"login": {"email_field_required": "Le champ de courrier est manquant.", "mail_lenght": "La longueur de l'espace de courrier est incorrecte", "password_field_required": "Le champ du mot de passe est obligatoire."}, "add_new_user": {"name_field_required": "L'espace de noms est requis.", "name_length": "La longueur de l'espace de nom est incorrecte", "email_field_required": "Le champ de courrier est manquant.", "not_valid_email": "Le champ de courrier est incorrect.", "mail_lenght": "La longueur de l'espace de courrier est incorrecte", "password_field_required": "Le champ du mot de passe est obligatoire.", "password_repeat_required": "Le champ de répétition du mot de passe est obligatoire."}, "user_update": {"name_field_required": "L'espace de noms est requis.", "name_length": "La longueur de l'espace de nom est incorrecte"}, "forgot_password": {"email_field_required": "Le champ de courrier est manquant.", "not_valid_email": "Le champ de courrier est incorrect."}, "set_new_password": {"new_password_field_required": "Le champ du mot de passe est obligatoire.", "new_password_length": "La nouvelle longueur du champ Mot de passe est incorrecte.", "new_password_repeat_field_required": "Le champ de répétition du mot de passe est obligatoire.", "new_password_repeat_length": "La nouvelle longueur du champ Mot de passe est incorrecte.", "passwords_are_not_equal": "Les mots de passe que vous avez entrés ne correspondent pas."}, "edit_user": {"name_field_required": "L'espace de noms est requis.", "email_field_required": "Le champ de courrier est manquant.", "new_password_field_required": "Le champ du mot de passe est obligatoire.", "new_password_repeat_field_required": "Le champ de répétition du mot de passe est obligatoire.", "passwords_are_not_equal": "Les mots de passe que vous avez entrés ne correspondent pas."}, "update_profile": {"name_field_required": "L'espace de noms est requis.", "password_field_required": "Le champ du mot de passe est obligatoire.", "password_repeat_required": "Le champ de répétition du mot de passe est obligatoire.", "passwords_are_not_equal": "Les mots de passe que vous avez entrés ne correspondent pas."}, "contact_form_message": {"first_name_required": "L'espace de noms est requis.", "name_length": "La longueur de l'espace de nom est incorrecte", "last_name_required": "L'espace de nom de famille est requis.", "last_name_length": "La longueur de l'espace de nom de famille  est incorrecte", "email_field_required": "Le champ de courrier est manquant.", "not_valid_email": "Le champ de courrier est incorrect.", "mail_length": "La longueur de l'espace de courrier est incorrecte", "subject_field_required": "Le champ de titre est obligatoire.", "subject_length": "La longueur de l'en-tête est incorrecte.", "content_field_required": "Le champ de contenu est obligatoire.", "content_field_length": "La longueur de la zone de contenu est incorrecte."}, "try_form_message": {"first_name_required": "L'espace de noms est requis.", "name_length": "La longueur de l'espace de nom est incorrecte", "last_name_required": "L'espace de nom de famille est requis.", "last_name_length": "La longueur de l'espace de nom de famille  est incorrecte", "email_field_required": "Le champ de courrier est manquant.", "not_valid_email": "Le champ de courrier est incorrect.", "mail_length": "La longueur de l'espace de courrier est incorrecte", "phone_field_required": "Le champ de téléphone est obligatoire.", "phone_length": "La longueur du téléphone n'est pas valide.", "company_name_field_required": "L'espace de nom de la société est requis.", "company_name_length": "La longueur de l'entreprise n'est pas valide.", "business_sector_field_required": "Le champ Secteur d'activité est obligatoire.", "business_sector_length": "La longueur du secteur d'activité n'est pas valide.", "staff_count_field_required": "Le champ Nombre d'employés est obligatoire.", "staff_count_length": "La longueur du champ Nombre de personnel est incorrecte.", "content_field_required": "Le champ de contenu est obligatoire.", "content_field_length": "La longueur de la zone de contenu est incorrecte."}, "unhandled_error": "<PERSON><PERSON><PERSON> V<PERSON>z contacter l'administrateur du Site.", "channel": {"name_cannot_be_empty": "Le prénom ne peut pas être vide", "type_cannot_be_empty": "Le type ne peut pas être vide"}}, "errors": {"main": {"captcha": "Captcha n'est pas correct.", "row_count": "Veuillez définir le nombre d'éléments à afficher dans chaque ligne sur 234 ou 6."}, "auth": {"credentials_not_valid": "Veuillez vérifier les informations que vous avez saisies et réessayer.", "login": {"user_is_passive": "Vous ne pouvez pas vous connecter car votre adhésion est passive.", "user_not_available_in_user_table": "Échec de la connexion, veuillez contacter l'administrateur."}, "set_new_password": {"url_is_invalid": "Le lien pour définir un nouveau mot de passe n'est pas valide. Veuillez remplir à nouveau le formulaire Mot de passe oublié.", "token_user_not_found": "Les informations ont été mal saisies, veuil<PERSON>z réessayer."}}, "user": {"login": {"not_permitted": "Vous n'êtes pas autorisé à effectuer cette opération.", "agent_is_not_in_your_company": "Vous n'êtes pas autorisé à effectuer cette opération.", "user_not_active": "Utilisateur non actif.", "user_not_permitted": "L'utilisateur n'est pas autorisé à se connecter à l'application.", "user_not_found": "L'utilisateur associé est introuvable."}, "add_new_user": {"user_exist": "Cette adresse e-mail est ajoutée au système. Veuillez essayer une autre adresse e-mail."}, "enable_user": {"associated_user_not_found": "L'utilisateur associé est introuvable.", "not_in_your_company": "L'utilisateur n'appartient pas à votre entreprise.", "already_enabled": "L'utilisateur semble déjà actif.", "self_edit": "Vous ne pouvez pas vous activer."}, "disable_user": {"associated_user_not_found": "L'utilisateur associé est introuvable.", "not_in_your_company": "L'utilisateur n'appartient pas à votre entreprise.", "already_disabled": "L'utilisateur semble déjà passif.", "self_edit": "Vous ne pouvez pas vous activer."}, "edit_user": {"associated_login_not_found": "Les informations de connexion associées sont introuvables.", "associated_user_not_found": "L'utilisateur associé est introuvable.", "not_in_your_company": "L'utilisateur n'appartient pas à votre entreprise."}, "edit_permissions": {"cannot_change_owner_permission": "Vous ne pouvez pas modifier les autorisations de l'administrateur.", "associated_user_not_found": "L'utilisateur associé est introuvable.", "invalid_self": "Vous ne pouvez pas modifier vos propres privilèges."}}, "dash": {"enable_channel": {"associated_channel_not_found": "Il n'y a pas de canaux affiliés à l'entreprise.", "not_in_your_company": "L'utilisateur n'appartient pas à votre entreprise.", "already_enabled": "L'utilisateur semble déjà actif."}, "disable_channel": {"associated_channel_not_found": "Il n'y a pas de canaux affiliés à l'entreprise.", "not_in_your_company": "L'utilisateur n'appartient pas à votre entreprise.", "already_disabled": "L'utilisateur semble déjà passif."}, "enable_integration": {"already_enabled": "L'utilisateur semble déjà actif."}, "disable_integration": {"already_disabled": "L'utilisateur semble déjà passif."}, "update_profile": {"user_not_found": "L'utilisateur associé est introuvable.", "old_password_wrong": "L'ancien mot de passe que vous avez entré est incorrect.", "associated_user_not_found": "L'utilisateur associé est introuvable."}}, "channel": {"unkown_channel_type": "Type de canal inconnu."}}, "success": {"user": {"added": "L’ajout de l’agent a réussi", "enabled": "Agent activé", "disabled": "L'agent a été désactivé.", "edited": "Les informations sur l'agent ont été modifiées.", "permissions_saved": "Les autorisations ont été enregistrées avec succès."}, "dash": {"profile_updated": "Le profil a été mis à jour avec succès.", "logout_success_message": "est sorti avec succès.", "channel_enabled": "Le canal a été activé.", "channel_disabled": "Le canal a été désactivé.", "integration_enabled": "Intégration activée", "integration_disabled": "Intégration désactivée"}, "auth": {"set_new_password": "Votre mot de passe a été changé avec succès."}, "main": {"contact_form_message_sent": "Le formulaire de contact a été envoyé avec succès.", "try_form_message_sent": "Le formulaire d'essai a été soumis avec succès."}}, "create_channel_success": "Le canal a été créé avec succès.", "edit_channel_success": "Le canal a été modifié avec succès.", "create_try_success": "Votre formulaire d'essai a été créé avec succès.", "channels": {"edit": {"title_TR": "Message de bienvenue", "title_EN": "Message de bienvenue (anglais)"}}, "channel_types_live_chat": "Discussion en direct", "permissions": {"options": {"please_select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "allow": "Permettre", "deny": "N'autorise pas"}, "edit_permissions": {"user": {"create": "Cher /Chere", "edit": "Permet aux utilisateurs de modifier les propriétés de différents utilisateurs.", "enable": "<PERSON><PERSON>", "disable": "Paramètres de profil", "edit_permissions": "Autorisation de l'utilisateur à modifier ses privilèges.", "update": "Active/désactive les utilisateurs pour mettre à jour les fonctionnalités de différents utilisateurs."}, "app": {"login": "Active/désactive l'accès de l'utilisateur à l'application APP.", "login_as_agent": "Active/désactive la modification des paramètres de canal par l'utilisateur."}, "channel": {"create": "Sélectionnez le Quartier", "edit": "Active/désactive la modification des paramètres de canal par l'utilisateur.", "update": "Active/désactive la mise à jour du canal par l'utilisateur."}, "site": {"login": "Cela rend l'utilisateur actif/passif se connectant au site."}, "quick_reply": {"update": "Active/désactive la mise à jour de la fonction de réponse rapide."}, "integration": {"update": "L'intégration rend la mise à jour active/passive.", "create": "L'intégration rend actif/passif."}, "thinker": {"enable": "Active/désactive l'accès au Thinker."}, "helobot": {"enable": "Active/désactive l'accès à Helobot."}, "reports": {"enable": "Active/désactive l'accès aux Rapports."}, "mail_service": {"enable": "Active/désactive l'accès à la Boîte de Réception."}, "hepsiburada": {"enable": "Active/désactive l'accès à Hepsiburada."}, "trendyol": {"enable": "Active/désactive l'accès à Trendyol."}, "pazarama": {"enable": "Active/désactive l'accès à Pazarama."}, "social_marketing": {"enable": "Active/désactive l'accès à la Gestion des Médias Sociaux."}, "chat_force_took": {"enable": "Rend l'autorisation de recevoir le chat d'un autre représentant actif/passif."}, "message_template": {"enable": "Active/désactive l'accès au modèle de message."}}}, "integration": {"days": {"0": "<PERSON><PERSON><PERSON>", "1": "<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON><PERSON><PERSON>", "6": "<PERSON><PERSON>"}, "small": "<PERSON><PERSON>", "medium": "<PERSON><PERSON><PERSON>", "big": "Grand"}}, "App": {"errors": {"campaign": {"not_found": "Campagne introuvable", "not_member": "La campagne est valable uniquement pour les membres."}, "agent": {"required": "L'identifiant de l'agent doit être envoyé.", "move_to": "le champ move_to est obligatoire."}, "user": {"customer_note_not_found": "Informations sur la note client introuvables", "not_found": "Utilisateur non trouvé", "name_not_found": "Nom introuvable", "password_invalid": "Şifrenin geçerli olabilmesi için aşağıdaki kurallara uyması gerekir:\n{{text}}", "password_uppercase": "- Doit contenir au moins une lettre majuscule\n", "password_numeric": "- Doit contenir au moins 1 caractère numérique\n", "password_special": "- Doit contenir au moins 1 caractère spécial (.,*- !)\n", "password_length": "- Doit comporter au moins 6 caractères", "allready_exists": "L'utilisateur existe déjà"}, "conversation": {"not_found": "Intégration introuvable.", "does_not_belongs_to_you": "Le discours n'est pas le vôtre.", "chat_on_agent": "La conversation est au-dessus d'une personne nommée {{name}}. <PERSON><PERSON><PERSON><PERSON> le demander.", "chat_on_agent_with_team": "La conversation concerne {{name}} de l'équipe {{team}}. Veuillez lui en faire la demande.", "message_cannot_be_empty": "Le message ne peut pas être envoyé vide.", "payment_options_could_not_reached": "Les options de paiement n'ont pas pu être atteintes.", "content_is_required": "Nom de l' entreprise", "max_1000": "La longueur du message peut aller jusqu'à 1 000 caractères", "quick_replies_max_1000": "La longueur de la réponse rapide peut aller jusqu'à 1 000 caractères.", "oowh_max_1000": "Heures de travail {{lang}} La longueur maximale peut être de 1 000 caractères", "welcome_max_1000": "Heure de bienvenue {{lang}} Peut contenir jusqu'à 1 000 caractères", "temp_id_not_found": "Veuillez entrer votre code postal.", "content_unavaliable": "Type de message invalide", "is_not_array": "Veuillez envoyer l'utilisateur en masse.", "key_not_found": "Veuillez remplir les champs obligatoires", "chat_received": "Chat {{chat_name}} reçu par {{name}}.", "chats_foward_not_found": "Sélectionnez dans quelle liste (En attente - Archiver) les discussions de la personne seront placées.", "unarchive_max_1000": "La longueur du message {{lang}} à envoyer lors<PERSON> le chat est désarchivé peut aller jusqu'à 1 000 caractères."}, "channel": {"not_found": "Intégration introuvable.", "is_not_active": "Le canal de communication n'est pas actif.", "channel_id_not_found": "L'identifiant du canal est un paramètre obligatoire.", "reviewer_id_not_found": "L'identifiant client est un paramètre obligatoire.", "wrong_status": "N° de Suivi de l'Expédition", "active_timeout": "Définir l'heure de suppression des conversations actives", "notification_sound_status": "L'état du son de notification ne peut pas être vide", "invalid_type": "Type de canal non valide", "welcome_messaging_setting_not_found": "Afficher la marque dans la liste des produits.", "welcome_messaging_setting_en_not_found": "Afficher les nouvelles que c'est nouveau dans la liste des produits.", "welcome_messaging_setting_tr_not_found": "Produit non disponible en stock", "min_one": "Il doit y avoir au moins 1 option.", "max_ten": "Il devrait y avoir un maximum de 10 options.", "button_text_not_found": "Nom de catégorie manquant. Saisissez Toutes les catégories.", "agent_channel_authority": "Cet agent n'est pas autorisé sur le canal.", "instagram_quick_replies": "réponses_rapides incorrectes", "instagram_quick_replies_not_found": "Réponse rapide sélectionnée introuvable", "instagram_quick_replies_text_not_found": "Le titre ne peut pas être laissé vide", "start_type": "Veuillez choisir correctement l'option de démarrage du flux", "connection_failed": "La connexion a échoué.", "unarchive_messaging_setting_not_found": "Aucune information de message à envoyer lorsque le chat est désarchivé", "archive_messaging_setting_not_found": "Aucune information de message à envoyer n'a été trouvée lors de l'archivage du chat", "only_cloud_api": "Cette fonction n'est disponible que pour les canaux connectés via l'API Cloud", "wigdet_size_invalid": "Les informations sur la taille du widget ne sont pas valides", "name_exists": "Une chaîne <PERSON>chat portant ce nom existe déjà", "cloud_profile": {"about_option": "À propos doit contenir au maximum 139 caractères.", "address_option": "Adresse doit contenir au maximum 256 caractères.", "description_option": "Description doit contenir au maximum 512 caractères.", "vertical_option": "Caté<PERSON><PERSON> invalide", "email_option": "Email doit contenir au maximum 128 caractères.", "websites_option": "Sites Web doivent être au maximum 2 et au maximum 256 caractères chacun.", "profile_pic_option": "Image invalide"}, "words": "Mots-clés introuvables", "auto_tag_status": "auto_tag_status information invalide", "auto_tag_not_found": "Aucun enregistrement d’attribution automatique d’étiquette trouvé", "event_data_set": {"invalid": "Nom d'événement non valide '{{event_name}}'"}}, "auth": {"login_as_agent": {"token_is_not_temp": "Page non trouvée", "token_not_found": "Le détail du jeton n'a pas pu être obtenu.", "token_invalid": "Le jeton n'est pas valide.", "user_not_found": "L'utilisateur associé est introuvable.", "user_not_active": "Utilisateur non actif.", "company_not_active": "L'entreprise n'est pas active", "company_not_found": "Il n'y a pas une telle entreprise.", "login": "Vérifiez les informations que vous avez saisies et reconnectez-vous.", "user_not_available_in_user_table": "Échec de la connexion, veuillez contacter l'administrateur.", "email_is_not_found": "Revenir à l'écran de connexion", "jwt_is_not_found": "<PERSON><PERSON><PERSON> de remplir tous les champs!", "email_invalid": "S'il vous plaît, mettez une adresse email valide"}, "forget_password": {"login_not_found": "Veuillez vérifier les informations que vous avez saisies et réessayer.", "mail_warning": "<PERSON><PERSON><PERSON>z réessayer dans 2 minutes."}, "set_new_password": {"token_not_found": "Le détail du jeton n'a pas pu être obtenu.", "hashes_dont_match": "Le jeton n'est pas valide.", "token_type_not_correct": "Le jeton n'est pas valide.", "token_expired": "Le jeton est obsolète.", "token_used": "Le jeton a été utilisé.", "passwords_should_not_be_empty": "Le nouveau mot de passe et le champ de répétition du mot de passe ne doivent pas être vides.", "passwords_should_be_equal": "Les champs du nouveau mot de passe et du mot de passe de répétition doivent être identiques.", "could_not_find_assosiciated_user": "Les informations ont été mal saisies, veuil<PERSON>z réessayer."}}, "message_report": {"start_date_end_date_not_found": "Doit avoir une heure de début et une heure de fin."}, "main": {"log": {"type_not_specified": "Le type de journal n'est pas spécifié.", "message_not_specified": "Aucun message de journal spécifié.", "type_could_not_find": "Type de journal introuvable."}}, "dash": {"conversation_is_disabled": "Le message indiquant que le canal de communication est passif par votre administrateur ne peut pas être envoyé.", "conversation_belong_to_someone": "La conversation semble appartenir à quelqu'un d'autre.", "conversation_active_time_expired": "Vous ne pourrez pas contacter ce client car l'appel a expiré."}, "integration": {"domain_invalid": "Le domaine n'est pas valide", "currency_code_not_found": "There is a currency code mismatch", "integration_and_channel_company_ids_do_not_match": "Les sociétés d'intégration et de canaux ne correspondent pas.", "integration_id_cannot_be_empty": "IntegrationId ne peut pas être vide", "action_cannot_be_empty": "L'action ne peut pas être vide", "type_cannot_be_empty": "Le type ne peut pas être vide", "integration_page_is_wrong": "La page d'intégration est incorrecte", "user_not_found": "L'utilisateur associé est introuvable.", "cart_is_empty": "Il n'y a aucun objet dans votre panier.", "addresses_are_empty": "L'adresse à partager est introuvable.", "chat_id_cannot_be_empty": "ChatId ne peut pas être laissé vide", "cargos_are_empty": "<PERSON><PERSON>ne cargaison à partager.", "quick_reply_not_found": "Paramètre de réponse rapide introuvable", "text_not_found": "Texte introuvable", "caption_not_found": "Type d'intégration", "remittance_name_not_provided": "Les informations de transfert et d'EFT doivent être écrites", "remittance_id_not_provided": "Les informations d'identification de transfert et d'EFT doivent être écrites.", "keys_not_found": "Clés introuvables", "keys_limit": "Les clés doivent avoir un maximum de 3 éléments.", "lowest_key_limit": "Les clés doivent avoir au moins 2 éléments.", "max_key_limit": "Les clés doivent avoir un maximum de 10 éléments.", "duplicate_snipped_text": "Impossible d'en ajouter plusieurs à partir du même raccourci de message", "duplicate_match": "Plus d'un ne peut pas être ajouté à partir du même raccourci!", "sub_payment_option_not_found": "SubPaymentOptionId est introuvable.", "stock_not_found": "Envoyer un message de bienvenue.", "address_data_not_found": "Sélection d'adresse réussie!", "campaign_group_id_not_found": "Paramètres de message en dehors des heures d'ouverture", "payment_option_id_not_found": "Nouveau client", "cargo_option_id": "Client approuvé avec succès!", "integration_not_found": "Il n'y a aucune commande à afficher", "info_not_found": "Client mis à jour!", "product_not_found": "Les informations oowh_messages  sont manquantes", "share_address_not_found": "Informations d'adresse  introuvable", "pay_at_door_options": "La valeur de 7 jours doit être saisie", "oowh_messages": "Envoyer un message de bienvenue par défaut.", "ads_messages": "les informations ads_messages sont manquantes", "chronological_order": "V<PERSON> devez ajouter les intervalles d'heures de travail dans l'ordre.", "start_hour_not_greater_than_end": "<PERSON><PERSON><PERSON> individuel<PERSON>", "start_hour_not_equal_end": "Ne peut pas être vide", "maximum_hour_range": "Un maximum de 3 heures peut être saisi.", "order_status_bad_request": "Bureau fiscal", "is_active_default_oowh_messages": "Processus", "working_hours_empty": "L'heure de début ne peut pas être supérieure à l'heure de fin", "required_seven_for_days": "Prénom Nom", "first_name_required": "L'espace de noms est requis.", "last_name_required": "L'espace de nom de famille est requis.", "email_required": "Date de commande", "phone_required": "Veuillez entrer votre ville", "channel_name_not_found": "Anciennes commandes", "the_product_is_out_of_stock": "Paiement effectué", "wrong_status": "N° de Suivi de l'Expédition", "there_is_integration": "Entrez le message EN", "working_hour_start_end": "Veuillez ne pas laisser l'une des heures de travail pleine et l'autre vide.", "perpage_not_found": "Veuillez d'abord entrer le nombre de pages", "perpage_wrong_value": "Veuillez entrer des valeurs entre 1 et 10", "next_or_after": "Il suffit de demander la page suivante ou précédente", "post_not_found": "Informations sur l'identifiant de la publication introuvables", "comment_not_found": "Veuillez écrire un commentaire", "comment_id_not_found": "Identifiant de commentaire introuvable", "not_found_variant": "Variable de sélection introuvable", "once_cargo": "Sélectionnez Expédition d'abord", "empty_cart": "Panier introuvable", "user_email_not_found": "Veuillez d'abord enregistrer l'e-mail du client.", "min_count": "Au moins {{count}} peut être augmenté", "note_field_length": "Le champ mémo peut avoir une longueur maximale de 250 caractères", "no_address": "Veuillez ajouter une adresse", "page_value": "Nombre de pages incorrect", "customer_email_required": "Premier e-mail d'information demandé au client. Enregistrez votre e-mail", "user_registered": "Utilisateur enregistré", "user_paired": "l'utilisateur a été éliminé", "user_paired_error": "User Not Found in Your Integration. Please Re-Match", "user_pairing_removed": "Appariement de l'utilisateur supprimé", "user_not_previously_paired": "Cet utilisateur n'a pas été précédemment apparié", "phone_number_invalid": "Le numéro de téléphone est invalide", "email_invalid": "Le courriel est invalide", "name_invalid": "Le nom n'est pas valide", "password_invalid": "Le mot de passe est invalide", "post_or_comment_removed": "L'action ne peut pas être effectuée. La publication ou le commentaire a peut-être été supprimé. Veuillez vérifier avec Meta.", "cart_isnot_avaliable": "Panier inactif", "payment_not_found": "Méthode de paiement non trouvée", "livechat_not_added": "LiveChat n'a pas encore été ajouté à votre site.", "livechat_added": "LiveChat a déjà été ajouté à votre site."}, "settings": {"user_not_found": "L'utilisateur associé est introuvable.", "assign_chat_to_agent_not_found": "Attribution de chat à l'agent introuvable", "hide_phone_number_not_found": "Paramètre de masquage du numéro introuvable", "chat_message_read_status_not_found": "Paramètre de masquage de l'état de lecture du message introuvable", "company_owner_get_force_chat_invalid": "La valeur du chat forcé n'est pas valide.", "daily_report_not_found": "Option de rapport quotidien introuvable.", "weekly_report_not_found": "Option de rapport hebdomadaire introuvable.", "monthly_report_not_found": "Option de rapport mensuel introuvable.", "export_right_not_found": "Vous n'avez pas droit à une sauvegarde pour ce canal. Réessayez le {{date}}", "at_least_one_ads_required": "<PERSON><PERSON> devez ajouter au moins une annonce.", "ads_required": "<PERSON><PERSON> de<PERSON> sélectionner une annonce.", "duplicate_ads": "Vous ne pouvez pas ajouter la même annonce plusieurs fois.", "at_least_one_reply_method_required": "Veuillez sélectionner au moins une option de réponse.", "thinker_flow_id_required": "<PERSON><PERSON> de<PERSON> sélectionner un flux Thinker.", "helobot_knowledge_base_id_required": "<PERSON><PERSON> de<PERSON> sélectionner une base de connaissances Helobot.", "message_text_required": "<PERSON><PERSON> devez saisir un message.", "message_cannot_exceed_1000_characters": "Le message ne peut pas dépasser 1000 caractères. Si des emojis ou des caractères spéciaux sont utilisés, le nombre peut être plus élevé."}, "ask_form_questions": {"field_key_not_found": "Clé de champ introuvable."}, "live_chat": {"ext_id_not_found": "Aucune carte de membre à afficher!", "message_not_found": "Archive de Demande", "name_required": "Informations de commande", "email_required": "Le champ de courrier est manquant.", "phone_required": "Le champ de téléphone est obligatoire.", "user_not_found": "L'utilisateur associé est introuvable."}, "cart": {"cannot_add_this_currency_code": "Options de mandat postal / TEF"}, "google": {"calendar": {"not_found_calendar_id": "L'ID de l'agenda est incorrect", "not_found_summary": "Le titre ne peut pas être laissé vide", "not_found_description": "La description ne peut pas être laissée vide", "not_found_end_time": "Date de fin incorrecte"}, "drive_account": {"allready_exist": "Le compte est déjà actif sur cette chaîne", "not_found": "Tout d’abord, connectez votre compte Google Drive"}}, "tags": {"tag_not_found": "Balise introuvable", "already_exists_tag": "La balise existe déjà", "already_exists_color": "La couleur de balise existe déjà", "tag_id_not_found": "ID de balise introuvable", "ids_not_found": "identifiants introuvables", "max_label": "Vous pouvez créer un maximum de 250 balises"}, "instagram": {"channel_id_invalid": "ID de chaîne non valide", "message_invalid": "Le contenu de la question-réponse ne peut pas être laissé vide", "status_invalid": "Statut non invalide", "persistent_menu_invalid": "Persistent <PERSON><PERSON> invalide", "call_to_actions_invalid": "Appel à l'action invalide", "messages_not_found": "Message invalide", "default_messages_not_found": "Messages par défaut invalides", "instagram": "Call to actions non invalide", "ice_braker_status": "Enregistrez d'abord un message de bienvenue", "ref_not_found": "<PERSON><PERSON><PERSON> non défini", "ref_id_not_found": "ID de référence invalide", "ref_registered": "<PERSON><PERSON><PERSON>", "instagram_channel_invalid": "Type de chaîne invalide."}, "message": {"temp_id": "temp_id introuvable", "pagination_invalid": "Les informations de pagination ne sont pas valides", "perpage_invalid": "la valeur par page doit être comprise entre 20 et 100", "page_invalid": "la valeur de la page peut être au moins 0", "text_invalid": "Texte de requête introuvable", "agent_message_not_found": "Il n'y a jamais eu de conversation auparavant.", "not_found": "Message non trouvé.", "retry_done": "Le message ne peut plus être envoyé. Renvoyer la tentative terminée", "channel_type_mismatch": "Le transfert de messages ne peut être effectué qu'entre les mêmes types de canaux", "agent_only_view": "Le chat existe mais l'agent n'a pas répondu."}, "team": {"agent_id_not_found": "L'identifiant de l'agent n'est pas valide", "channel_id_not_found": "L'identifiant de la chaîne n'est pas valide", "team_id_not_found": "L'identifiant de l'équipe n'est pas valide", "channels_not_found": "Il manque des chaînes", "agents_not_found": "Les agents manquent", "team_not_found": "Equipe introuvable", "status_not_found": "L'état de l'état est invalide", "team_name_not_found": "Le nom de l'équipe n'est pas valide", "channel_not_authorization": "Vous n'êtes pas autorisé à accéder au canal.", "name_already_exists": "L'équipe enregistrée avec ce nom existe déjà.", "team_status_not_found": "L'équipe n'a aucune information de statut", "team_setting_status_inactive": "État de réglage de l'équipe inactif", "chat_on_team": "Le chat est sur l'équipe {{team_name}}."}, "trendyol": {"account_available": "Compte disponible", "account_not_found": "Compte non trouvé", "question_id_not_found": "l'identifiant de la question n'a pas été trouvé", "name_not_found": "Nom introuvable", "supplier_id_not_found": "ID de fournisseur introuvable", "username_not_found": "Informations sur le nom d'utilisateur introuvables", "password_not_found": "informations de mot de passe introuvables"}, "hepsiburada": {"account_available": "Compte disponible", "account_not_found": "Compte non trouvé", "text_not_found": "Message non trouvé", "question_id_not_found": "l'identifiant de la question n'a pas été trouvé", "name_not_found": "Nom introuvable", "supplier_id_not_found": "ID de fournisseur introuvable", "username_not_found": "Informations sur le nom d'utilisateur introuvables", "password_not_found": "informations de mot de passe introuvables", "merchant_id_not_found": "Identifiant de magasin introuvable", "error_auth": "ID de la boutique ou mot de passe incorrect. Veuillez vérifier vos informations"}, "helobot": {"account_available": "Compte disponible", "account_not_found": "Compte non trouvé", "question_not_found": "Question introuvable", "name_not_found": "Nom introuvable", "file_urls_not_found": "Fichier introuvable", "kb_id_invalid": "Les informations de la base de connaissances ne sont pas valides", "kb_not_found": "Base de connaissances introuvable", "conversation_id_not_found": "L'identifiant de la conversation est introuvable", "timeout": "Les informations de ''délai d'attente'' ont été saisies de manière incorrecte ou invalide", "allready_started": "Cette conversation est maintenant transfé<PERSON>e à Helobot."}, "media": {"not_found_media_name": "Nom du média introuvable"}, "location": {"id": "ID Introuvable", "name": "Nom Introuvable", "title": "titre Introuvable", "address": "<PERSON><PERSON><PERSON>", "longitude": "Longitude Introuvable", "latitude": "Latitude Introuvable", "selected_location": "Emplacement Sélectionné Introuvable", "name_already_exists": "Un emplacement avec ce nom existe déjà."}, "overtimes": {"status": "Statut de travail invalide", "OVERTIME_ON": "Commencer les heures supplémentaires", "OVERTIME_OFF": "Terminer les heures supplémentaires", "BREAK_TIME_ON": "Commencer la <PERSON>", "BREAK_TIME_OFF": "<PERSON><PERSON><PERSON> la <PERSON>", "overtime_error": "Veuillez d'abord d<PERSON><PERSON><PERSON> Shift ou Finish Break."}, "pazarama": {"account_available": "Compte disponible", "account_not_found": "Compte introuvable", "text_not_found": "Message introuvable", "client_id_not_found": "Les informations client_id sont introuvables", "client_secret_not_found": "Informations client_secret introuvables"}, "n11": {"api_key_not_found": "Informations api_key non trouvées", "api_password_not_found": "Informations api_password non trouvées"}, "abandoned_cart": {"abandoned_cart_id_not_found": "ID du panier abandonné introuvable", "not_found": "Enregistrement de panier abandonné introuvable"}, "service_account": {"cannot_delete": "Ce compte de service ne peut pas être supprimé."}, "market_quick_reply": {"text_not_found": "Information de texte introuvable", "type_not_found": "Information de type introuvable ou incorrecte"}}, "mail": {"issue_invalid": "Le type de message n'est pas valide (issue)", "header_invalid": "Le titre est invalide", "body_invalid": "La partie contenu n'est pas valide", "agent_id_invalid": "agent_id invalide", "success_message": "Votre demande a été reçue. Nous te remercions.", "error_message": "Une erreur s'est produite, la demande n'a pas été livrée.", "not_verified": "<PERSON><PERSON> de<PERSON> d'abord confirmer votre adresse e-mail."}, "channel": {"upload_channel_picture": "Sons de notification de message", "change_channel_name": "Modification du son de notification de message", "leave_active_conversation_time": "Envoyer aux archives (Minute)", "send_to_archive_time": "Envoyer le chat vers l'archive", "send_to_archive_time_status": "<PERSON> le client ne répond pas, envoyez le chat vers l'archive", "send_to_archive_time_timeout": "Temps d'archivage du chat (minutes)", "notification_sound_status": "État du son de notification", "welcome_message": "Rejoignez facilement vos clients!", "send_welcome_message": "Entrez votre note ...", "is_default_send_welcome_message": "Processus d'envoi pour approbation réussi!", "welcome_message_item_title": "Rejoignez facilement vos clients!", "whatsapp_about": "Whatsapp Sur", "send_welcome_message_actions": "Affectation du message de bienvenue", "quick_replies": "Paramètres de réponse rapide", "hide_phone_number": "Gardez le numéro de téléphone du client caché pour l'agent", "team_message": "Paramètres des messages d'équipe", "unarchived": "Le message d'équipe est transféré une fois que le chat atteint l'attente de l'archive", "after_order": "<PERSON><PERSON> après la commande (2 heures plus tard)", "before_order": "Si la commande n'est pas faite, envoyez-la (après 24 heures)", "snipped_text": "Texte coupé", "unarchive_message": "Désarchivage du message", "send_message_when_unarchived": "Envoyer un message lorsque le chat n'est pas archivé", "is_default_send_archive_message": "Envoi d'un message par défaut lorsque le chat n'est pas archivé", "unarchive_message_item_title": "Désarchivage du message", "send_unarchive_message_actions": "Archive Post Message Assignment", "timeout_for_archived_chat": "Temps d'attente dans les archives pour l'envoi d'un message", "send_archive_message_actions": "Envoi d'un message pendant l'archivage d'une conversation", "is_default_send_archiving_message": "Envoyer un message par défaut", "archive_message_item_title": "Message à envoyer aux archives", "send_message_while_chat_is_archived": "Envoyer un message pendant que le chat est archivé"}, "integration": {"send_cart_message": {"item": "[BR][/BR]{{emoji}}[B]{{title}} {{variant_message}}[/B][BR][/BR][B]Montant:[/B] {{count}} {{stock_unit}}[BR][/BR][B]Prix:[/B] {{price}}[BR][/BR][B]Montant:[/B] {{amount}}", "sub_total": "[B]Total:[/B] {{total}}", "coupon_total": "[BR][/BR][B]Coupon de réduction :[/B][SPACE]2[/SPACE]{{total}}", "campaign_total": "[B]Remise de campagne :[/B][SPACE]2[/SPACE]{{total}}", "general_total": "[BR][/BR][B]le total global:[/B] {{total}}", "approve_message": "Si vous approuvez votre panier, saisissez [B]1[/B], s'il vous plaît."}, "send_history_message": {"item": "[BR][/BR]{{emoji}} [B]{{title}} ({{variant_message}})[/B][BR][/BR][B]Quantité :[/B][SPACE]17[/SPACE]{{count}} {{stock_unit}}[BR][/BR][B]Prix :[/B][SPACE]20[/SPACE]{{price}}[BR][/BR]", "order_code": "[B]Code de commande:[/B] {{order_code}}[BR][/BR]", "status": "[B]Statut de la commande:[/B] {{status}}[BR][/BR]", "cargo": "[B]Cargaison:[/B] {{cargo}}[BR][/BR]", "cargo_tracking_code": "[B]Code de suivi du fret:[/B] {{cargo_tracking_code}}[BR][/BR]", "cargo_tracking_url": "[B]Lien de suivi du fret:[/B] {{cargo_tracking_url}}[BR][/BR]", "date": "[B]Date:[/B] {{date}}[BR][/BR]", "total_price": "[B]Montant:[/B] {{total_price}}[BR][/BR]", "order_url": "Vous pouvez vérifier le statut de votre commande ici.[BR][/BR]{{url}}"}, "send_heloscope_order_message": "Votre commande a été créée avec succès.", "send_product_share_messageV2": "[BR][/BR][B]{{product_title}}[/B][BR][/BR]{{description}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Si vous souhaitez ajouter ce produit au panier [B]{{product_share_counter}}[/B] en été.", "send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Prix :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Si vous souhaitez ajouter ce produit au panier [B]{{product_share_counter}}[/B] en été.", "send_product_share_message_no_action": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Prix :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}", "whatsapp_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Prix :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR].", "livechat_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Prix :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}.", "order_summary_message": "chère [B]{{username}}[/B][BR][/BR]Produits dans votre panier:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Informations sur la cargaison :[/B][BR][/BR]Transporteur :[SPACE]10[/SPACE]{{cargo_option_name}}[BR][/BR]Frais de port :[SPACE]20[/SPACE]{{cargo_option_fee}}[BR][/BR][BR][/BR][B]le total global :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse de facturation:[/B][BR][/BR][B]Adresse :[/B] {{invoice_address}} [BR][/BR][B]Quartier:[/B] {{invoice_district}} [BR][/BR][B]Province :[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Adresse de livraison :[/B][BR][/BR][B]Adresse :[/B] {{delivery_address}} [BR][/BR][B]Quartier:[/B] {{delivery_district}} [BR][/BR][B]Province:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Mode de paiement[/B]: {{payment_type}}[BR][/BR][BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, veuillez [B]1[/B] envoyer votre message.", "shopify_order_summary_message": "chère [B]{{username}}[/B][BR][/BR]Produits dans votre panier:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][B]impôt :[/B]{{tax}}[BR][/BR][B]Somme finale :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse:[/B]{{address}}[BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, veuillez [B]1[/B] envoyer votre message.", "shopify_whatsapp_order_summary_message": "Cher(e) [B]{{username}}[/B][BR][/BR]Les articles dans votre panier :[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][BR][/BR][B]Taxe :[/B]{{tax}}[BR][/BR][BR][/BR][B]Frais de livraison :[/B] {{cargo_option_fee}}[BR][/BR][BR][/BR]{{discounts}}[B]Total général :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse :[/B]{{address}}[BR][/BR]", "whatsapp_order_summary_message": "chère [B]{{username}}[/B] Produits dans votre panier:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Informations sur la cargaison:[/B][BR][/BR]Transporteur: {{cargo_option_name}}[BR][/BR]Frais de port: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]le total global:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse de facturation[/B][BR][/BR][B]Adresse:[/B] {{invoice_address}}[BR][/BR][B]Quartier:[/B] {{invoice_district}}[BR][/BR][B]Province:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Adresse de livraison:[/B][BR][/BR][B]Adresse:[/B] {{delivery_address}}[BR][/BR][B]Quartier:[/B] {{delivery_district}}[BR][/BR][B]Province:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Mode de paiement:[/B] {{payment_type}}[BR][/BR][BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, veuillez cliquer sur le bouton ci-dessous.", "whatsapp_order_summary_message_cart_content": "chère [B]{{username}}[/B] Produits dans votre panier: {{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Informations sur la cargaison: [/B][BR][/BR]Transporteur: {{cargo_option_name}}[BR][/BR]Frais de port: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]le total global: [/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse de facturation: [/B][BR][/BR]{{invoice_address}}[BR][/BR]", "whatsapp_order_summary_message_invoice": "[B]Quartier:[/B] {{invoice_district}}[BR][/BR][B]Province:[/B] {{invoice_city}}[BR][/BR]", "whatsapp_order_summary_message_delivery_address": "[BR][/BR][B]Adresse de livraison:[/B][BR][/BR]{{delivery_address}}[BR][/BR]", "whatsapp_order_summary_message_delivery": "[B]Quartier:[/B] {{delivery_district}}[BR][/BR][B]Province:[/B] {{delivery_city}}[BR][/BR]", "whatsapp_order_summary_message_approve": "[BR][/BR][B]Mode de paiement:[/B] {{payment_type}}[BR][/BR][BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, veuillez cliquer sur le bouton ci-dessous.", "whatsapp_order_summary_message_partition": "chère [B]{{username}}[/B] Produits dans votre panier:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}", "whatsapp_order_summary_message_partition_2": "{{additional_cost}}[B]Informations sur la cargaison:[/B][BR][/BR]Transporteur: {{cargo_option_name}}[BR][/BR]Frais de port: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]le total global:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse de facturation[/B][BR][/BR][B]Adresse:[/B] {{invoice_address}}[BR][/BR][B]Quartier:[/B] {{invoice_district}}[BR][/BR][B]Province:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Adresse de livraison:[/B][BR][/BR][B]Adresse:[/B] {{delivery_address}}[BR][/BR][B]Quartier:[/B] {{delivery_district}}[BR][/BR][B]Province:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Mode de paiement:[/B] {{payment_type}}[BR][/BR][BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, veuillez cliquer sur le bouton ci-dessous.", "order_summary_message_partition_2": "{{additional_cost}}[B]Informations sur la cargaison:[/B][BR][/BR]Transporteur: {{cargo_option_name}}[BR][/BR]Frais de port: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]le total global:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adresse de facturation[/B][BR][/BR][B]Adresse:[/B] {{invoice_address}}[BR][/BR][B]Quartier:[/B] {{invoice_district}}[BR][/BR][B]Province:[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Adresse de livraison:[/B][BR][/BR][B]Adresse:[/B] {{delivery_address}}[BR][/BR][B]Quartier:[/B] {{delivery_district}}[BR][/BR][B]Province:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Mode de paiement:[/B] {{payment_type}}[BR][/BR][BR][/BR]Si vous confirmez votre commande avec les informations ci-dessus, Veuillez écrire 1.", "send_customer_password_created": "Votre adhésion a été créée avec succès, temporaire[B]votre mot de passe: {{password}}[/B], V<PERSON> pouvez modifier le mot de passe à tout moment.✅", "send_customer_created": "Votre adhésion a été créée avec succès.✅", "send_order_status_message": "chère [B]{{customer_name}}[/B] [BR][/BR][B]Numéro de commande :[/B]{{order_code}}[BR][/BR][B]Date de commande :[/B]{{order_date}}[BR][/BR][B]Montant de la commande :[/B]{{order_total_price}}[BR][/BR][B]Compagnie de fret :[/B]{{cargo_option_name}}[BR][/BR][B]Code de suivi du fret :[/B]{{cargo_tracking_code}}[BR][/BR][B]Statut d'envoi :[/B]{{order_status}}[BR][/BR]{{cargo_tracking_url}}", "remittance_message": "[BR][/BR][B]IBAN[/B]: {{iban}}", "send_cargo_message": "Vos options d'expédition sont répertoriées ci-dessous. Écrivez le numéro à côté de celui que vous voulez choisir.[BR][/BR][BR][/BR]{{cargo_options}}", "whatsapp_send_cargo_message": "Vos options d'expédition sont répertoriées ci-dessous. Choisissez en un s'il vous plait", "send_address_message_caption": "{{emoji}}[B]Adresse :[/B] {{address}}[BR][/BR][B]Ville :[/B] {{city}}[BR][/BR][B]Quartier :[/B] {{town}}", "send_address_message_caption_address": "[BR][/BR]{{emoji}}[B]Adresse :[/B] {{address}}", "send_address_message_caption_city": "[BR][/BR][B]Ville :[/B] {{city}}[BR][/BR][B]Quartier :[/B] {{town}}", "shopify_send_address_message_caption": "{{emoji}}[B]Adresse :[/B] {{address}}[BR][/BR][B]Pays :[/B] {{country}}[BR][/BR][B]Ville :[/B] {{city}}[BR][/BR][B]Quartier :[/B] {{province}}", "general_order_note": "[BR][/BR][B]Note de commande :[/B]{{general_order_note}}[BR][/BR]", "first_message": "Bon<PERSON><PERSON>, votre demande a été reçue. Vous serez contacté dès que possible.🛒", "discount_coupone_message": "[BR][/BR][BR][/BR][B]Prix ​​bas:[/B] {{discount_price}}[BR][/BR][BR][/BR]", "send_customer_confirmation_message": "Vos informations d'adhésion sont listées ci-dessous.[BR][/BR][BR][/BR]{{customer_data}}{{terms_of_use_url}}{{gdpr_url}}[BR][/BR]Si vous confirmez les informations d'adhésion et souhaitez devenir notre membre, [B]onaylıyorum[/B] S'il vous plait écrivez.", "send_payment_url": "Afin de compléter vos achats avec une carte de crédit, nous vous prions de bien vouloir compléter votre paiement à partir du lien ci-dessous.[BR][/BR]{{payment_url}}", "yes": "O<PERSON>", "no": "Non", "cart_approve_message": "Si vous approuvez votre panier, saisissez [B]1[/B], s'il vous plaît.", "coupon_message": "[B]Coupon de réduction :[/B][SPACE]5[/SPACE]{{price_coupon}}[BR][/BR]", "send_out_of_hours_message": "Bonjour! Bienvenue sur Helorobo. Nous avons bien reçu votre message et vous répondrons dans les 24 heures. Merci d'avoir pris contact avec nous.", "instagram_private_reply": "Merci de nous avoir contactés, comment puis-je vous aider?", "cash": "Espèces", "credit_card": "Carte de <PERSON>", "turkish_out_of_working_hours_message": "Message en dehors des heures d'ouverture.", "english_out_of_working_hours_message": "Message en dehors des heures d'ouverture (en anglais)", "order_not_found_message": "Il n'y a pas de commande dont vous pouvez vérifier l'état de la commande", "confirm_select_address": "Votre commande a été créée avec succès! Numéro de commande", "send_empty_address_message": "Il n'y a pas d'adresse à choisir. Veuillez fournir vos informations d'adresse.", "edit": {"kvkk": "Vider le Panier", "membership_registration_agreement": "Les heures doivent être à la fois pleines ou vides", "customer_required": "la communauté est un pas de plus.", "precision": "Aucun produit trouvé", "show_brand_name": "order_status_id ne peut pas être 0", "show_product_new": "Aucune information sur l'option de paiement trouvée", "eft_options": "<PERSON><PERSON>", "door_payment_options": "Le message {{content}} ayan<PERSON>", "order_status": "Canal de communication introuvable.", "ready_cargo": "Votre mot de passe actuel", "gave_to_cargo": "Langue de correspondance client", "success_payment": "Nouveau mot de passe", "out_of_hours_message_settings": "Vous pouvez choisir une catégorie en haut à gauche ou commencer vos achats en effectuant une recherche.", "out_of_hours_send_message": "Sélectionnez la Ville", "out_of_hours_default_send_message": "Mot de passe", "out_of_hours_message": "Ville", "out_of_hours": "Changer le mot de passe", "show_is_new": "Impossible de trouver l'information welcome_messaging_setting", "show_brand": "Votre commande a été créée avec succès!", "perpage": "Combien de produits envoyer en même temps dans le partage de produits", "order_created_message": "Message d'état de la commande", "send_order_created_message": "Envoyer un message d'état de la commande?", "is_default_order_created_message": "Envoyer un message de bienvenue par défaut?", "manuel_discount_permission": "Opportunité de remise manuelle", "all": "Toute", "just_company_owner": "Propriétaire de l'entreprise uniquement", "default_currency_code": "devise par défaut", "warn_unregister_purchase": "Envoyer un message de confirmation pour l'impossibilité de payer par carte de crédit pour les clients non membres", "send_product_price_without_vat": "Lors du partage d'un produit, envoyez le prix hors TVA"}, "duplicate_member": "<PERSON><PERSON> membre, vous n'avez pas plus d'un abonnement sur notre site, avec laquelle des informations suivantes souhaitez-vous continuer ? [BR][/BR]{{duplicate_member}}", "duplicate_member_item": "{{emoji}} Email: {{email}} [BR][/BR] Téléphone: {{phone_number}}", "tax": "<PERSON><PERSON><PERSON>", "kdv": "TVA", "arvia": {"created_room": "Vous pouvez commencer à nous parler via le lien ci-dessous pour une conversation vidéo ou audio.\n{{url}}"}, "only_one_discount": "Un seul coupon de réduction peut être appliqué", "credit_cart": "Carte de Crédit/Carte de Débit", "discounts": "Réductions"}, "auth": {"forgot_password": {"form": {"title": "Quel est le titre de votre adresse? (du travail, du domicile, etc.)", "subtitle": "Le champ de courrier est manquant.", "email": "Pouvez-vous écrire votre adresse e-mail?", "enter_page": "Opération en cours"}, "accept": {"title": "Quel est le titre de votre adresse? (du travail, du domicile, etc.)", "subtitle": "Nous avons envoyé le lien de réinitialisation du mot de passe, vérifiez votre courrier électronique."}, "mail_template": {"subject_dear": "Aucune information trouvée...", "subject_description": "Paramètres de message d'accueil", "title": "Quel est le titre de votre adresse? (du travail, du domicile, etc.)", "subtitle": "Demande de changement de mot de passe", "description": "Quel est votre type de facture ? (Particulier ou entreprise)", "sub_description": "Vous pouvez sélectionner le client sur la gauche et démarrer immédiatement la conversation.", "enter_set_new_password": "Active/désactive l'accès de l'utilisateur à l'application APP."}, "password_change_request": "<PERSON><PERSON><PERSON> de Changement de Mot de Passe Helorobo", "password_change_link": "Nous avons reçu une demande de réinitialisation du mot de passe pour votre compte. Veuillez utiliser le lien ci-dessous pour définir un nouveau mot de passe :", "warning_1": "Si vous n'êtes pas à l'origine de cette demande, ve<PERSON><PERSON>z ignorer cet e-mail. Votre compte restera sécurisé.", "warning_2": "Le lien de réinitialisation du mot de passe est valable uniquement pendant 24 heures. Vous devez utiliser le lien dans ce délai pour réinitialiser votre mot de passe.", "warning_3": "Si vous rencontrez des problèmes, n'hésitez pas à contacter notre équipe de support."}, "helorobo_bilgi_teknolojileri": "© Helorobo Bilgi Teknolojileri A.Ş"}, "success": {"integration": {"add_to_cart": "Le produit nommé {{item_name}} a été ajouté à votre panier.", "empty_cart": "Tous les articles de votre panier ont été supprimés.", "dec_item_from_cart": "Le nombre de produits nommés a été réduit.", "inc_item_from_cart": "Le nombre de produits nommés a été augmenté.", "item_removed_from_cart": "L'élément {{item_name}} a été supprimé de votre panier.", "order_message": "Votre commande a été créée avec succès.[BR][/BR]Numéro de commande :[B]{{orderId}}[/B][BR][/BR]chère [B]{{username}}[/B],[BR][/BR]{{orderTotalPrice}} Nous avons bien reçu votre commande.[BR][/BR]Il devrait être livré dans les jours 2 à 4. Pour connaître l'état de votre commande, vous pouvez utiliser. [B]Sipariş Durumu[/B] Si vous nous envoyez le message, notre robot vous informera du processus.", "order_message_manuel": "Votre commande a été créée avec succès.  Numéro de commande :{{orderId}}  chère {{username}},  {{orderTotalPrice}} Nous avons bien reçu votre commande.  Il devrait être livré dans les jours 2 à 4. <PERSON>ur connaître l'état de votre commande, vous pouvez utiliser. 'Sipariş Durumu' Si vous nous envoyez le message, notre robot vous informera du processus.", "order_message_credit_cart": "Votre commande a été créée avec succès.[BR][/BR]Numéro de commande :[B]{{orderId}}[/B][BR][/BR]chère [B]{{username}}[/B],[BR][/BR]{{orderTotalPrice}} Nous avons bien reçu votre commande.[BR][/BR] Afin de finaliser votre commande, veuillez compléter votre commande via le lien dans le message entrant. Ensuite, si vous nous envoyez le message [B]Sipariş Durumu[/B] via pour connaître l'état de votre commande, notre robot vous informera du processus.", "shopify_order_credit_cart_message": "Votre commande a été créée avec succès. Vous pouvez continuer le processus de paiement à partir du lien ci-dessous.[BR][/BR]{{url}}", "shopify_order_message": "Votre commande a été créée avec succès. Vous pouvez accéder aux détails de la commande à partir du lien ci-dessous.[BR][/BR]{{url}}", "customer_address_message": "Bonjour, vos adresses sont indiquées ci-dessous dans l'ordre. Quelle adresse souhaitez-vous choisir comme adresse de livraison ?[BR][/BR]", "livechat_customer_address_message": "Bon<PERSON>r, vos adresses sont indiquées ci-dessous dans l'ordre. Quelle adresse souhaitez-vous choisir comme adresse de livraison ?", "customer_only_address_message": "Bonjour, votre adresse est indiquée ci-dessous. Confirmez-vous cette adresse?[BR][/BR]", "customer_invoice_address_message": "Bonjour, vos adresses sont indiquées ci-dessous dans l'ordre. Quelle adresse souhaitez-vous choisir comme adresse de facturation ?[BR][/BR]", "livechat_customer_invoice_address_message": "Bon<PERSON>r, vos adresses sont indiquées ci-dessous dans l'ordre. Quelle adresse souhaitez-vous choisir comme adresse de facturation ?", "payment_type": "Bonjour, vos options de paiement sont répertoriées ci-dessous. Quelle option de paiement souhaitez-vous utiliser ? S'il te plaît [B]numara[/B] Spécifiez comme.[BR][/BR][BR][/BR]{{payment_options}}", "whatsapp_payment_type": "Bonjour, vos options de paiement sont données dans l'ordre dans les options. Quelle option de paiement souhaitez-vous utiliser ? Choisissez en un s'il vous plait.", "whatsapp_payment_type_continue": "Plus de vos options de paiement sont données ici. Quelle option de paiement souhaitez-vous utiliser ? Choisissez en un s'il vous plait.", "add_to_cart_children": "Les options de produits sont répertoriées ci-dessous. Écrivez le numéro à côté de celui que vous voulez choisir.[BR][/BR]{{children_title}}[BR][/BR][BR][/BR]{{children}}[BR][/BR]", "whatsapp_add_to_cart_children": "Options disponibles: {{children_title}}[BR][/BR][BR][/BR]choisissez l'une des options.", "livechat_add_to_cart_children": "Options disponibles: {{children_title}}[BR][/BR]choisissez l'une des options.", "add_to_cart_variant": "Les options de produits sont répertoriées ci-dessous. Écrivez le numéro à côté de celui que vous voulez choisir.[BR][/BR]{{variant_title}}[BR][/BR][BR][/BR]{{variant}}[BR][/BR]", "whatsapp_add_to_cart_variant": "Options disponibles: {{variant_title}}[BR][/BR][BR][/BR]choisissez l'une des options.", "livechat_add_to_cart_variant": "Options disponibles: {{variant_title}}[BR][/BR]choisissez l'une des options.", "gdpr_url_message": "[BR][/BR]J'ai lu et j'accepte l'accord KVKK.({{gdpr_url}})[BR][/BR]", "terms_of_use_url_message": "[BR][/BR]J'ai lu et j'accepte le contrat d'adhésion.({{terms_of_use_url}})[BR][/BR]", "has_been_changed_of_product_count": "dans votre panier {{product_name}} nombre de produit {{number}} changé en", "arvia_chat_message": "Vous pouvez nous contacter via le lien ci-dessous pour un appel vidéo ou audio.\n{{url}}"}, "ask_form_questions": {"first_name": "Quel est votre prénom ?", "last_name": "Quel est votre nom de famille?", "identity_number": "Veuillez entrer votre district", "birth_date": "Quelle est votre date de naissance?", "email": "Pouvez-vous écrire votre adresse e-mail?", "title": "Quel est le titre de votre adresse? (du travail, du domicile, etc.)", "fullname": "Quel est votre nom et prénom?", "address": "Pouvez-vous écrire votre adresse?", "post_code": "Pui<PERSON>-je obtenir le code postal?", "phone_number": "Veuillez écrire votre nom.", "password": "Entrez votre mot de passe", "district_name": "Statuts de commande", "district_code": "Quel est votre comté ?", "address_type": "<PERSON> d'adresse", "name": "Veuillez sélectionner d'abord le mode de paiement.", "company_name": "Votre secteur", "tax_office": "<PERSON>ui<PERSON>-je prendre votre numéro de téléphone ?", "tax_number": "temp_id introuvable", "zip": "<PERSON><PERSON><PERSON>  une commande", "province": "Il n'y a pas d'article dans votre panier.", "other_phone": "Individuel", "town_code": "Paiement", "is_same_delivery_and_invoce_address": "Votre adresse de livraison a été confirmée avec succès. Si vous souhaitez sélectionner cette adresse comme adresse de facturation, appuyez sur [B]1[/B] ou si vous ne la souhaitez pas, appuyez sur [B]2[/B].", "whatsapp_is_same_delivery_and_invoce_address": "Votre adresse de livraison a été confirmée avec succès. Voulez-vous sélectionner cette adresse comme adresse de facturation ?", "gender": "Nom Facture", "invoice_type": "Quel est votre type de facture ? (Particulier ou entreprise)", "country_name": "Envoi du récapitulatif de la commande réussi!", "city_name": "Commande", "town_name": "sequence", "become_member": "Étant donné que vous magasinez sans abonnement, vous ne pourrez pas payer avec une carte de crédit. Si vous voulez toujours continuer [B]Yes[/B] écrivez.", "whatsapp_become_member": "Étant donné que vous magasinez sans abonnement, vous ne pourrez pas payer avec une carte de crédit. Si vous souhaitez toujours continuer, veuil<PERSON><PERSON> confirmer", "get_next_page_for_products": "Tapez [B]0[/B] pour voir d'autres produits.", "whatsapp_get_next_page_for_products": "Cliquez pour voir d'autres produits.", "birth_month": "quel est ton mois de naissance ?", "birth_year": "Quelle est votre année de naissance ?", "birth_day": "quel est ton anniversaire?", "mail_notify": "Souh<PERSON><PERSON>-vous recevoir une notification par courrier ?", "kvkk": "Acceptez-vous le contrat d'adhésion ?"}}, "campaign": {"send_campaign": "Bonjour, vos options de campagne sont indiquées ci-dessous dans l'ordre. Quelle option de campagne souhaitez-vous utiliser ? Veuillez spécifier le [B]numéro[/B]. [BR][/BR][BR][/BR]{{campaign_items}}", "whatsapp_send_campaign": "Bon<PERSON>r, vos options de campagne sont indiquées ci-dessous dans l'ordre. Quelle option de campagne souhaitez-vous utiliser ?"}, "support": {"solved": "R<PERSON>ol<PERSON>", "pending": "En attente", "unsolved": "Non résolu", "in_process": "En cours", "normal": "Normal", "danger": "<PERSON><PERSON>"}, "customer_evaluation": {"evaluation_message": "Avez-vous été satisfait de notre service ? Vous pouvez nous aider à mieux vous servir en évaluant votre expérience.", "evaluation_message_5": "Fantastique 😍", "evaluation_message_4": "Bien 😊", "evaluation_message_3": "Belle 🙂", "evaluation_message_2": "Mal 😑", "evaluation_message_1": "Misérable 😤", "evaluation_message_text": "Ave<PERSON>-vous été satisfait de notre service ? Vous pouvez nous aider à mieux vous servir en évaluant votre expérience.[BR][/BR][BR][/BR]{{rows}} ", "evaluation": "évaluez-le", "customer_evaluation_setting": "Paramètre d'évaluation client", "customer_evaluation_timeout": "Temps de réglage de l'évaluation du client après la commande (minutes)", "evaluation_type_error": "Le type d'évaluation n'est pas valide.", "evaluation_options": "Sélection du statut d'expédition", "evaluation_option_1": "Une fois la vente conclue", "evaluation_option_2": "Une fois le client archivé"}, "agent_report": {"not_found": "Rapport introuvable", "date_invalid": "Date invalide"}, "thinker": {"now": "Initialiser Thinker en envoyant un message", "wait": "Initialiser Thinker après avoir reçu un message du client", "start_type": "Type de démarrage de Thinker", "flow_id": "Nom du flux Thinker", "status": "Statut de Thinker", "thinker": "Thinker"}, "chat_action": {"action_not_found": "Action introuvable", "system": "système", "customer": "cliente", "moved_to_public": "La discussion a été suspendue par {{name}}", "system_moved_to_public": "Le chat a été mis en attente par le système", "archived_by_system": "Chat archivé par le système", "chat_hide": "Discussion archivée par {{name}}", "system_chat_hide": "Discussion archivée par {{name}}", "block": "<PERSON><PERSON> bloqué par {{name}}", "unblock": "<PERSON><PERSON> <PERSON><PERSON> par {{name}}", "system_block": "<PERSON><PERSON> bloqué par {{name}}", "assign_to_team": "Chat attribué à {{team}} par {{name}}", "assign_to_agent": "Chat attribué à l'opérateur {{assigned_user}} par {{name}}", "add_chat_tag": "Balise {{tag}} a<PERSON><PERSON>e au contact par {{name}}", "delete_chat_tag": "{{tag}} par la personne {{name}} a été supprimé", "update_user_profile": "Les informations de profil de la personne ont été mises à jour par {{name}}", "started_flow": "<PERSON><PERSON> transfé<PERSON> à Thinker par {{name}}", "stopped_flow": "Flux de discussion de Thinker arrê<PERSON> par {{name}}", "received_chat": "Le chat a été pris par {{name}}", "owner_forced_chat": "Le chat a été détourné par {{name}}", "customer_assign_to_team": "Chat transféré à l'équipe par {{name}}", "user_profile_customer_note": "The customer's note information was updated from {{old_customer_note}} to {{new_customer_note}} by {{name}}", "user_profile_email": "Les informations de messagerie du client de {{old_email}} ont été mises à jour vers {{new_email}} par {{name}}", "user_profile_first_name": "Les informations relatives au nom du client ont été mises à jour de {{old_first_name}} à {{new_first_name}} par {{name}}", "user_profile_last_name": "Les informations de nom de famille du client ont été mises à jour de {{old_last_name}} à {{new_last_name}} par {{name}}", "user_profile_phone_number": "Les informations téléphoniques du client de {{old_phone_number}} ont été mises à jour vers {{new_phone_number}} par {{name}}", "add_new_address": "<PERSON>resse intitulée {{address_title}} ajou<PERSON>e par {{name}}", "edit_address": "Adresse {{address_title}} mise à jour par {{name}}", "delete_address": "<PERSON>'adresse {{address_title}} a été supprimée par {{name}}", "selected_address": "'{{address_title}}' l'adresse a été choisie par {{name}}", "selected_cargo": "La cargaison '{{address_title}}' a été sélectionnée par {{name}}", "selected_payment": "Le mode de paiement {{payment_title}} a été choisi par {{name}}", "deleted_agent_pending_list": "Depuis que l'opérateur {{deleted_agent_name}} a été supprimé par {{name}}, le chat a été transféré sur la liste d'attente.", "deleted_agent_archived_list": "Depuis que l'opérateur {{deleted_agent_name}} a été supprimé par {{name}}, le chat a été transféré sur la liste d'archive.", "noc_moved_to_public": "Chat transféré vers la liste en attente", "campaign_code_applied": "Code de réduction {{coupone_code}} appliqué par {{name}}", "campaign_activated": "La campagne {{campaign_name}} a été mise en œuvre par {{name}}", "campaign_remove": "La campagne '{{campaign_name}}'a été supprimée par {{name}}", "thinker_bot_stopped": "<PERSON><PERSON> a<PERSON><PERSON><PERSON> par Thinker", "pinned": "<PERSON><PERSON> par {{name}}", "remove_archive": "Le chat a été supprimé des archives par {{name}}", "mark_as_unread": "Chat marqué comme non lu par {{name}}", "mark_as_read": "Chat marqué comme lu par {{name}}", "started_helobot": "<PERSON><PERSON> trans<PERSON><PERSON><PERSON> Helobot par {{name}}", "stopped_helobot": "<PERSON><PERSON><PERSON> He<PERSON>bot par {{name}}", "system_stopped_helobot": "Helobot a été arrêté par le système", "system_stopped_helobot_error": "<PERSON><PERSON>bot a été arrêté par le système en raison d'une erreur '{{error}}' survenue dans Helobot.", "connected_user": "Client connecté", "disconnected_user": "Connexion client terminée.", "website_action": "Client actuellement sur la page -> '{{website}}'", "helobot_timeout": "<PERSON><PERSON><PERSON> arrêté en raison du délai d'attente", "thinker_flow_timeout": "Le flux Thinker a expiré.", "timeout_flow_start": "Le flux Thinker s'est terminé et le flux de délai a commencé."}, "translate": {"text_invalid": "Le texte n'est pas valide", "target_language": "La langue vers laquelle convertir n'est pas valide"}, "message": {"customer_not_replied_message": "Votre session a été interrompue car nous n'avons pas reçu de réponse de votre part depuis longtemps.", "archived_message_text": "Comment pouvons-nous vous aider?"}, "user_log": {"edit_user": "{{agent_name}} a modifié l'utilisateur {{user_name}}.", "new_user": "L'utilisateur {{name}} a été ajouté. (Email : {{email}}, Type : {{type}})", "delete_user": "L'utilisateur {{name}} a été supprimé.", "create_company": "{{agent_name}} a créé une entreprise nommée {{name}} avec le numéro de téléphone {{phone_number}}.", "status_change_company": "{{agent_name}} a changé le statut de l'entreprise {{name}} à {{status}}.", "edit_company": "{{agent_name}} a modifié les informations de l'entreprise {{company_name}}.", "create_channel": "{{agent_name}} a créé le canal avec l'ID {{ext_id}}.", "delete_channel": "{{agent_name}} a supprimé le canal nommé {{name}}.", "edit_channel": "{{agent_name}} a modifié les informations du canal {{channel_name}}.", "archived_channel_chats": "{{agent_name}} a archivé les discussions du canal {{channel_name}}.", "archived_channel_chats_with_date": "{{agent_name}} a archivé les discussions du canal {{channel_name}} entre {{start_date}} et {{end_date}}.", "remove_token_for_facebook": "{{agent_name}} a supprimé le jeton d'accès pour le canal {{channel_name}}.", "remove_credit_line": "{{agent_name}} a supprimé le mode de paiement pour le canal {{channel_name}}.", "add_credit_line": "{{agent_name}} a ajouté un mode de paiement au canal {{channel_name}}.", "add_integration": "{{agent_name}} a ajouté une intégration nommée {{integration_name}} de type {{type}}.", "edit_integration": "{{agent_name}} a modifié l'intégration nommée {{integration_name}}.", "login": "{{agent_name}} s'est connecté au système.", "logout": "{{agent_name}} s'est déconnecté du système.", "message_export": "{{agent_name}} a commencé à exporter les messages du canal {{channel_name}} entre {{start_date}} et {{end_date}}.", "channel_status": "{{agent_name}} a changé le statut du canal {{channel_name}} à {{status}}.", "profile_edit": "{{agent_name}} a mis à jour ses informations de profil.", "add_tag": "{{agent_name}} a ajouté une étiquette nommée {{tag_name}}.", "edit_tag": "{{agent_name}} a modifié l'étiquette nommée {{tag_name}}.", "thinker_register": "{{agent_name}} a activé Thinker.", "helobot_register": "{{agent_name}} a activ<PERSON>.", "mail_register": "{{agent_name}} a activé le système de messagerie.", "message_template_register": "{{agent_name}} a activé le modèle de message.", "hepsiburada_register": "{{agent_name}} a activé <PERSON>.", "trendyol_register": "{{agent_name}} a activé <PERSON>.", "pazarama_register": "{{agent_name}} a activé <PERSON>.", "n11_register": "{{agent_name}} a activé N11."}}, "Worker": {"run": {"job_not_found": "Job introuvable.", "job_data_not_found": "Données du travail introuvables.", "job_type_not_implemented": "Le type du travail n'a pas pu être implémenté.", "channel_not_implemented": "Le canal n'a pas été implémenté."}}, "Noc": {"errors": {"channel": {"id_not_found": "Identifiant de chaîne introuvable", "not_found": "<PERSON><PERSON><PERSON> introuvable", "missing": "Informations sur la chaîne manquantes", "provider_not_found": "Informations sur le fournisseur introuvables", "type_not_found": "<PERSON><PERSON>z les informations introuvables", "name_not_found": "Informations sur le nom introuvables", "provider_not_support": "Actuellement, ce fournisseur de canal n'est pas pris en charge.", "type_not_support": "Actuellement, ce type de canal n'est pas pris en charge.", "allready_exist": "Canal déjà attribué", "access_token_not_found": "paramètre access_token introuvable", "ext_id_not_found": "paramètre ext_id introuvable", "page_id_not_found": "Identifiant de page introuvable", "is_active": "Sélectionnez Actif ou Passif", "limit_invalid": "Veuillez entrer une valeur limite valide.", "unlimit_invalid": "Les informations illimitées ne sont pas valides.", "no_right_add_channels": "Vous n'avez pas le droit d'ajouter de nouvelles chaînes.", "credit_line_exists": "La ligne de crédit existe déjà"}, "container": {"code": "Code de conteneur introuvable", "subdomain": "Sous-domaine introuvable", "not_login": "Impossible de se connecter au conteneur.", "not_found_certificate": "Certificat introuvable.", "method": "Méthode d'envoi de code introuvable.", "invaid_method": "Méthode de soumission de confirmation non valide.", "exist": "Ce conteneur existe déjà", "invalid_phone": "Veuillez ne pas utiliser de caractères spéciaux", "not_found_phone": "Veuillez envoyer le numéro de téléphone", "id_not_found": "ID de conteneur introuvable", "not_found": "Conteneur introuvable", "missing": "Informations sur le conteneur manquantes", "cant_get_container": "Échec de la récupération des conteneurs"}, "company": {"id_not_found": "Identifiant de l'entreprise introuvable", "not_found": "Entreprise introuvable", "missing": "Informations sur l'entreprise manquantes", "number_allready_using": "Le nom de l'entreprise ou le numéro de téléphone est déjà utilisé. Veuillez choisir un autre numéro ou nom.", "name_not_found": "Le nom de l'entreprise est requis", "phone_not_found": "Le numéro de téléphone est requis", "limit_number_small_count_agent": "Le nombre limite saisi est inférieur au nombre actuel d'agents."}, "integration": {"login": "Échec de la connexion au système spécifié", "baseurl": "Veuillez saisir l'URL de base du client. échantillon: https://example.com", "username": "Entrez le nom d'utilisateur du service Web", "password": "Entrez le mot de passe de l'utilisateur du service Web", "not_found": "Intégration introuvable", "missing": "Informations d'intégration manquantes", "service_missing": "Informations sur le service Web d'intégration manquantes", "baseurl_not_found": "URL introuvable", "type_not_found": "Type introuvable", "link_error": "Veuillez supprimer le '/' à la fin du lien", "settings_key_error": "Une erreur s'est produite dans les paramètres de la clé", "login_key": "La clé de connexion est incorrecte"}, "auth": {"login_not_found": "Utilisateur non trouvé", "no_noc_user": "L'utilisateur n'est pas un utilisateur NOC", "active": "L'utilisateur n'est pas actif", "bcrypt_error": "Mot de passe utilisateur introuvable", "no_permission": "Autorisation utilisateur introuvable", "allready_email": "Ce courriel existe déjà"}, "company_log": {"setup_id": "Aucune information d'identification trouvée", "not_found": "Aucun enregistrement trouvé", "allready_exist": "Actions déjà entreprises pour cet enregistrement"}, "user": {"not_found": "Utilisateur non trouvé", "missing": "Informations utilisateur manquantes", "email_regex": "Adresse e-mail invalide", "username_not_found": "Nom d'utilisateur introuvable", "password_not_found": "Mot de passe introuvable", "email_exist": "Cet e-mail est déjà utilisée", "email_not_found": "Email non trouvé"}, "embedded_signup": {"short_live_token_not_found": "jeton introuvable", "data_not_found": "informations non trouvées", "business_management_not_found": "Identifiant de gestion commerciale introuvable. Vérifier les autorisations", "whatsapp_business_management_not_found": "Identifiant Whatsapp Business Management introuvable. Vérifier les autorisations", "not_found_whatsapp_numbers": "Le numéro de l'utilisateur enregistré sur Whatsapp Business est introuvable."}, "report": {"period_not_found": "Sélectionnez la période"}, "mobile": {"id_not_found": "Aucune version trouvée dans ID.", "changelog_invalid": "Changelog invalide.", "version_invalid": "Version invalide.", "force_upgrade_invalid": "Force upgrade invalide."}, "version": {"name_invalid": "Le champ Nom n'est pas valide", "text_invalid": "Le champ de texte n'est pas valide", "id_invalid": "Le champ ID n'est pas valide", "version_not_found": "Version introuvable"}, "ads_medias": {"not_found": "Informations sur la publicité introuvables", "id_not_found": "ID non valide", "name_not_found": "Nom non valide", "url_not_found": "URL non valide", "redirect_url_not_found": "URL de redirection non valide", "is_active_not_found": "État is_active non valide", "new_index_not_found": "new_index non valide"}, "message": {"contents_not_found": "Contenu du message introuvable"}}, "onboarding_wizards": {"long_lived_token": "Jeton à long terme", "connected_instagram_account": "Compte Instagram lié", "name": "Nom", "username": "Nom d'utilisateur", "profile_picture_url": "Lien de l'image de profil", "display_phone_number": "Numéro de téléphone", "wp_status": "Statut WhatsApp", "customer_business_name": "Nom commercial du client", "customer_business_link": "Lien commercial client", "certificate": "Certificat"}}, "UnNameUser": "Client anonyme", "Onboarding": {"errors": {"conversation_count": "Le nombre de discussions mensuelles n'est pas valide.", "package_price_not_found": "Les informations sur les frais du forfait ne sont pas valides", "name_not_found": "Veuillez saisir un nom.", "phone_not_found": "S'il vous plaît entrer un numéro de téléphone valide.", "invalid_email": "S'il vous plaît, mettez une adresse email valide.", "code_not_found": "Les informations de code sont obligatoires.", "onboarding_wizard_id_not_found": "id Informations Zorunludur.", "embeddedInfo_not_found": "Aucun enregistrement intégré trouvé.", "onboardingWizard_not_found": "Aucun enregistrement à bord trouvé.", "invalid_privacy_policy": "Politique de confidentialité Sélection requise", "invalid_pdpa": "La sélection KVKK est obligatoire", "newsletter": "Sélection du bulletin électronique requise", "email_valid": "ce courriel existe déjà", "service_type_not_found": "Informations sur le type de service requises.", "integration_type_not_found": "Aucune information d'intégration trouvée. Veuillez sélectionner au moins une page Instagram et une page Facebook ou vérifier les autorisations nécessaires", "package_id_not_found": "ID de paquet introuvable", "features_ids_not_found": "Informations sur la propriété non trouvées", "package_not_found": "Paquet introuvable", "id_not_found": "je n'ai pas trouvé", "access_token_not_found": "Informations Access_token introuvables", "instagram_account_not_exist": "Votre compte Instagram introuvable", "facebook_account_does_not_exist": "Votre compte Facebook introuvable", "wrong_instagram_account_selected": "Vous avez essayé de connecter différents comptes Instagram. Veuillez sélectionner le compte Instagram {{name}} ou contacter le support.", "wrong_facebook_account_selected": "Vous avez essayé de connecter différents comptes Facebook. Veuillez sélectionner le compte Facebook {{name}} ou contacter le support.", "token_invalid_or_missing_permission": "Le jeton n'est pas valide ou l'autorisation accordée n'est pas suffisante.", "facebook_error_message": "Message d'erreur Facebook: {{message}}.", "missing_scopes": "<PERSON><PERSON><PERSON> manquantes: {{scopes}}.", "missing_granular_scopes": "Étendues granulaires manquantes: {{granularScopes}}.", "company_name_not_found": "Nom de l'entreprise introuvable", "tax_office_not_found": "Bureau des impôts introuvable", "tax_or_identity_number_not_found": "Numéro de taxe introuvable", "pdpa_not_found": "Accepter l'accord KVKK", "select_package": "Veuillez sélectionner le forfait", "not_get_facebook_token": "Échec de l'obtention du jeton Facebook", "not_get_facebook_fields": "Vos informations n'ont pas pu être obtenues de Facebook.", "info_is_not_yet": "Information insuffisante. Veuillez compléter les étapes précédentes", "container_problem": "Le conteneur ne peut pas créer", "onboarding_wizard_type": "Choisissez la méthode d'envoi du code de confirmation", "onboarding_wizard_code": "Pas de code de confirmation", "phone_number_valid": "Ce numéro de téléphone est déjà utilisé", "whatsapp_number_not_found": "Pas de numéro Whatsapp", "unauthorized_action": "Action non Autorisée", "allready_exists_company": "Cette entreprise est déjà enregistrée", "please_try_again": "Veuillez réessayer plus tard", "invalid_token_error": "Le message n'a pas pu être envoyé. Essayez de vous reconnecter à votre page {{page_name}}. {{page_name}} Impossible de se connecter à votre page.", "session_not_found": "séance introuvable", "self_onboard_not_found": "L'auto-intégration n'est pas effectuée", "package_already_exists": "Le forfait actif existe déjà."}, "info": {"whatsapp_process": "Votre installation est en cours. Vous serez contacté dans les 2-4 jours ouvrables.", "login_data": "Bonjour, votre inscription a été reçue avec succès par HeloRobo. Vous pouvez vous connecter avec les informations suivantes.<br/><br/>E-mail: {{email}}<br/>Mot de passe: {{password}}", "whatsapp_email_header": "Informations sur HeloRobo", "login_header": "Informations requises pour la connexion HeloRobo"}}, "Package": {"starter": "D<PERSON>but", "instagram": "Instagram", "tariff": "<PERSON><PERSON><PERSON>", "control_panel": "Helorobo (Panneau d'administration)", "waba": "WhatsApp Business API", "faba": "Utilisateur de Facebook Messenger", "facebook_eshop": "Facebook eShops", "instagram_eshop": "Instagram eShops", "message": "1.000 SMS", "template_message": "<PERSON><PERSON><PERSON><PERSON>", "shop_integration": "Intégration du commerce électronique", "message_package": "Paquets de messagerie supplémentaires", "heloscope": "Heloscope"}, "Advermind": {"token_not_found": "<PERSON><PERSON><PERSON>z vous connecter", "accesstoken_not_found": "<PERSON><PERSON> d'accès Advermind introuvable", "campaign_id_not_found": "Informations sur l'ID de campagne introuvables", "campaign_status_not_found": "Informations sur l'état de la campagne introuvables", "not_found_facebook_page": "Pour ce faire, votre Page Facebook doit être enregistrée par HeloRobo."}, "Mobile": {"not_found_version": "Version introuvable"}, "Api": {"webhook": {"url_invalid": "L'URL n'est pas valide", "hash_invalid": "Le hachage n'est pas valide", "content_caption_invalid": "Le caption du contenu n'est pas valide", "context_url_invalid": "L'URL du contenu n'est pas valide", "type_invalid": "Le type n'est pas valide", "chat_id_invalid": "L'identifiant de chat n'est pas valide", "channel_id_invalid": "l'identifiant de la chaîne n'est pas valide"}}, "Thinker": {"errors": {"thinker_not_found": "Enregistrement du Thinker introuvable", "name_invalid": "Le nom n'est pas valide", "message_url_id_invalid": "L'ID de l'URL du message n'est pas valide", "flow_id_invalid": "L'ID de flux n'est pas valide", "start_type_invalid": "Le type de démarrage n'est pas valide", "url_invalid": "L'URL n'est pas valide", "hash_invalid": "Le hachage n'est pas valide", "id_invalid": "l'identifiant est invalide", "channel_not_found": "Veuillez sélectionner le canal"}, "stopped": "Flux arrêté"}, "MessageTemplate": {"errors": {"message_template_not_found": "Enregistrement du Message Template introuvable", "company_admin_required": "Veuillez d'abord créer le propriétaire de l'entreprise. Inscrivez-vous plus tard"}}, "Reseller": {"errors": {"hash_invalid": "informations hash non valides", "timestamp_invalid": "informations timestamp non valides", "reseller_key_invalid": "informations reseller_key non valides", "company_name_invalid": "informations company_name non valides", "company_phone_invalid": "informations company_phone non valides", "allready_exists_company": "Cette entreprise est déjà enregistrée"}}}