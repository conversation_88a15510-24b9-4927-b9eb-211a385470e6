{"Global": {"chat_message": {"add": "Add", "add_cart": "<PERSON><PERSON>", "select": "Select", "options": "Options", "select_address": "Select Address", "select_cargo": "Select Shipping", "select_payment": "Select Payment", "select_campaign": "Select Campaign", "confirm": "Approve", "confirm_cart": "Approve Cart", "next": "Next", "yes": "Yes", "no": "No", "empty_basket": "Take a Retrospective Action on an Expired or Purchased Product.", "no_exist_product": "No Products to Display", "order_status": "Order Status", "cart": "Confirm to add to cart", "select_account": "Account List", "confirm_order": "Confirm Order", "thanks": "Thanks", "hello": "Hello"}, "errors": {"two_fa_code_not_found": "2FA code is not found. Please enter a valid 2FA code.", "two_fa_code_invalid": "Invalid 2FA code. Please enter a valid 2FA code.", "two_fa_code_string": "Invalid 2FA code format. Please enter a 6-digit 2FA code.", "two_fa_not_found": "twoFA_status information not found.", "two_fa_allready_open": "2FA Already Enabled.", "two_fa_allready_close": "2FA Already Disabled.", "token_required": "Token id and token hash required.", "token_not_found": "To<PERSON> not found.", "token_deleted": "<PERSON><PERSON> deleted.", "token_hash_not_some": "Token hash not same.", "token_expired": "<PERSON><PERSON> expired.", "page_not_found": "Could not find the page that you are looking for.", "not_authorized": "You are not authorized to do this operation.", "package_not_authorized": "You cannot perform this operation with the package you are using. Please upgrade the package.", "package_expire_not_authorized": "The time allocated for this operation in your package has expired. Please upgrade your package.", "package_expire": "Your package has expired. Please renew your package.", "not_permissions": "You are not permission to do this operation.", "conversation_package": "Please purchase an additional message package.", "company_not_found": "Company is not found.", "company_waba_not_found": "Not Found Waba id of Company.", "channel_waba_not_found": "Not Found Waba id of Channel. Please Contact System Administrator.", "company_id_not_found": "Company ID is required.", "integration": {"address_not_found": "The addressId parameter is required.", "not_found": "Integration not found.", "couponcode_not_found": "Coupon code not found."}, "chat": {"pinned_chat_max_count": "You can pin up to 50 customers.", "chat_not_found": "The chatId parameter is required."}, "whatsapp_message_error": "An unexpected error has occurred on the Whatsapp side.", "instagram_message_error": "An unexpected error has occurred on the Instagram side.", "pagination_not_found": "No Paging Information Found", "page_count_not_found": "Page Count not found.", "perpage_not_found": "Paging Count not found.", "secret_key_not_found": "Secret Key Not Found", "secret_key_error": "Wrong Secret Key", "username_not_found": "Email Not Found", "password_not_found": "Password Not Found", "chat_list_not_found": "Please Select Chat List", "billtekrom_account_id_not_found": "Billtekrom Account ID not found in this Company", "billtekrom_account_not_found": "<PERSON><PERSON><PERSON><PERSON> Account not found in this Company", "status": "status information not found", "platform": "platform information not found", "not_supported": "This feature is not supported"}, "form_field": {"first_name": "Name", "last_name": "Surname", "fullname": "Fullname", "email": "Email", "phone": "Phone Number", "account_info": "Membership information", "address_info": "Address information", "address1": "Address", "city": "City", "province": "Province", "zip": "Zip", "country": "Country", "company": "Company Name", "password": "Password", "gender": "Gender", "birth_date": "Birth Day", "birth_month": "Birth Month", "birth_year": "Birth Year", "address": "Address", "mail_notify": "Mail Notify", "yes": "Yes", "no": "No", "woman": "Woman", "man": "Man", "member_contract": "Member Contract", "address_type": "Address Type", "individual": "Individual", "institutional": "Institutional", "address_title": "Address Title", "identity_number": "Identity Number", "district": "District", "post_code": "Post Code", "tax_administration": "Tax Administration", "tax_number": "Tax Number", "nationality": "I am not a Turkish citizen", "all": "All"}, "language_code": {"tr": "Turkish", "ar": "Arabic", "fr": "French", "de": "German", "en": "English", "ru": "Russian", "default": "<PERSON><PERSON><PERSON>"}}, "Site": {"validators": {"login": {"email_field_required": "Mail field is required.", "mail_lenght": "Mail field length error.", "password_field_required": "Password field is required."}, "add_new_user": {"name_field_required": "Name field is required.", "name_length": "Name length is not valid.", "email_field_required": "Mail field is required.", "not_valid_email": "Mail field does not contain valid mail address.", "mail_lenght": "Mail field length error.", "password_field_required": "Password field is required.", "password_repeat_required": "Password Repeat field is required."}, "user_update": {"name_field_required": "Name field is required.", "name_length": "Name length is not valid."}, "forgot_password": {"email_field_required": "Mail field is required.", "not_valid_email": "Mail field does not contain valid mail address."}, "set_new_password": {"new_password_field_required": "New Password field is required.", "new_password_length": "New Password length is not valid.", "new_password_repeat_field_required": "New Password Repeat field is required.", "new_password_repeat_length": "New Password length is not valid.", "passwords_are_not_equal": "Password and Password again does not match."}, "edit_user": {"name_field_required": "Name length is not valid.", "email_field_required": "Mail field is required.", "new_password_field_required": "New Password field is required.", "new_password_repeat_field_required": "New Password again field is required.", "passwords_are_not_equal": "Password and Password again does not match."}, "update_profile": {"name_field_required": "Name length is not valid.", "password_field_required": "Password field is required.", "password_repeat_required": "Password Repeat field is required.", "passwords_are_not_equal": "Password and Password again does not match."}, "contact_form_message": {"first_name_required": "Name field is required.", "name_length": "Name length is not valid.", "last_name_required": "Last Name field is required.", "last_name_length": "Last Name length is not valid.", "email_field_required": "Mail field is required.", "not_valid_email": "Mail field does not contain valid mail address.", "mail_length": "Mail length is not valid.", "subject_field_required": "Subject field is required.", "subject_length": "Subject length is not valid.", "content_field_required": "Content field is required.", "content_field_length": "Content length is not valid."}, "try_form_message": {"first_name_required": "Name field is required.", "name_length": "Name length is not valid.", "last_name_required": "Last Name field is required.", "last_name_length": "Last Name length is not valid.", "email_field_required": "Mail field is required.", "not_valid_email": "Mail field does not contain valid mail address.", "mail_length": "Mail length is not valid.", "phone_field_required": "Phone field is required.", "phone_length": "Phone length is not valid.", "company_name_field_required": "Company Name field is required.", "company_name_length": "Company length is not valid.", "business_sector_field_required": "Business Sector field is required.", "business_sector_length": "Business Sector length is not valid.", "staff_count_field_required": "Staff Count field is required.", "staff_count_length": "Staff Count length is not valid.", "content_field_required": "Content field is required.", "content_field_length": "Content length is not valid."}, "unhandled_error": "Unhandled error.", "channel": {"name_cannot_be_empty": "Name field cannot be empty.", "type_cannot_be_empty": "Type field cannot be empty."}}, "errors": {"main": {"captcha": "<PERSON><PERSON> is wrong.", "row_count": "Please set the number of products to be displayed in each row to 2, 3, 4, 5."}, "auth": {"credentials_not_valid": "Invalid Credentials.", "login": {"user_is_passive": "You can not login becuase your user is passive.", "user_not_available_in_user_table": "Could not connect, please contact the administrator."}, "set_new_password": {"url_is_invalid": "The 'Set New Password' url is invalid. Please fill in the 'Forgot Password Form' again.", "token_user_not_found": "Invalid Credentials."}}, "user": {"login": {"not_permitted": "You are not permitted to do this operation.", "agent_is_not_in_your_company": "You are not permitted to do this operation.", "user_not_active": "User is not active", "user_not_permitted": "User is not permitted to login.", "user_not_found": "Couldn't find the user"}, "add_new_user": {"user_exist": "This e-mail address is added to the system. Please try another e-mail address."}, "enable_user": {"associated_user_not_found": "Associated user not found.", "not_in_your_company": "Agent is not in your company.", "already_enabled": "Agent is already enabled.", "self_edit": "You can not change your user status."}, "disable_user": {"associated_user_not_found": "Associated user not found.", "not_in_your_company": "Agent is not in your company.", "already_disabled": "Agent is already disabled.", "self_edit": "You can not change your user status."}, "edit_user": {"associated_login_not_found": "Associated login not found.", "associated_user_not_found": "Associated user not found.", "not_in_your_company": "Agent is not in your company."}, "edit_permissions": {"cannot_change_owner_permission": "You can not change the company owner's permissions.", "associated_user_not_found": "Associated user not found.", "invalid_self": "You can't change your permissions."}}, "dash": {"enable_channel": {"associated_channel_not_found": "There is not a channel associated channel.", "not_in_your_company": "There is not a channel associated channel.", "already_enabled": "Channel already enabled."}, "disable_channel": {"associated_channel_not_found": "There is not a channel associated channel.", "not_in_your_company": "There is not a channel associated channel.", "already_disabled": "Channel already disabled."}, "enable_integration": {"already_enabled": "Integration already enabled."}, "disable_integration": {"already_disabled": "Integration already disabled."}, "update_profile": {"user_not_found": "User not found.", "old_password_wrong": "Old password wrong.", "associated_user_not_found": "Associated user not found."}}, "channel": {"unkown_channel_type": "Unknow Channel Type."}}, "success": {"user": {"added": "Agent successfully created.", "enabled": "Agent enabled.", "disabled": "Agent de-activated.", "edited": "Agent information successfully changed.", "permissions_saved": "Agent permissions successfully saved."}, "dash": {"profile_updated": "User profile updated successfully.", "logout_success_message": "<PERSON><PERSON>ut Successfully.", "channel_enabled": "Channel activated.", "channel_disabled": "<PERSON> disabled.", "integration_enabled": "Integration Activated", "integration_disabled": "Integration Disabled"}, "auth": {"set_new_password": "Your password has been changed successfully."}, "main": {"contact_form_message_sent": "Contact form successfully sent.", "try_form_message_sent": "Try form successfully sent."}, "create_channel_success": "Channel created succesfully", "edit_channel_success": "Channel edited successfully.", "create_try_success": "Your trial form has been created successfully."}, "channels": {"edit": {"title_TR": "Welcome Message ( Turkish )", "title_EN": "Welcome Message"}}, "channel_types_live_chat": "Live Chat", "permissions": {"options": {"please_select": "Select", "allow": "Allow", "deny": "<PERSON><PERSON>"}, "edit_permissions": {"user": {"create": "Makes the user's authority to add new operators active / inactive.", "edit": "Makes the users active / passive the authority to change the properties of different users.", "enable": " It makes the user active, thus the login of the user becomes active.", "disable": "The user makes it passive, so the user's login becomes inactive.", "edit_permissions": "The user's authority to regulate their privileges.", "update": "Enables/disables users to update features of different users."}, "app": {"login": "The user activates / passives the APP application login.", "login_as_agent": " Enables / disables the user's access to the APP application from the panel."}, "channel": {"create": "Makes the user active / inactive the authority to create channels.", "edit": "Makes the channel editing authority active / inactive for the user.", "update": "Enables/disables the user to update the channel."}, "site": {"login": "Makes the user active / inactive in entering the site."}, "quick_reply": {"update": "Enables/disables updating quick reply feature."}, "integration": {"update": "Integration makes update active/disabled.", "create": "Integration makes active/passive."}, "thinker": {"enable": "Enables/disables access to the Thinker."}, "helobot": {"enable": "Enables/disables access to Helobot."}, "reports": {"enable": "Enables/disables access to Reports."}, "mail_service": {"enable": "Enables/disables access to Mailbox."}, "hepsiburada": {"enable": "Enables/disables access to Hepsiburada."}, "trendyol": {"enable": "Enables/disables access to Trendyol."}, "pazarama": {"enable": "Enables/disables access to Pazarama."}, "social_marketing": {"enable": "Enables/disables access to Social Media Management."}, "chat_force_took": {"enable": "Makes the authority to receive the chat from another representative active/passive."}, "message_template": {"enable": "Enables/disables access to Message Template."}}}, "integration": {"days": {"0": "Sunday", "1": "Monday", "2": "Tuesday", "3": "Wednesday", "4": "Thursday", "5": "Friday", "6": "Saturday"}, "small": "Small", "medium": "Medium", "big": "Big"}}, "App": {"errors": {"agent": {"required": "Agent id required.", "move_to": "move_to field is required."}, "user": {"customer_note_not_found": "Customer note information not found", "not_found": "User not found", "name_not_found": "Name not found", "password_invalid": "For the password to be valid, it must follow the following rules:\n{{text}}", "password_uppercase": "- Must contain at least 1 capital letter \n", "password_numeric": "- Must contain at least 1 numeric character \n", "password_special": "- Must contain at least 1 special character (.,*-!) \n", "password_length": "- Must be at least 6 characters", "allready_exists": "User Already Exists"}, "campaign": {"not_found": "Campaign Not Found", "not_member": "The Campaign is Valid for Members Only."}, "conversation": {"not_found": "Conversation not found.", "does_not_belongs_to_you": "Conversation does not belongs to you.", "chat_on_agent": "Conversation is Over a Person named {{name}}. Please Request Him.", "chat_on_agent_with_team": "The conversation is about {{name}} from the {{team}} team. Please Request Him.", "message_cannot_be_empty": "Message can not be empty.", "payment_options_could_not_reached": "Payment options could not be reached.", "content_is_required": "content information is required", "max_1000": "Message Length can be up to 1000 characters", "quick_replies_max_1000": "Quick Reply Length can be up to 1000 characters", "oowh_max_1000": "Working Hours {{lang}} Maximum length can be 1000 characters", "welcome_max_1000": "Welcome Time {{lang}} Can be up to 1000 characters in length", "temp_id_not_found": "temp_id not found", "content_unavaliable": "Invalid message type", "is_not_array": "Please Send User in Bulk.", "key_not_found": "Please Fill in the Mandatory Fields", "chat_received": "{{chat_name}} chat received by {{name}}.", "chats_foward_not_found": "Select which list (Pending - Archive) the person's chats will be placed in.", "unarchive_max_1000": "The length of the message {{lang}} to be sent when the chat is unarchived can be up to 1000 characters."}, "channel": {"not_found": "Channel is not found", "is_not_active": "Channel is not active.", "channel_id_not_found": "Channel id is not blank.", "reviewer_id_not_found": "Customer id is a mandatory parameter.", "wrong_status": "Wrong Status Message", "active_timeout": "Set the Time to Remove from Active Conversations", "notification_sound_status": "Notification Sound Status Cannot Be Blank", "invalid_type": "Invalid Channel Type", "welcome_messaging_setting_not_found": "Welcome Message information not found", "welcome_messaging_setting_en_not_found": "No English Message for the Welcome Message", "welcome_messaging_setting_tr_not_found": "No Turkish Message for the Welcome Message", "min_one": "There must be at least 1 Option.", "max_ten": "There should be a maximum of 10 Options.", "button_text_not_found": "Category Name Missing. Enter All Categories.", "agent_channel_authority": "This agent is not authorized to the channel.", "instagram_quick_replies": "quick_replies not correct", "instagram_quick_replies_not_found": "Selected Quick Reply not Found", "instagram_quick_replies_text_not_found": "Title cannot be left blank", "start_type": "Please Choose the Stream Start Option Correctly", "connection_failed": "Connection failed.", "unarchive_messaging_setting_not_found": "No message information could be found to send when the chat is unarchived", "archive_messaging_setting_not_found": "No message information to send was found while archiving the chat", "only_cloud_api": "This feature is only available for channels connected via Cloud API", "wigdet_size_invalid": "Widget size information is invalid", "name_exists": "A Livechat channel with this name already exists", "cloud_profile": {"about_option": "About must be at most 139 characters.", "address_option": "Address must be at most 256 characters.", "description_option": "Description must be at most 512 characters.", "vertical_option": "Invalid Category", "email_option": "Email must be at most 128 characters.", "websites_option": "Websites must be at most 2 and at most 256 characters each.", "profile_pic_option": "Invalid Picture"}, "words": "Keywords not found", "auto_tag_status": "Invalid auto_tag_status information", "auto_tag_not_found": "No auto-tag assignment record found", "event_data_set": {"invalid": "Invalid Event Name '{{event_name}}'"}}, "auth": {"login_as_agent": {"token_is_not_temp": "Could not find the page that you are looking for.", "token_not_found": "Could not find the page that you are looking for.", "token_invalid": "<PERSON><PERSON> is invalid.", "user_not_found": "User not found.", "user_not_active": "User not active.", "company_not_active": "Company is not active", "company_not_found": "Company not found.", "login": "The information you entered is incorrect, please check your information", "user_not_available_in_user_table": "Could not connect, please contact the administrator.", "email_is_not_found": "Email can not be empty", "jwt_is_not_found": "jwt is not found", "email_invalid": "Please Enter Valid a Email"}, "forget_password": {"login_not_found": "User not found.", "mail_warning": "Please try again in 2 minutes."}, "set_new_password": {"token_not_found": "<PERSON><PERSON> is invalid.", "hashes_dont_match": "<PERSON><PERSON> is invalid.", "token_type_not_correct": "<PERSON><PERSON> is invalid.", "token_expired": "Token is expired.", "token_used": "Invalid Link Address. Please Check Your Last Email.", "passwords_should_not_be_empty": "New password and password repeat fields should not be empty.", "passwords_should_be_equal": "New password and password fields should be same.", "could_not_find_assosiciated_user": "Invalid information given. Please re-enter."}}, "message_report": {"start_date_end_date_not_found": "Must have start time and end time."}, "main": {"log": {"type_not_specified": "Type is not specified.", "message_not_specified": "Message is not specified.", "type_could_not_find": "Type could not find"}}, "dash": {"conversation_is_disabled": "Conversation is disabled.", "conversation_belong_to_someone": "Conversation belong to someone.", "conversation_active_time_expired": "You can not send a message due to messaging timeframe expired."}, "integration": {"domain_invalid": "Domain invalid", "currency_code_not_found": "There is a currency code mismatch", "integration_and_channel_company_ids_do_not_match": "Integration and channel company do not match", "integration_id_cannot_be_empty": "IntegrationId cannot be empty", "action_cannot_be_empty": "Action cannot be empty", "type_cannot_be_empty": "Type cannot be empty", "integration_page_is_wrong": "Integration page is wrong", "user_not_found": "the customer must first become a member.", "cart_is_empty": "Your cart is empty.", "addresses_are_empty": "No address to share.", "chat_id_cannot_be_empty": "Process: <PERSON><PERSON> has not given.", "cargos_are_empty": "No cargo to share.", "quick_reply_not_found": "Quick Reply parameter is not found.", "text_not_found": "Text not found.", "caption_not_found": "Caption not found", "remittance_name_not_provided": "Remittance name not provided", "remittance_id_not_provided": "Remittance id not provided", "keys_not_found": "Keys not found", "keys_limit": "Keys has max 3 element", "lowest_key_limit": "<PERSON> has min 2 element.", "max_key_limit": "Keys has max 10 element.", "duplicate_snipped_text": "Cannot add the same snipped text more than one!", "duplicate_match": "Cannot add the same shorcut more than one !", "sub_payment_option_not_found": "SubPaymentOptionId is not found.", "stock_not_found": "You can not add the product that is out of stock.", "address_data_not_found": "Address info is not found", "campaign_group_id_not_found": "Campaign Group id is not found", "payment_option_id_not_found": "Payment Option id is not found", "cargo_option_id": "Cargo Options is not Found", "integration_not_found": "integration is not found", "info_not_found": "Info is not found", "product_not_found": "Product is not found", "share_address_not_found": "The address to be shared could not be found.", "pay_at_door_options": "Payment information is missing at the door", "oowh_messages": "Please enter the message you want to forward in the relevant field.", "ads_messages": "ads_messages information is missing", "chronological_order": "You need to add the working hours intervals in order.", "start_hour_not_greater_than_end": "start time cannot be greater than end time", "start_hour_not_equal_end": "start time cannot be equal end time", "maximum_hour_range": "A maximum of 3 hours can be entered.", "order_status_bad_request": "Order Status cannot be 0", "is_active_default_oowh_messages": "You are trying to send the default working hour message when Send out of working hours message is not selected. Select to send the working hour message first", "working_hours_empty": "Both watches must be full or empty", "required_seven_for_days": "You need to enter seven value", "first_name_required": "first_name required", "last_name_required": "last_name required", "email_required": "email required", "phone_required": "phone required", "channel_name_not_found": "Channel Name Not Found", "the_product_is_out_of_stock": "The Product is out of stock", "wrong_status": "Wrong Status Message", "there_is_integration": "This user already exist", "working_hour_start_end": "Please do not leave one of the working hours full and the other empty.", "perpage_not_found": "Please Enter Number of Pages First", "perpage_wrong_value": "Please enter values between 1 and 10", "next_or_after": "Just Request Next or Previous Page", "post_not_found": "Post id information not found", "comment_not_found": "Please Write a Comment", "comment_id_not_found": "Comment id not found", "not_found_variant": "Selection Variable not found", "once_cargo": "Select Shipping First", "empty_cart": "Cart Not Found", "user_email_not_found": "Please Save Customer's Email First.", "min_count": "At least {{count}} can be increased", "note_field_length": "The memo field can have a maximum character length of 250", "no_address": "Please Add Address", "page_value": "Page Count <PERSON>or<PERSON>ct", "customer_email_required": "First Email Information Requested From The Customer. Register Your Email", "user_registered": "User registered", "user_paired": "User paired", "user_paired_error": "Kullanıcı Entegrasyonunuzda Bulunamadı. Lütfen Tekrar Eşleştirme Yapın", "user_pairing_removed": "User pairing removed", "user_not_previously_paired": "This user was not previously paired", "phone_number_invalid": "Phone number invalid", "email_invalid": "E-mail invalid", "name_invalid": "Name invalid", "password_invalid": "Password invalid", "post_or_comment_removed": "Action cannot be performed. The post or comment might have been removed. Please check with <PERSON><PERSON>.", "cart_isnot_avaliable": "Cart Inactive", "payment_not_found": "Payment Method not found", "livechat_not_added": "LiveChat has not been added to your site.", "livechat_added": "LiveChat has already been added to your site."}, "settings": {"user_not_found": "User not found.", "assign_chat_to_agent_not_found": "Chat assignment to agent not found", "hide_phone_number_not_found": "Number hiding setting not found", "chat_message_read_status_not_found": "Message read status hide setting not found", "company_owner_get_force_chat_invalid": "Forced chat value is invalid.", "daily_report_not_found": "Daily report option not found.", "weekly_report_not_found": "Weekly report option not found.", "monthly_report_not_found": "Monthly report option not found.", "export_right_not_found": "You are not entitled to a backup for this channel. Try again on {{date}}", "at_least_one_ads_required": "You must add at least one ad.", "ads_required": "You must select an ad.", "duplicate_ads": "You cannot add the same ad more than once.", "at_least_one_reply_method_required": "Please select at least one reply option.", "thinker_flow_id_required": "You must select a Thinker flow.", "helobot_knowledge_base_id_required": "You must select a Helobot knowledge base.", "message_text_required": "You must enter a message.", "message_cannot_exceed_1000_characters": "Message cannot exceed 1000 characters. If emojis or special characters are used, the count may be higher."}, "ask_form_questions": {"field_key_not_found": "Field Key Not Found."}, "live_chat": {"ext_id_not_found": "Ext id is not blank", "message_not_found": "Please write a message", "name_required": "Please write your name", "mail_required": "Please write your e-mail.", "phone_required": "Please write your phone number.", "user_not_found": "User not found."}, "cart": {"cannot_add_this_currency_code": "A Product with a Different Currency Cannot be Added to the Cart"}, "google": {"calendar": {"not_found_calendar_id": "Calendar ID is incorrect", "not_found_summary": "Title cannot be left blank.", "not_found_description": "Description cannot be left blank", "not_found_end_time": "Incorrect end date"}, "drive_account": {"allready_exist": "Account is already active on this channel", "not_found": "First, Connect Your Google Drive Account"}}, "tags": {"tag_not_found": "Tag not found", "already_exists_tag": "Tag already exists", "already_exists_color": "Tag Color already exists", "tag_id_not_found": "Tag id not found", "ids_not_found": "Ids not found", "max_label": "You can create a maximum of 250 tags"}, "instagram": {"channel_id_invalid": "Channel ID invalid", "status_invalid": "Status invalid", "message_invalid": "Question-answer content cannot be left blank", "persistent_menu_invalid": "Persistent <PERSON><PERSON> invalid", "call_to_actions_invalid": "Call to actions invalid", "messages_not_found": "Message invalid", "default_messages_not_found": "Default messages invalid", "instagram": "Call to actions invalid", "ice_braker_status": "Record a Welcome Message First", "ref_not_found": "Ref undefined", "ref_id_not_found": "Ref ID invalid", "ref_registered": "Ref registered", "instagram_channel_invalid": "Channel type invalid."}, "message": {"temp_id": "temp_id not found", "pagination_invalid": "Pagination information is invalid", "perpage_invalid": "perpage value should be between 20 - 100", "page_invalid": "page value can be at least 0", "text_invalid": "Text not found", "agent_message_not_found": "There has never been a conversation before.", "not_found": "Message not found.", "retry_done": "Message Can No Longer Be Sent. Resend Attempt Over", "channel_type_mismatch": "Message forwarding can only be done between same channel types", "agent_only_view": "Cha<PERSON> exists but agent has not responded."}, "team": {"agent_id_not_found": "Agent id is not found", "channel_id_not_found": "Channel id is not found", "team_id_not_found": "Team id is not found", "channels_not_found": "Channels not found", "agents_not_found": "Agents not found", "team_not_found": "Team is not found", "status_not_found": "Status is not found", "team_name_not_found": "Team name is not found", "channel_not_authorization": "You are not authorized to the channel.", "name_already_exists": "The registered team with this name already exists.", "team_status_not_found": "Team has no status information", "team_setting_status_inactive": "Team setting status inactive", "chat_on_team": "The chat is on the {{team_name}} team."}, "trendyol": {"account_available": "Account available", "account_not_found": "Account not found", "text_not_found": "Message not found", "question_id_not_found": "question id not found", "name_not_found": "Name not found", "supplier_id_not_found": "Supplier ID not found", "username_not_found": "Username information not found", "password_not_found": "password information not found"}, "hepsiburada": {"account_available": "Account available", "account_not_found": "Account not found", "text_not_found": "Message not found", "question_not_found": "question id not found", "name_not_found": "Name not found", "supplier_id_not_found": "Supplier ID not found", "username_not_found": "Username information not found", "password_not_found": "password information not found", "merchant_id_not_found": "Store ID not found", "error_auth": "Store ID or password is incorrect. Please check your information"}, "helobot": {"account_available": "Account available", "account_not_found": "Account not found", "question_not_found": "Question not found", "name_not_found": "Name not found", "file_urls_not_found": "File not found", "kb_id_invalid": "Knowledge base information is invalid", "kb_not_found": "Knowledge base not found", "conversation_id_not_found": "Conversation id is not found", "timeout": "timeout information was entered incorrectly or invalidly", "allready_started": "This conversation is now being transferred to Helobot"}, "media": {"not_found_media_name": "Media Name Not Found"}, "location": {"id": "ID Not Found", "name": "Name Not Found", "title": "title Not Found", "address": "Address Not Found", "longitude": "Longitude Not Found", "latitude": "Latitude Not Found", "selected_location": "Selected Location Not Found", "name_already_exists": "A location with this name already exists."}, "overtimes": {"status": "Invalid Working Status", "OVERTIME_ON": "Start Overtime", "OVERTIME_OFF": "End Overtime", "BREAK_TIME_ON": "Start Break", "BREAK_TIME_OFF": "End Break", "overtime_error": "Please start working first or finish your break."}, "pazarama": {"account_available": "Account available", "account_not_found": "Account not found", "text_not_found": "Message not found", "client_id_not_found": "client_id information not found", "client_secret_not_found": "client_secret information not found"}, "n11": {"api_key_not_found": "api_key information not found", "api_password_not_found": "api_password information not found"}, "abandoned_cart": {"abandoned_cart_id_not_found": "Abandoned cart ID not found", "not_found": "Abandoned cart record not found"}, "service_account": {"cannot_delete": "This service account cannot be deleted."}, "market_quick_reply": {"text_not_found": "Text information not found", "type_not_found": "Type information not found or incorrect"}}, "mail": {"issue_invalid": "Message type invalid (issue)", "header_invalid": "Header invalid", "body_invalid": "Body invalid", "agent_id_invalid": "agent_id invalid", "success_message": "Your request has been received. We thank you.", "error_message": "An error occurred, the request was not delivered.", "not_verified": "You must first confirm your email address."}, "channel": {"upload_channel_picture": "Upload Channel Picture", "change_channel_name": "Change Channel Name", "leave_active_conversation_time": "Drop Time From Active Conversations (Minute)", "send_to_archive_time": "Send Cha<PERSON> to Archive", "send_to_archive_time_status": "If Customer Doesn't Reply, Send Cha<PERSON> to Archive", "send_to_archive_time_timeout": "Time to Archive Chat (Minutes)", "notification_sound_status": "Notification Sound Status", "welcome_message": "Welcome Message Settings", "send_welcome_message": "Send welcome message", "is_default_send_welcome_message": "Send default welcome message", "welcome_message_item_title": "Welcome message", "whatsapp_about": "Whatsapp About", "send_welcome_message_actions": "Welcome Message Assignment", "quick_replies": "Quick Reply Settings", "hide_phone_number": "Keep client phone number hidden for agent", "team_message": "Team Message Settings", "unarchived": "Team message is forwarded after the chat reaches the pending from the archive", "after_order": "Delivered after the order is placed (2 hours later)", "before_order": "If the order is not made, send it (after 24 hours)", "snipped_text": "Snipped Text", "unarchive_message": "Unarchiving Message", "send_message_when_unarchived": "Send Message When Cha<PERSON> Unarchived", "is_default_send_archive_message": "Send the default message after unarchive", "send_unarchive_message_actions": "Send default message when chat is unarchived", "unarchive_message_item_title": "Unarchiving message ", "timeout_for_archived_chat": "Chat Waiting Time in Archive to Send a Message", "send_archive_message_actions": "Assigning Messages When Archiving a Chat", "is_default_send_archiving_message": "Send default message", "archive_message_item_title": "Message to send to archive", "send_message_while_chat_is_archived": "Send Message While Chat Is Archived"}, "integration": {"send_cart_message": {"item": "[BR][/BR]{{emoji}}[B]{{title}} {{variant_message}}[/B][BR][/BR][B]Quantity :[/B] {{count}} {{stock_unit}}[BR][/BR][B]Price:[/B] {{price}}[BR][/BR][B]Amount:[/B] {{amount}}", "sub_total": "[B]Total :[/B][SPACE]23[/SPACE]{{total}}", "coupon_total": "[BR][/BR][B]Discount Coupon :[/B][SPACE]2[/SPACE]{{total}}", "campaign_total": "[B]Discount Campaign :[/B][SPACE]2[/SPACE]{{total}}", "general_total": "[BR][/BR][B]Total Sum:[/B] {{total}}", "approve_message": "If you are approving your cart, type [B]1[/B], please."}, "send_history_message": {"item": "[BR][/BR]{{emoji}} [B]{{title}} ({{variant_message}})[/B][BR][/BR][B]Quantity :[/B][SPACE]17[/SPACE]{{count}} {{stock_unit}}[BR][/BR][B]Price :[/B][SPACE]20[/SPACE]{{price}}[BR][/BR]", "order_code": "[B]Order code:[/B] {{order_code}}[BR][/BR]", "status": "[B]Order status:[/B] {{status}}[BR][/BR]", "cargo": "[B]Cargo:[/B] {{cargo}}[BR][/BR]", "cargo_tracking_code": "[B]Cargo Tracking Code:[/B] {{cargo_tracking_code}}[BR][/BR]", "cargo_tracking_url": "[B]Cargo Tracking Link:[/B] {{cargo_tracking_url}}[BR][/BR]", "date": "[B]Date:[/B] {{date}}[BR][/BR]", "total_price": "[B]Amount:[/B] {{total_price}}[BR][/BR]", "order_url": "You can check your order status here.[BR][/BR]{{url}}"}, "send_heloscope_order_message": "Your order has been successfully created.", "send_product_share_messageV2": "[BR][/BR][B]{{product_title}}[/B][BR][/BR]{{description}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Bu ürünü sepete eklemek istiyorsanız [B]{{product_share_counter}}[/B] yazın.", "send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Price :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR]Type [B]{{product_share_counter}}[/B] if you want to add this product to cart.", "send_product_share_message_no_action": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Price :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}", "whatsapp_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Price :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}[BR][/BR][BR][/BR].", "livechat_send_product_share_message": "[BR][/BR][B]{{product_title}}[/B][BR][/BR][B]Price :[/B]{{product_price}}{{product_variant}}[BR][/BR][BR][/BR]{{base_url}}", "order_summary_message": "Dear [B]{{username}}[/B][BR][/BR]Items in your cart:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Cargo Information :[/B][BR][/BR]Cargo Company :[SPACE]10[/SPACE]{{cargo_option_name}}[BR][/BR]Cargo Price :[SPACE]20[/SPACE]{{cargo_option_fee}}[BR][/BR][BR][/BR][B]Overall Total :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Invoice Address:[/B][BR][/BR][B]Address :[/B]{{invoice_address}}[BR][/BR][B]District:[/B]{{invoice_district}}[BR][/BR][B]City:[/B]{{invoice_city}}[BR][/BR][BR][/BR][B]Delivery Address:[/B][BR][/BR][B]Address :[/B]{{delivery_address}}[BR][/BR][B]District:[/B]{{delivery_district}}[BR][/BR][B]City:[/B]{{delivery_city}}[BR][/BR][BR][/BR][B]Payment Type :[/B]{{payment_type}}[BR][/BR][BR][/BR]If you are confirming your order with the information above, please send the message [B]1[/B].", "shopify_order_summary_message": "Dear [B]{{username}}[/B][BR][/BR]Items in your cart:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][B]Tax :[/B]{{tax}}[BR][/BR][B]Overall Total :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Adress:[/B]{{address}}[BR][/BR]If you are confirming your order with the information above, please send the message [B]1[/B].", "shopify_whatsapp_order_summary_message": "Dear [B]{{username}}[/B][BR][/BR]Items in your cart:[BR][/BR][BR][/BR]{{cart_content}}[BR][/BR][BR][/BR][B]Tax :[/B]{{tax}}[BR][/BR][BR][/BR][B]Shipping Fee :[/B] {{cargo_option_fee}}[BR][/BR][BR][/BR]{{discounts}}[B]Grand Total :[/B][SPACE]5[/SPACE]{{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Address:[/B]{{address}}[BR][/BR]", "whatsapp_order_summary_message": "Dear [B]{{username}}[/B] Items in your cart:[BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Cargo Information:[/B][BR][/BR]Cargo Company: {{cargo_option_name}}[BR][/BR]Cargo Price: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Overall Total:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Invoice Address[/B][BR][/BR][B]Address:[/B] {{invoice_address}}[BR][/BR][B]District:[/B] {{invoice_district}} [BR][/BR][B]City :[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Delivery Address:[/B][BR][/BR][B]Address :[/B] {{delivery_address}}[BR][/BR][B]District:[/B] {{delivery_district}}[BR][/BR][B]City:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Payment Type:[/B] {{payment_type}}[BR][/BR][BR][/BR]If you confirm your order with the information above, please click the button below.", "whatsapp_order_summary_message_cart_content": "Dear [B]{{username}}[/B] Items in your cart: {{cart_content}}[BR][/BR]{{remittance_discount}}{{additional_cost}}[B]Cargo Information:[/B][BR][/BR]Cargo Company: {{cargo_option_name}}[BR][/BR]Cargo Price: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Overall Total:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Invoice Address:[/B][BR][/BR]{{invoice_address}}[BR][/BR]", "whatsapp_order_summary_message_invoice": "[B]District:[/B] {{invoice_district}}[BR][/BR][B]City:[/B] {{invoice_city}}[BR][/BR]", "whatsapp_order_summary_message_delivery_address": "[BR][/BR][B]Delivery Address: [/B][BR][/BR]{{delivery_address}}[BR][/BR]", "whatsapp_order_summary_message_delivery": "[B]District:[/B] {{delivery_district}}[BR][/BR][B]City:[/B] {{delivery_city}}[BR][/BR]", "whatsapp_order_summary_message_approve": "[BR][/BR][B]Payment Type:[/B] {{payment_type}}[BR][/BR][BR][/BR]If you confirm your order with the information above, please click the button below.", "whatsapp_order_summary_message_partition": "Dear [B]{{username}}[/B] Items in your cart:[BR][/BR]{{cart_content}}[BR][/BR]{{remittance_discount}}", "whatsapp_order_summary_message_partition_2": "{{additional_cost}}[B]Cargo Information:[/B][BR][/BR]Cargo Company: {{cargo_option_name}}[BR][/BR]Cargo Price: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Overall Total:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Invoice Address[/B][BR][/BR][B]Address:[/B] {{invoice_address}}[BR][/BR][B]District:[/B] {{invoice_district}} [BR][/BR][B]City :[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Delivery Address:[/B][BR][/BR][B]Address :[/B] {{delivery_address}}[BR][/BR][B]District:[/B] {{delivery_district}}[BR][/BR][B]City:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Payment Type:[/B] {{payment_type}}[BR][/BR][BR][/BR]If you confirm your order with the information above, please click the button below.", "order_summary_message_partition_2": "{{additional_cost}}[B]Cargo Information:[/B][BR][/BR]Cargo Company: {{cargo_option_name}}[BR][/BR]Cargo Price: {{cargo_option_fee}}[BR][/BR][BR][/BR][B]Overall Total:[/B] {{total_amount}}[BR][/BR]{{general_order_note}}[BR][/BR][B]Invoice Address[/B][BR][/BR][B]Address:[/B] {{invoice_address}}[BR][/BR][B]District:[/B] {{invoice_district}} [BR][/BR][B]City :[/B] {{invoice_city}}[BR][/BR][BR][/BR][B]Delivery Address:[/B][BR][/BR][B]Address :[/B] {{delivery_address}}[BR][/BR][B]District:[/B] {{delivery_district}}[BR][/BR][B]City:[/B] {{delivery_city}}[BR][/BR][BR][/BR][B]Payment Type:[/B] {{payment_type}}[BR][/BR][BR][/BR]If you confirm your order with the information above, please write 1.", "send_customer_password_created": "Your membership has been created, your temporary [B]password is {{password}}[/B], you can change your password if you want.✅", "send_customer_created": "Your membership has been successfully created.✅", "send_order_status_message": "Dear [B]{{customer_name}}[/B] [BR][/BR][B]Order Code :[/B]{{order_code}}[BR][/BR][B]Order Date :[/B]{{order_date}}[BR][/BR][B]Order Amount :[/B]{{order_total_price}}[BR][/BR][B]Cargo Company :[/B]{{cargo_option_name}}[BR][/BR][B]Cargo Tracking No :[/B]{{cargo_tracking_code}}[BR][/BR][B]Order Status :[/B]{{order_status}}[BR][/BR]{{cargo_tarcking_url}}", "remittance_message": "[BR][/BR][B]IBAN[/B]: {{iban}}", "send_cargo_message": "Your shipping options are listed below. Write the number next to the one you want to choose.[BR][/BR][BR][/BR]{{cargo_options}}", "whatsapp_send_cargo_message": "Your shipping options are listed below. Please choose one", "send_address_message_caption": "{{emoji}} [B]Address :[/B] {{address}}[BR][/BR][B]City :[/B] {{city}}[BR][/BR][B]District :[/B] {{town}}", "send_address_message_caption_address": "[BR][/BR]{{emoji}}[B]Address: [/B] {{address}}", "send_address_message_caption_city": "[BR][/BR][B]City: [/B] {{city}}[BR][/BR][B]District: [/B] {{town}}", "shopify_send_address_message_caption": "{{emoji}}[B]Address :[/B] {{address}}[BR][/BR][B]Country :[/B] {{country}}[BR][/BR][B]City :[/B] {{city}}[BR][/BR][B]District :[/B] {{province}}", "general_order_note": "[BR][/BR][B]Order Note:[/B] {{general_order_note}}[BR][/BR]", "first_message": "Hello, your request has been received. We will deal with you as soon as possible.🛒", "discount_coupone_message": "[BR][/BR][BR][/BR][B]Discount Price:[/B] {{discount_price}}[BR][/BR][BR][/BR]", "send_customer_confirmation_message": "Your membership information is listed below.[BR][/BR]{{customer_data}}{{gdpr_url}}{{terms_of_use_url}}[BR][/BR]If you approve the membership information and want to become our member, we ask you to write [B]approve.[/B]", "send_payment_url": "In order to complete your shopping, we kindly ask you to complete your payment from the link below.[BR][/BR]{{payment_url}}", "yes": "Yes", "no": "No", "cart_approve_message": "If you are approving your cart, type [B]1[/B], please.", "coupon_message": "[B]Discount Coupon :[/B][SPACE]13[/SPACE]{{price_coupon}}[BR][/BR]", "send_out_of_hours_message": "Hello there! Welcome to Helorobo. We have received your message and will get back to you within 24 hours. Thank you for getting in touch with us.", "instagram_private_reply": "Thanks for reaching out to us, how can I help you?", "cash": "Cash", "credit_card": "Credit Card", "turkish_out_of_working_hours_message": "Out of hours message (Turkish)", "english_out_of_working_hours_message": "Out of hours message (English)", "order_not_found_message": "There is no order available to check the order status.", "confirm_select_address": "Hello, if you are certifying your address below, please type [B]{{index}}[/B].[BR][/BR]", "send_empty_address_message": "There is no address to choose from. Please provide your address information.", "edit": {"kvkk": "KVKK Agreement", "membership_registration_agreement": "Membership Registration Agreement", "customer_required": "Require customer creation approval", "precision": "The number of digits after the comma", "show_brand_name": "Show the brand in the product list", "show_product_new": "Show new information in product list", "eft_options": "Transfer Eft options", "door_payment_options": "Payment at Delivery Settings", "order_status": "Order Statuses", "ready_cargo": "Shipping Ready", "out_of_hours_send_message": "Send a message outside of working hours", "gave_to_cargo": "Delivered to Cargo", "success_payment": "Payment completed", "out_of_hours_default_send_message": "Send default message outside of working hours", "out_of_hours_message": "Out of hours message.", "out_of_hours_message_settings": "Out of Hours Message Settings", "out_of_hours": "Working hours", "show_is_new": "Product is new", "show_brand": "Show Brand", "perpage": "How Many Products to Send at the Same Time in Product Sharing", "order_created_message": "Order created Message", "send_order_created_message": "Send order created message", "is_default_order_created_message": "Send default order created message?", "manuel_discount_permission": "Manual Discount Opportunity", "all": "All", "just_company_owner": "Company Owner Only", "default_currency_code": "Default Currency Code", "warn_unregister_purchase": "Send a Confirmation Message for Unable to Pay by Credit Card for Customers Without Membership", "send_product_price_without_vat": "When Sharing a Product, Send the Price Excluding VAT"}, "duplicate_member": "Mr. member, you do not have more than one membership on our site, which of the following information would you like to continue with? [BR][/BR]{{duplicate_member}}", "duplicate_member_item": "{{emoji}} Email: {{email}} [BR][/BR] Phone: {{phone_number}}", "tax": "Tax", "kdv": "VAT", "arvia": {"created_room": "You can start talking to us via the link below for video or audio conversation.\n{{url}}"}, "only_one_discount": "Only 1 Discount Coupon Can Be Applied", "credit_cart": "Credit Card/Debit Card", "discounts": "Discounts"}, "auth": {"forgot_password": {"form": {"title": "Parolanızı Sıfırlayın", "subtitle": "E-Posta adresini gir", "email": "<PERSON><PERSON><PERSON>", "enter_page": "<PERSON><PERSON><PERSON>"}, "accept": {"title": "<PERSON><PERSON>", "subtitle": "Şifre sıfırlama bağlantısını gönderdik , e-postanızı kontrol edin."}, "mail_template": {"subject_dear": "<PERSON><PERSON><PERSON>", "subject_description": "HeloRobo Web Parola Sıfırlama Maili", "title": "HeloRobo", "subtitle": "<PERSON><PERSON><PERSON>", "description": "Sıfırlama Bağlantısı", "sub_description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>re s<PERSON>ırl<PERSON> bağlantınız mevcut aşağıdan buton'a tıklayarak şifrenizi değiştirebilirsiniz.", "enter_set_new_password": "Şifre <PERSON>ırla"}, "password_change_request": "Helorobo Password Change Request", "password_change_link": "We have received a request to reset the password for your account. Please use the link below to set a new password:", "warning_1": "If you did not make this request, please disregard this email. Your account will remain secure.", "warning_2": "The password reset link is only valid for 24 hours. You need to use the link within this period to reset your password.", "warning_3": "If you encounter any issues, please do not hesitate to contact our support team."}, "helorobo_bilgi_teknolojileri": "© Helorobo Bilgi Teknolojileri A.Ş"}, "success": {"integration": {"add_to_cart": "{{item_name}} has been added to your cart.", "empty_cart": "All items in your cart have been deleted.", "dec_item_from_cart": "has been decreased from your cart.", "inc_item_from_cart": "has been increased from your cart.", "item_removed_from_cart": "{{item_name}} has been removed from your cart.", "order_message": "Your order was successful[BR][/BR]Order Number:[B]{{orderId}}[/B][BR][/BR]Dear[B]{{username}}[/B],[BR][/BR]we have received your order of {{orderTotalPrice}}.[BR][/BR]It is expected to be delivered within 2-4 days.If you send us the [B]“Order Status”[/B] message to learn the status of your order, our robot will inform you about the process.", "order_message_manuel": "Your order was successful    Order Number:{{orderId}}    Dear{{username}},   we have received your order of {{orderTotalPrice}}.   It is expected to be delivered within 2-4 days.If you send us the 'Order Status' message to learn the status of your order, our robot will inform you about the process.", "order_message_credit_cart": "Your order was successful.[BR][/BR]Order Number:[B]{{orderId}}[/B][BR][/BR]Dear [B]{{username}}[/B],[BR][/BR]we have received your order of {{orderTotalPrice}}.[BR][/BR] In order to complete your order, please complete your order via the link in the incoming message. If you send us the [B]“Order Status”[/B] message via to learn the status of your order later, our robot will inform you about the process.", "shopify_order_credit_cart_message": "Your order has been successfully created. You can continue the payment process from the link below.[BR][/BR]{{url}}", "shopify_order_message": "Your order has been successfully created. You can access the order details from the link below.[BR][/BR]{{url}}", "customer_address_message": " Hello, your addresses are given in the order below. Which address would you like to choose as the delivery address ?[BR][/BR]", "livechat_customer_address_message": " Hello, your addresses are given in the order below. Which address would you like to choose as the delivery address ?", "customer_only_address_message": "Hello, your address is given below. Do you confirm this address?[BR][/BR]", "customer_invoice_address_message": "Hello, your addresses are given below in order. Which address would you like to choose as billing address?[BR][/BR]", "livechat_customer_invoice_address_message": "Hello, your addresses are given below in order. Which address would you like to choose as billing address?", "payment_type": "Hello, your payment options are listed below. Which payment option would you like to use? Please indicate as the [B]number[/B].[BR][/BR][BR][/BR]{{payment_options}}", "whatsapp_payment_type": "Hello, your payment options are given in order in the options. Which payment option would you like to use? Please choose one.", "whatsapp_payment_type_continue": "More of your payment options are given here. Which payment option would you like to use? Please choose one.", "add_to_cart_children": "Product options are listed below. Write the number next to which one you want to choose.[BR][/BR]{{children_title}}[BR][/BR][BR][/BR]{{children}}[BR][/BR]", "whatsapp_add_to_cart_children": "Available Options: {{children_title}}[BR][/BR][BR][/BR]select of the options.", "livechat_add_to_cart_children": "Available Options: {{children_title}}[BR][/BR]select of the options.", "add_to_cart_variant": "Product options are listed below. Write the number next to which one you want to choose.[BR][/BR]{{variant_title}}[BR][/BR][BR][/BR]{{variant}}[BR][/BR]", "whatsapp_add_to_cart_variant": "Available Options: {{variant_title}}[BR][/BR][BR][/BR]select of the options.", "livechat_add_to_cart_variant": "Available Options: {{variant_title}}[BR][/BR]select of the options.", "gdpr_url_message": "[BR][/BR]I read the gdpr agreement, I accept it. ({{gdpr_url}})[BR][/BR]", "terms_of_use_url_message": "[BR][/BR]I have read the Member Registration Agreement, I accept it.({{terms_of_use_url}})[BR][/BR]", "has_been_changed_of_product_count": "The number of the {{product_name}} item in your basket has been changed to {{number}}", "arvia_chat_message": "You can contact us via the link below for a video or audio call.\n{{url}}"}, "ask_form_questions": {"first_name": "What's your name ?", "last_name": "What's your lastname ?", "identity_number": "What is your identity number?", "birth_date": "What is your birth date ?", "email": "Can you write your e-mail address ?", "title": "What is your address title ? (home, work, etc.)", "fullname": "What's your fullname ?", "address": "Can you write your address ?", "post_code": "Can I have your zip code ?", "phone_number": "Can I have your phone number ?", "password": "Define your password please ?", "district_name": "What is your district’s name?", "district_code": "What is your district?", "address_type": "Can you write address header?", "name": "What's your name ?", "company_name": "What's your company name?", "tax_office": "Can you share tax office?", "tax_number": "Can you share tax number?", "zip": "What is your zip code?", "province": "Can you write province?", "other_phone": "Can you share other phone number?", "town_code": "Can you write town code?", "is_same_delivery_and_invoce_address": "Your delivery address has been successfully confirmed. If you want to choose this address as the billing address, press [B]1[/B] if you do not want it, press [B]2[/B].", "whatsapp_is_same_delivery_and_invoce_address": "Your shipping address has been successfully confirmed. Do you want to select this address as the billing address?", "gender": "Can you write gender?", "invoice_type": "What is your invoice type?", "country_name": "What is your country’s name?", "city_name": "What is your city’s name?", "town_name": "What is your town’s name?", "become_member": "Since you are shopping without a membership, you will not be able to pay with a credit card. If you still want to continue, type [B]Yes[/B].", "whatsapp_become_member": "Since you are shopping without a membership, you will not be able to pay with a credit card. If you still want to continue, please confirm", "get_next_page_for_products": "Type [B]0[/B] to see other products.", "whatsapp_get_next_page_for_products": "Click to see other products.", "birth_month": "what is your birth month ?", "birth_year": "What is your birth year ?", "birth_day1": "what is your birthday ?", "mail_notify": "Would you like to receive mail notification ?", "kvkk": "Do you accept the membership agreement ?"}}, "campaign": {"send_campaign": "Hello, your campaign options are given below in order. Which campaign option would you like to use? Please specify [B]number[/B]. [BR][/BR][BR][/BR]{{campaign_items}}", "whatsapp_send_campaign": "Hello, your campaign options are given below in order. Which campaign option would you like to use?"}, "support": {"solved": "Solved", "pending": "Pending", "unsolved": "Unsolved", "in_process": "In process", "normal": "Normal", "danger": "<PERSON><PERSON>"}, "customer_evaluation": {"evaluation_message": "Were you satisfied with our service? You can help us serve you better by rating your experience.", "evaluation_message_5": "Excellent 😍", "evaluation_message_4": "Good 😊", "evaluation_message_3": "Nice 🙂", "evaluation_message_2": "Bad 😑", "evaluation_message_1": "Horrible 😤", "evaluation_message_text": "Would you like to rate your shopping experience?[BR][/BR][BR][/BR]{{rows}} ", "evaluation": "Rate it", "customer_evaluation_setting": "Customer Evaluation Setting", "customer_evaluation_timeout": "Customer Evaluation Setting Time After Order (Minutes)", "evaluation_type_error": "Evaluation type is invalid.", "evaluation_options": "Shipping Status Selection", "evaluation_option_1": "After the sale is completed", "evaluation_option_2": "After the client is archived"}, "agent_report": {"not_found": "Report not found", "date_invalid": "Date invalid"}, "thinker": {"now": "Initiate Thinker by Sending a Message", "wait": "Initiate Thinker after Receiving a Customer Message", "start_type": "Thinker Start Type", "flow_id": "Thinker Flow Name", "status": "Thinker Status", "thinker": "Thinker"}, "chat_action": {"action_not_found": "Action not found", "system": "system", "customer": "customer", "moved_to_public": "The chat was put on hold by {{name}}", "system_moved_to_public": "The chat has been put on hold by the system", "archived_by_system": "Chat archived by the system", "chat_hide": "Chat archived by {{name}}", "system_chat_hide": "Chat archived by {{name}}", "block": "Chat blocked by {{name}}", "unblock": "Chat unblocked by {{name}}", "system_block": "Chat blocked by {{name}}", "assign_to_team": "<PERSON><PERSON> assigned to {{team}} by {{name}}", "assign_to_agent": "Chat assigned to operator {{assigned_user}} by {{name}}", "add_chat_tag": "{{tag}} tag added to contact by {{name}}", "delete_chat_tag": "{{tag}} by person {{name}} has been deleted", "update_user_profile": "The person's profile information has been updated by {{name}}", "started_flow": "<PERSON><PERSON> transferred to <PERSON><PERSON> by {{name}}", "stopped_flow": "Thinker stream of chat stopped by {{name}}", "received_chat": "<PERSON><PERSON> was taken by {{name}}", "owner_forced_chat": "<PERSON><PERSON> was forcefully taken by {{name}}", "customer_assign_to_team": "<PERSON><PERSON> forwarded to the team by {{name}}", "user_profile_customer_note": "The customer's note information was updated from {{old_customer_note}} to {{new_customer_note}} by {{name}}", "user_profile_email": "The customer's email information from {{old_email}} was updated to {{new_email}} by {{name}}", "user_profile_first_name": "The customer's name information was updated from {{old_first_name}} to {{new_first_name}} by {{name}}", "user_profile_last_name": "The customer's surname information was updated from {{old_last_name}} to {{new_last_name}} by {{name}}", "user_profile_phone_number": "The customer's phone information from {{old_phone_number}} was updated to {{new_phone_number}} by {{name}}", "add_new_address": "Address titled {{address_title}} added by {{name}}", "edit_address": "{{address_title}} address updated by {{name}}", "delete_address": "Address {{address_title}} was deleted by {{name}}", "selected_address": "'{{address_title}}' address was chosen by {{name}}", "selected_cargo": "'{{cargo_title}}' cargo was chosen by {{name}}", "selected_payment": "'{{payment_title}}' payment method was chosen by {{name}}", "deleted_agent_pending_list": "The chat was transferred to the pending list because the {{deleted_agent_name}} operator was deleted by {{name}}", "deleted_agent_archived_list": "The chat was transferred to the archived list because the {{deleted_agent_name}} operator was deleted by {{name}}", "noc_moved_to_public": "<PERSON><PERSON> added to pending list", "campaign_code_applied": "{{coupone_code}} discount code applied by {{name}", "campaign_activated": "{{campaign_name}} campaign implemented by {{name}}", "campaign_remove": "'{{campaign_name}}' campaign was removed by {{name}}", "thinker_bot_stopped": "<PERSON><PERSON> stopped by <PERSON><PERSON>", "pinned": "Chat pinned by {{name}}", "remove_archive": "The chat was removed from the archive by {{name}}", "mark_as_unread": "Chat marked as unread by {{name}}", "mark_as_read": " Chat marked as read by {{name}}", "started_helobot": "Transferred chat to Helobot by {{name}}", "stopped_helobot": "Removed from chat Helo<PERSON> by {{name}}", "system_stopped_helobot": "Helobot has been stopped by the system", "system_stopped_helobot_error": "Helobot was stopped by the system due to '{{error}}' error occurring in Helobot.", "connected_user": "Customer Connected", "disconnected_user": "Customer Connection Terminated.", "website_action": "Customer is currently on -> '{{website}}' Page", "helobot_timeout": "Helobot stopped due to timeout", "thinker_flow_timeout": "Thinker flow timed out.", "timeout_flow_start": "Thinker flow ended and timeout flow started."}, "translate": {"text_invalid": "Text is invalid", "target_language": "The language to be converted to is invalid"}, "message": {"customer_not_replied_message": "Your session has been terminated because we have not received a response from you for a long time.", "archived_message_text": "How can we help you?"}, "user_log": {"edit_user": "{{agent_name}} has edited the user {{user_name}}.", "new_user": "{{name}} user has been added. (Email: {{email}}, Type: {{type}})", "delete_user": "{{name}} user has been deleted.", "create_company": "{{agent_name}} has created a company named {{name}} with phone number {{phone_number}}.", "status_change_company": "{{agent_name}} has changed the status of the company {{name}} to {{status}}.", "edit_company": "{{agent_name}} has edited the details of the company {{company_name}}.", "create_channel": "{{agent_name}} has created the channel with ID {{ext_id}}.", "delete_channel": "{{agent_name}} has deleted the channel named {{name}}.", "edit_channel": "{{agent_name}} has edited the details of the channel {{channel_name}}.", "archived_channel_chats": "{{agent_name}} has archived the chats of the channel {{channel_name}}.", "archived_channel_chats_with_date": "{{agent_name}} has archived the chats of the channel {{channel_name}} between {{start_date}} and {{end_date}}.", "remove_token_for_facebook": "{{agent_name}} has removed the access token for the channel {{channel_name}}.", "remove_credit_line": "{{agent_name}} has removed the payment method for the channel {{channel_name}}.", "add_credit_line": "{{agent_name}} has added a payment method to the channel {{channel_name}}.", "add_integration": "{{agent_name}} has added an integration named {{integration_name}} with type {{type}}.", "edit_integration": "{{agent_name}} has edited the integration named {{integration_name}}.", "login": "{{agent_name}} has logged in to the system.", "logout": "{{agent_name}} has logged out of the system.", "message_export": "{{agent_name}} has started exporting messages from the channel {{channel_name}} between {{start_date}} and {{end_date}}.", "channel_status": "{{agent_name}} has changed the status of the channel {{channel_name}} to {{status}}.", "profile_edit": "{{agent_name}} has updated their profile information.", "add_tag": "{{agent_name}} has added a tag named {{tag_name}}.", "edit_tag": "{{agent_name}} has edited the tag named {{tag_name}}.", "thinker_register": "{{agent_name}} has activated <PERSON><PERSON>.", "helobot_register": "{{agent_name}} has activated He<PERSON><PERSON>.", "mail_register": "{{agent_name}} has activated the Mail System.", "message_template_register": "{{agent_name}} has activated the Message Template.", "hepsiburada_register": "{{agent_name}} has activated Hepsiburada.", "trendyol_register": "{{agent_name}} has activated Trendyol.", "pazarama_register": "{{agent_name}} has activated <PERSON><PERSON><PERSON>.", "n11_register": "{{agent_name}} has activated N11."}}, "Worker": {"run": {"job_not_found": "Job Not Found.", "job_data_not_found": "Job data's is not found.", "job_type_not_implemented": "Job type not implemented.", "channel_not_implemented": "Channel not implemented."}}, "Noc": {"errors": {"channel": {"id_not_found": "Channel id not found", "not_found": "Channel not found", "missing": "Channel Information Missing", "provider_not_found": "Provider information not found", "type_not_found": "Type information not found", "name_not_found": "Name information not found", "provider_not_support": "Currently, this channel provider is not supported..", "type_not_support": "This channel type is currently not supported.", "allready_exist": "Channel is already assigned", "access_token_not_found": "access_token not found", "ext_id_not_found": "ext_id not foun", "page_id_not_found": "Page id not found", "is_active": "Select Active or Passive", "limit_invalid": "Please enter a valid limit value.", "unlimit_invalid": "Unlimit information is invalid.", "no_right_add_channels": "You do not have the right to add new channels.", "credit_line_exists": "Credit line already exists"}, "container": {"code": "Container Code Not Found", "subdomain": "Subdomain Not Found", "not_login": "Could not login to the container.", "not_found_certificate": "Certificate Not Found.", "method": "Code Send Method Not Found.", "invaid_method": "Invalid Confirmation Submission Method.", "exist": "This container already exists", "invalid_phone": "Please Do Not Use Special Characters", "not_found_phone": "Please send phone number", "id_not_found": "Container id not found", "not_found": "Container not found", "missing": "Container Information Missing", "cant_get_container": "Containers could not be retrieved"}, "company": {"id_not_found": "Company id not found", "not_found": "Company not found", "missing": "Company Information Missing", "number_allready_using": "Company Name or Phone Number is already in use. Please choose another number or name.", "name_not_found": "Company Name is required", "phone_not_found": "Phone Number is Required", "limit_number_small_count_agent": "The entered limit number is less than the current number of agents."}, "integration": {"login": "Failed to Login to the Specified System", "baseurl": "Please enter the Client's BaseURL. sample: https://example.com", "username": "Enter Web Service Username", "password": "Enter Web Service User Password", "not_found": "Integration not found", "missing": "Integration Information Missing", "service_missing": "Integration Web Service Information Missing", "baseurl_not_found": "Url not found", "type_not_found": "Tip not found", "link_error": "Please delete the '/' at the end of the link", "settings_key_error": "<PERSON><PERSON>r Occurred <PERSON> in Key", "login_key": "Login key is incorrect"}, "auth": {"login_not_found": "User not found", "no_noc_user": "User is not NOC user", "active": "User is not active", "bcrypt_error": "User password not found", "no_permission": "User authorization not found", "allready_email": "This Email Already Exists"}, "company_log": {"setup_id": "No id Information Found", "not_found": "No Records Found", "allready_exist": "Actions Already Taken for This Registration"}, "user": {"not_found": "User not found", "missing": "User Information Missing", "email_regex": "In<PERSON>id Email Address", "username_not_found": "Username Not Found", "password_not_found": "Password Not Found", "email_exist": "This Email is already in user", "email_not_found": "<PERSON><PERSON> not found"}, "embedded_signup": {"short_live_token_not_found": "token is not found", "data_not_found": "data not found", "business_management_not_found": "Business Management id not found. Check Permissions", "whatsapp_business_management_not_found": "Whatsapp Business Management id not found. Check Permissions", "not_found_whatsapp_numbers": "The User's Number Registered on Whatsapp Business Could Not Be Found."}, "report": {"period_not_found": "Please Select a Period"}, "mobile": {"id_not_found": "Version ID not found.", "changelog_invalid": "Changelog invalid.", "version_invalid": "Version invalid.", "force_upgrade_invalid": "Force upgrade invalid."}, "version": {"name_invalid": "Name field is invalid", "text_invalid": "Text field is invalid", "id_invalid": "Id field is invalid", "version_not_found": "Version not found"}, "ads_medias": {"not_found": "Ad information not found", "id_not_found": "Invalid ID", "name_not_found": "Invalid name", "url_not_found": "Invalid URL", "redirect_url_not_found": "Invalid redirect URL", "is_active_not_found": "Invalid is_active status", "new_index_not_found": "Invalid new_index"}, "dashboard": {"start_date_invalid": "The start date is invalid.", "end_date_invalid": "The end date is invalid.", "date_range": "The date range can be a minimum of 1 day and a maximum of 30 days.", "sort_invalid": "Sort value is invalid", "sort_field_invalid": "sort field invalid", "sort_value_invalid": "sort value invalid"}, "message": {"contents_not_found": "Message content not found"}}, "onboarding_wizards": {"long_lived_token": "Long Live Token", "connected_instagram_account": "Connected Instagram Account", "name": "Name", "username": "Username", "profile_picture_url": "Profile Picture Url", "display_phone_number": "Display Phone Number", "wp_status": "Whatsapp Status", "customer_business_name": "Customer Business Name", "customer_business_link": "Customer Business Link", "certificate": "Certificate"}}, "UnNameUser": "Anonymous Customer", "Onboarding": {"errors": {"conversation_count": "Monthly chat count is invalid.", "package_price_not_found": "Package price count is invalid.", "name_not_found": "Please Enter Name.", "phone_not_found": "Please Enter a Valid Phone Number.", "invalid_email": "Please Enter a <PERSON><PERSON> Email Address.", "code_not_found": "Code Information is Required.", "onboarding_wizard_id_not_found": "id Required.", "embeddedInfo_not_found": "No Onboarding Record Found.", "onboardingWizard_not_found": "No Onboard Record Found.", "invalid_privacy_policy": "Privacy Policy Selection Required", "invalid_pdpa": "KVKK Selection is Mandatory", "newsletter": "E-Newsletter Selection Required", "email_valid": "This email already exists", "service_type_not_found": "Service Type Information Required.", "integration_type_not_found": "No Integration Information Found.", "package_id_not_found": "Package id not Found", "features_ids_not_found": "Property Information Not Found", "package_not_found": "Package not found", "id_not_found": "id not Found", "access_token_not_found": "access_token not Found", "instagram_account_not_exist": "Your Instagram Account Not Found. Please select at least one instagram page and facebook page or check the permissions needed.", "facebook_account_does_not_exist": "Your Facebook Account Not Found. Please select at least one facebook page or check the permissions needed.", "wrong_instagram_account_selected": "You tried to connect different instagram account. Please select the {{name}} instagram account or contact with the support.", "token_invalid_or_missing_permission": "<PERSON><PERSON> is invalid or not enough permission given.", "facebook_error_message": "Facebook Error Message: {{message}}.", "missing_scopes": "Missing Scopes: {{scopes}}.", "missing_granular_scopes": "Missing Granular Scopes: {{granularScopes}}.", "company_name_not_found": "Company Name Not Found", "tax_office_not_found": "Tax Office Not Found", "tax_or_identity_number_not_found": "Tax Number Not Found", "pdpa_not_found": "Accept the KVKK Agreement", "select_package": "Please Select Package", "not_get_facebook_token": "Failed to get Facebook token", "not_get_facebook_fields": "Your information could not be obtained from Facebook.", "info_is_not_yet": "Insufficient Information. Please Complete the Previous Steps", "container_problem": "Container cannot create", "onboarding_wizard_type": "Choose Confirmation Code Sending Method", "onboarding_wizard_code": "No Confirmation Code", "phone_number_valid": "This Phone Number Is Already Used", "whatsapp_number_not_found": "No Whatsapp Number", "unauthorized_action": "Unauthorized Action", "allready_exists_company": "This Company Is Already Registered", "please_try_again": "Please Try Again Later", "invalid_token_error": "Message could not be sent. Try Reconnecting to Your {{page_name}} Page. {{page_name}} Could not be connected to your page.", "session_not_found": "session not found", "self_onboard_not_found": "self onboarding is not done", "package_already_exists": "Active Package Already Exists."}, "info": {"whatsapp_process": "Your installation is in progress. You will be contacted within 2-4 business days.", "login_data": "Hello, your registration has been successfully received by HeloRobo. You can login with the following information.<br/><br/>Email: {{email}}<br/>Password: {{password}}", "whatsapp_email_header": "HeloRobo Information", "login_header": "Information Required for HeloR<PERSON><PERSON>"}}, "Package": {"starter": "Starter", "instagram": "Instagram", "tariff": "Monthly", "control_panel": "<PERSON><PERSON><PERSON><PERSON> (Management panel)", "waba": "WhatsApp Business API", "faba": "Facebook Messenger User", "facebook_eshop": "Facebook eShops", "instagram_eshop": "Instagram eShops", "message": "1.000 Text Messages", "template_message": "Template Message", "shop_integration": "eCommerce Integration", "message_package": "Additional messaging packages", "heloscope": "Heloscope"}, "Advermind": {"token_not_found": "Please Login", "accesstoken_not_found": "Advermind Access Token not found", "campaign_id_not_found": "Campaign ID Information Not Found", "campaign_status_not_found": "Campaign Status Information Not Found", "not_found_facebook_page": "In order to do this, your Facebook Page must be registered by HeloRobo."}, "Mobile": {"not_found_version": "Version not Found"}, "Api": {"webhook": {"url_invalid": "Url is invalid", "hash_invalid": "Hash is invalid", "content_caption_invalid": "Content caption invalid", "context_url_invalid": "Content url invalid", "type_invalid": "Type invalid", "chat_id_invalid": "Chat id invalid", "channel_id_invalid": "Channel id invalid"}}, "Thinker": {"errors": {"thinker_not_found": "Thinker record not found", "name_invalid": "Name is invalid", "message_url_id_invalid": "Message url id is invalid", "flow_id_invalid": "Flow id is invalid", "start_type_invalid": "Start type is invalid", "url_invalid": "Url is invalid", "hash_invalid": "Hash is invalid", "id_invalid": "id is invalid", "allready_started": "This Client Is Currently Already in a Stream", "not_found": "No Active Streams Found for This Customer", "channel_not_found": "Please Select Channel"}, "stopped": "Flow Stopped"}, "MessageTemplate": {"errors": {"message_template_not_found": "Message Template record not found", "company_admin_required": "Please Create Company Owner First. Register Later"}}, "Reseller": {"errors": {"hash_invalid": "hash bilgisi information invalid", "timestamp_invalid": "timestamp information invalid", "reseller_key_invalid": "reseller_key information invalid", "company_name_invalid": "company_name information invalid", "company_phone_invalid": "company_phone information invalid", "allready_exists_company": "This Company Is Already Registered"}}}