const enums = require('./../libs/enums')
const helpers = require('./../libs/helpers')

const moment = require('moment')
const Message = require('../models/Message')
const User = require('../models/User')

const __getInteractiveMessageType = (message, emojiData) => {

  if (!message.vContent.sub_type) {
    return {
      type: enums.app_message_types.INTERACTIVE,
      content: {
        mid: message.vContent.mid,
        interactive: message.vContent.interactive,
        ...emojiData
      }
    }
  }

  if (message.vContent.thinker) {
    switch (message.vContent.sub_type) {
      case enums.message_types.BUTTON:

        if (message.vContent.header.type === 'image') {
          return {
            type: enums.app_message_types.INTERACTIVE,
            content: {
              mid: message.vContent.mid,
              caption: message.vContentBbCode ?
                helpers.getHtmlBbCodeParser().parseString(message.vContentCaption) :
                message.vContentCaption,
              url: message.vContent.header.image.link,
              hide_image: message.vContentHideImage,
              sub_type: message.vContent.sub_type,
              buttons: message.vContent.buttons,
              ...emojiData
            }
          }
        }

        return {
          type: enums.app_message_types.INTERACTIVE,
          content: {
            mid: message.vContent.mid,
            caption: message.vContentBbCode ?
              helpers.getHtmlBbCodeParser().parseString(message.vContentCaption || message.vContentText) :
              message.vContentCaption || message.vContentText,
            hide_image: message.vContentHideImage,
            sub_type: message.vContent.sub_type,
            buttons: message.vContent.buttons,
            ...emojiData
          }
        }

      case enums.message_types.LIST:
        return {
          type: enums.app_message_types.INTERACTIVE,
          content: {
            mid: message.vContent.mid,
            caption: message.vContentBbCode ?
              helpers.getHtmlBbCodeParser().parseString(message.vContentText) :
              message.vContentText,
            sub_type: message.vContent.sub_type,
            list: message.vContent.buttons,
            ...emojiData
          }
        }
    }
  }

  switch (message.vContent.sub_type) {

    case enums.message_types.BUTTON:

      if (message.vContent.header.type === 'image') {
        return {
          type: enums.app_message_types.INTERACTIVE,
          content: {
            mid: message.vContent.mid,
            caption: message.vContentBbCode ?
              helpers.getHtmlBbCodeParser().parseString(message.vContentCaption) :
              message.vContentCaption,
            url: message.vContent.header.image.link,
            hide_image: message.vContentHideImage,
            sub_type: message.vContent.sub_type,
            buttons: message.vContent.buttons
          }
        }
      }

      return {
        type: enums.app_message_types.INTERACTIVE,
        content: {
          mid: message.vContent.mid,
          caption: message.vContentBbCode ?
            helpers.getHtmlBbCodeParser().parseString(message.vContentCaption || message.vContentText) :
            message.vContentCaption || message.vContentText,
          hide_image: message.vContentHideImage,
          sub_type: message.vContent.sub_type,
          buttons: message.vContent.buttons
        }
      }

    case enums.message_types.LIST:
      return {
        type: enums.app_message_types.INTERACTIVE,
        content: {
          mid: message.vContent.mid,
          caption: message.vContentBbCode ?
            helpers.getHtmlBbCodeParser().parseString(message.vContentText) :
            message.vContentText,
          sub_type: message.vContent.sub_type,
          list: message.vContent.buttons
        }
      }

    default:
      throw new Error('Interacitve type bilgisi handle edilemedi: ' + message.vContent.sub_type)
  }

}

const __getReplyMessageType = async (message, emojiData) => {

  /**
   * Bu kısımda mesajların gerçek type bilgisi bulunmaktadır. bu gerçek bilgiye göre mesaj typeları tekrar gezilir.
   *   */
  if (message.vContent.type) {

    const messageContentWhatsapp = await getMessageItem(message, message.vContent.type)

    return {
      type: enums.app_message_types.REPLY_MESSAGE,
      content: {
        ...messageContentWhatsapp.content,
        reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
        type: messageContentWhatsapp.type,
      }
    }

  }

  return {
    type: enums.app_message_types.REPLY_MESSAGE,
    content: {
      mid: message.vContent.mid,
      text: message.vContentText,
      reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
      type: enums.message_types.TEXT,
      ...emojiData
    }
  }

}

async function getMessageItem(message, contentType = undefined) {

  let originalType = message.type

  if (contentType) {
    originalType = contentType
  }

  let emojiData = {}
  if (message.vContent.emoji || message.vContent.agent_emoji) {
    emojiData = {
      reaction: message.vContent.reaction,
      emoji: message.vContent.emoji,
      agent_emoji: message.vContent.agent_emoji
    }

    if (message.vContent.agent_emoji && message.vContent.reacted_agent_id) {
      const user = await User.findById(message.vContent.reacted_agent_id)

      emojiData.reacted_agent = {
        id: user.id,
        name: helpers.getName(user)
      }
    }

  }

  message.vContent = message.vContent || message.content || {}

  switch (originalType) {

    // GLOBAL

    case enums.message_types.TEXT:

      if (message.vContent.text) {

        return {
          type: enums.app_message_types.TEXT,
          content: {
            mid: message.vContent.mid,
            text: message.vContent.bb_code ?
              helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
              message.vContent.text,
            commands: message.vContent.commands,
            ...emojiData
          }
        }

      }

      // VERSION 0.0.1 - content'e doğrudan mesajın içeriği yazılıyordu.
      return {
        type: enums.app_message_types.TEXT,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.content) :
            message.content,
          commands: message.vContent.commands,
          ...emojiData
        }
      }

    case enums.message_types.IMAGE_URL:

      return {
        type: enums.app_message_types.IMAGE_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          url: message.content.url,
          hide_image: message.vContent.hide_image || false,
          ...emojiData
        }
      }

    case enums.message_types.FILE_URL:

      return {
        type: enums.app_message_types.FILE_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.bb_code ?   // caption sadece thinker için var.
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          filename: message.vContent.filename,
          url: message.content.url,
          hide_image: false,
          ...emojiData
        }
      }

    case enums.message_types.VIDEO_URL:

      return {
        type: enums.app_message_types.VIDEO_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          url: message.content.url,
          hide_image: false,
          ...emojiData
        }
      }

    case enums.message_types.AUDIO_URL:

      return {
        type: enums.app_message_types.AUDIO_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          url: message.content.url,
          hide_image: false,
          ...emojiData
        }
      }

    case enums.message_types.LIVECHAT_BUTTON:

      return {
        type: enums.app_message_types.LIVECHAT_BUTTON,
        content: {
          filename: message.content.filename,
          url: message.content.url,
          buttons: message.content.buttons,
          sub_type: message.content.sub_type,
          text: message.content.text, // ya text vardır yada caption
          caption: message.content.caption,
          ...emojiData
        }
      }

    case enums.message_types.LIVECHAT_REPLY_TO_MESSAGE:

      return {
        type: enums.app_message_types.LIVECHAT_REPLY_MESSAGE,
        content: {
          button_reply: message.vContent.button_reply, // { id: 'asda', text: '', url?: '' }
          reply_to: message.vContent.reply_to,
          text: message.vContent.text,
          ...emojiData
        }
      }

    case enums.message_types.LIVECHAT_LOCATION:

      return {
        type: enums.app_message_types.LOCATION_URL,
        content: {
          url: `https://maps.google.com/maps?q=${message.vContent.latitude},${message.vContent.longitude}&z=15&output=embed`,
          latitude: message.vContent.latitude,
          longitude: message.vContent.longitude,
          name: message.vContent.name,
          address: message.vContent.address,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_TEMPLATEV2:

      return {
        type: enums.app_message_types.TEMPLATE_MESSAGEV2,
        content: message.content
      }

    case enums.message_types.WHATSAPP_TEMPLATE:

      // Eğer template mesaja cevap verildi ise burası tetiklenmeli
      if (message.content.button) {
        return {
          type: enums.app_message_types.REPLY_TEMPLATE_MESSAGE,
          content: {
            type: message.content.type,
            button: message.content.button, // { text: 'asdsa' }
            ...emojiData
          }
        }
      }

      return {
        type: enums.app_message_types.TEMPLATE_MESSAGE,
        content: {
          type: message.content.type,
          header: message.content.header,
          body: message.content.body,
          footer: message.content.footer,
          buttons: message.content.buttons,
          ...emojiData
        }
      }


    case enums.message_types.INFOBIP_IMAGE_URL:
      return {
        type: enums.app_message_types.IMAGE_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.caption) :
            message.vContent.caption,
          url: message.content.url,
          hide_image: message.vContent.hide_image,
          ...emojiData
        }
      }

    // FACEBOOK

    case enums.message_types.FACEBOOK_IMAGE_URL:

      // @deprecated

      return {
        type: enums.app_message_types.IMAGE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: [message.vContent.url],
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_VIDEO_URL:

      // @deprecated

      return {
        type: enums.app_message_types.VIDEO_URLS,
        content: {
          mid: message.vContent.mid,
          urls: [message.vContent.url],
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_AUDIO_URL:

      // @deprecated
      return {
        type: enums.app_message_types.AUDIO_URL,
        content: {
          mid: message.vContent.mid,
          url: message.vContent.url,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_AUDIO_URLS:

      return {
        type: enums.app_message_types.AUDIO_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_STICKER_URL:

      // @deprecated

      return {
        type: enums.app_message_types.IMAGE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: [message.vContent.url],
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_FILE_URL:

      // @deprecated

      return {
        type: enums.app_message_types.FILE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: [message.vContent.url],
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_IMAGE_URLS:

      return {
        type: enums.app_message_types.IMAGE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_VIDEO_URLS:

      return {
        type: enums.app_message_types.VIDEO_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_FILE_URLS:

      return {
        type: enums.app_message_types.FILE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.items,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_LOCATION:

      return {
        type: enums.app_message_types.LOCATION_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.title,
          url: `https://maps.google.com/maps?q=${message.vContentLatitude},${message.vContentLongitude}&z=15&output=embed`,
          latitude: message.vContentLatitude,
          longitude: message.vContentLongitude,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_FALLBACK:

      return {
        type: enums.app_message_types.TEXT,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.title + ': ' + message.vContent.content + ', url: ' + message.vContent.url,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_FALLBACK_URLS:

      return {
        type: enums.app_message_types.TEXT,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          items: message.vContent.items,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_REPLY_TO_MESSAGE:

      return {
        type: enums.app_message_types.REPLY_MESSAGE,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.bb_code ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
            message.vContent.text,
          type: enums.message_types.TEXT,
          reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
          ...emojiData
        },
      }

    case enums.message_types.FACEBOOK_QUICK_REPLY:

      // @todo payload kısmı ne yapılabilir, düşünülecek!

      return {
        type: enums.app_message_types.TEXT,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          commands: message.vContent.commands,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_GENERIC_BUTTON:
      return {
        type: enums.app_message_types.GENERIC_BUTTON,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.subtitle ? helpers.getHtmlBbCodeParser().parseString(message.vContent.subtitle) : message.vContent.subtitle,
          header: message.vContent.title,
          url: message.vContent.image_url,
          buttons: message.vContent.buttons,
          reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
          ...emojiData
        }
      }

    case enums.message_types.FACEBOOK_PRODUCT_TEMPLATE:
      return {
        type: enums.app_message_types.FACEBOOK_PRODUCT_TEMPLATE,
        content: message.content
      }

    case enums.message_types.FACEBOOK_MEDIA_REPLY:
      return {
        type: enums.app_message_types.MEDIA_REPLY,
        content: {
          mid: message.vContent.mid,
          post_data: message.vContent.post_data,
          comment: message.vContent.comment,
          text: message.vContent.text,
          ...emojiData
        }
      }

    // WHATSAPP

    case enums.message_types.WHATSAPP_IMAGE_URL:

      return {
        type: enums.app_message_types.IMAGE_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.caption,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_STICKER_URL:

      return {
        type: enums.app_message_types.STICKER_URL,
        content: {
          mid: message.vContent.mid,
          animated: message.vContent.animated || false,
          metadata: message.vContent.metadata,
          mime_type: message.vContent.mime_type,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_VIDEO_URL:

      return {
        type: enums.app_message_types.VIDEO_URL,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.caption,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_VOICE_URL:

      return {
        type: enums.app_message_types.VOICE_URL,
        content: {
          mid: message.vContent.mid,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_DOCUMENT_URL:

      return {
        type: enums.app_message_types.FILE_URL,
        content: {
          mid: message.vContent.mid,
          filename: message.vContent.filename || message.vContent.file_name,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_AUDIO_URL:

      return {
        type: enums.app_message_types.AUDIO_URL,
        content: {
          mid: message.vContent.mid,
          url: message.vContent.url || process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_CONTACTS:

      return {
        type: enums.app_message_types.CONTACTS,
        content: {
          mid: message.vContent.mid,
          contacts: message.vContent.contacts,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_LOCATION:

      return {
        type: enums.app_message_types.LOCATION_URL,
        content: {
          mid: message.vContent.mid,
          url: `https://maps.google.com/maps?q=${message.vContent.latitude},${message.vContent.longitude}&z=15&output=embed`,
          latitude: message.vContent.latitude,
          longitude: message.vContent.longitude,
          name: message.vContent.name,
          address: message.vContent.address,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_LOCATION_REQUEST:

      return {
        type: enums.app_message_types.WHATSAPP_LOCATION_REQUEST,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          ...emojiData
        }
      }

    case enums.message_types.WHATSAPP_INTERACTIVE:

      return __getInteractiveMessageType(message, emojiData)

    case enums.message_types.WHATSAPP_REPLY_TO_MESSAGE:

      return __getReplyMessageType(message, emojiData)

    case enums.message_types.WHATSAPP_REFERRAL:

      const data = {
        type: enums.app_message_types.REFERRAL,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          context: message.vContent.context,
          referral: {
            body: message.vContent.referral.body,
            headline: message.vContent.referral.headline,
            source_type: message.vContent.referral.source_type,
            source_url: message.vContent.referral.source_url,
            ctwa_clid: message.vContent.referral.ctwa_clid,
            media_type: message.vContent.referral.media_type,
            video_url: message.vContent.referral.video_url,
            thumbnail_url: message.vContent.referral.thumbnail_url,
          },
          ...emojiData
        }
      }

      const isMedia = helpers.getReferralMedia(message.vContent.referral)
      if (isMedia) {
        data.content.referral[isMedia.type] = {
          ...isMedia.data,
          url: process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash
        }
      }

      return data

    // INSTAGRAM

    case enums.message_types.INSTAGRAM_VIDEO_URLS:

      return {
        type: enums.app_message_types.VIDEO_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_AUDIO_URLS:

      return {
        type: enums.app_message_types.AUDIO_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_FILE_URLS:

      return {
        type: enums.app_message_types.FILE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_IMAGE_URLS:

      return {
        type: enums.app_message_types.IMAGE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_SHARE_URLS:

      return {
        type: enums.app_message_types.SHARE_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_STORY_MENTION_URLS:

      return {
        type: enums.app_message_types.STORY_MENTION_URLS,
        content: {
          mid: message.vContent.mid,
          urls: message.vContent.urls,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_REPLY_TO_STORY:

      let mediaType = enums.message_types.VIDEO_URL
      if (message.content.story.media_type?.includes('IMAGE')) {
        mediaType = enums.message_types.IMAGE_URL
      }

      message.content.story.media_type = mediaType

      return {
        type: enums.app_message_types.INSTAGRAM_REPLY_TO_STORY,
        content: {
          mid: message.content.mid,
          text: message.content.text,
          story: {
            ...message.content.story,
            media_url: message.content.story.media_url || ''
          },
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_REPLY_TO_MESSAGE:

      return {
        type: enums.app_message_types.REPLY_MESSAGE,
        content: {
          mid: message.vContent.mid,
          text: message.vContentBbCode ?
            helpers.getHtmlBbCodeParser().parseString(message.vContent.text) :
            message.vContent.text,
          type: enums.message_types.TEXT,
          reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
          ...emojiData
        },
      }

    case enums.message_types.INSTAGRAM_QUICK_REPLY:

      return {
        type: enums.app_message_types.QUICK_REPLY,
        content: {
          mid: message.vContent.mid,
          quick_replies: message.vContent.quick_replies,
          text: message.vContent.text,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_QUICK_REPLY_ANSWER:

      return {
        type: enums.app_message_types.QUICK_REPLY_ANSWER,
        content: {
          mid: message.vContent.mid,
          quick_reply: message.vContent.quick_reply,
          text: message.vContent.text,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_GENERIC_BUTTON:
      return {
        type: enums.app_message_types.GENERIC_BUTTON,
        content: {
          mid: message.vContent.mid,
          caption: message.vContent.subtitle ? helpers.getHtmlBbCodeParser().parseString(message.vContent.subtitle) : message.vContent.subtitle,
          header: message.vContent.title,
          url: message.vContent.image_url,
          buttons: message.vContent.buttons,
          reply_to: typeof message.vContent.reply_to !== 'string' ? '' : message.vContent.reply_to,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_MEDIA_REPLY:
      return {
        type: enums.app_message_types.MEDIA_REPLY,
        content: {
          mid: message.vContent.mid,
          post_data: message.vContent.post_data,
          comment: message.vContent.comment,
          text: message.vContent.text,
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_REELS:
      return {
        type: enums.app_message_types.REELS,
        content: {
          mid: message.vContent.mid,
          attachments: message.vContent.attachments, // [{type: "ig_reel", payload: {reel_video_id: "", title: "", url: ""}}]
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_STORY:
      return {
        type: enums.app_message_types.STORY,
        content: {
          mid: message.vContent.mid,
          attachments: message.vContent.attachments, // [{type: "ig_story", payload: {story_media_id: "", story_media_url: ""}}]
          ...emojiData
        }
      }

    case enums.message_types.INSTAGRAM_PRODUCT_MESSAGE:
      return {
        type: enums.app_message_types.INSTAGRAM_PRODUCT_MESSAGE,
        content: message.content
      }

    case enums.message_types.UNSUPPORTED_MESSAGE:

      return {
        type: enums.app_message_types.UNSUPPORTED_MESSAGE,
        content: {
          mid: message.vContent.mid,
          is_unsupported: message.vContent.is_unsupported,
          ...emojiData
        }
      }

    case enums.message_types.ADS_OPEN_THREAD:

      return {
        type: enums.app_message_types.ADS_OPEN_THREAD,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          referral: message.vContent.referral,
          type: message.vContent.type,
          ...emojiData
        }
      }

    case enums.message_types.OPEN_THREAD:

      return {
        type: enums.app_message_types.ADS_OPEN_THREAD,
        content: {
          mid: message.vContent.mid,
          text: message.vContent.text,
          referral: message.vContent.referral,
          type: message.vContent.type,
          ...emojiData
        }
      }

    case enums.message_types.ONLY_ADS_OPEN_THREAD:

      return {
        type: enums.app_message_types.ONLY_ADS_OPEN_THREAD,
        content: {
          referral: message.vContent.referral,
          type: message.vContent.type,
          mid: message.vContent.mid,
          ...emojiData
        }
      }

    // VERSION 0.0.1

    case 'URL':

      let text = ''

      if (message.content.title && message.content.title.length > 0) {
        text += message.content.title + ': '
      }

      text += process.env.BASE_URL + '/message-document/' + message._id.toString() + '/' + message.hash

      return {
        type: enums.app_message_types.TEXT,
        content: {
          mid: message.vContent.mid,
          text: text,
          ...emojiData
        }
      }

  }

  throw new Error('type bilgisi handle edilemedi: ' + message.type)

}

const DashPresenter = {

  getMessageItemV4: (message, item, chatOrAgent) => {
    let name = chatOrAgent.title
    if (message.from_type === enums.message_from_types.AGENT || message.from_type === enums.message_from_types.SYSTEM) {
      name = helpers.getName(chatOrAgent)
    }

    if (message.from_type === enums.message_from_types.CUSTOMER) {
      name = helpers.getName(message.conversation_id)
    }

    return {
      id: message.id,
      type: item.type,
      from_type: message.from_type,
      content: item.content,
      status: message.status || '',
      user: {
        id: message.from_type === enums.message_from_types.CUSTOMER ? message.conversation_id?._id?.toString() || message.conversation_id?.toString() : chatOrAgent.id,
        name: name
      },
      mid: message.ext_id,
      date: moment(message.created_at).unix(),
      date_ms: moment(message.created_at).valueOf(),
      deleted: !!message.deleted_at,
      reply_message: null,
      star: message.star,
      conversation_id: message.conversation_id?._id?.toString() || message.conversation_id?.toString(),
      retry: message.retry,
      imported: message.import || false,
      error_message: message.error_message || null,
      platform: message.platform || null
    }
  },

  getMessageItemCustomerOrAgent: async (message, agent = undefined) => {
    const item = await getMessageItem(message)

    if (message.from_type === enums.message_from_types.CUSTOMER) {
      return DashPresenter.getMessageItemV4(message, item, message.conversation)
    } else {
      // agent ve system
      return DashPresenter.getMessageItemV4(message, item, agent || message.user)
    }
  },

  getRepliedMessage: async (message, agent = undefined) => {

    let repliedMessageFormatted = null
    if (message.content.reply_to) {
      const replyMessage = await Message.findOne({ ext_id: message.content.reply_to }).populate('conversation_id').populate('user_id')
      if (replyMessage) {
        repliedMessageFormatted = await DashPresenter.getMessageItemCustomerOrAgent(replyMessage)
      }
    }

    const data = await DashPresenter.getMessageItemCustomerOrAgent(message, agent)
    data.reply_message = repliedMessageFormatted

    return data
  },

  getChatItem: (chat, lastMessage, channel, chatIntegration, thinkerStatus = false, helobotStatus = false) => {

    let is_active = false

    let subtitle = ''
    let createdAt = chat.updated_at

    if (lastMessage != null) {

      subtitle = lastMessage.vSubtitle || lastMessage?.content?.text || lastMessage?.[0]?.content?.text || lastMessage?.content?.interactive?.list_reply?.title

      if (lastMessage.vContentBbCode) {

        switch (lastMessage.type) {

          case enums.message_types.TEXT:
            subtitle = helpers.getBbCodeProviderParser(lastMessage.vSubtitle)
            break

          case enums.message_types.IMAGE_URL:
            subtitle = helpers.getBbCodeProviderParser(lastMessage.vContentCaption)
            break

        }

      }

      createdAt = lastMessage.created_at
    }

    if (chat.owner_user_id || chat.is_active) {
      is_active = true
    }

    let title = chat.title
    if (chat.first_name) {
      title = chat.first_name

      if (chat.last_name) {
        title += ' ' + chat.last_name
      }
    }

    let teamData = null
    if (typeof chat.team?.id === 'string') {
      teamData = {
        id: chat.team.id,
        name: chat.team.name
      }
    } else if (chat.team_id) {
      teamData = {
        id: chat.team_id.toString(),
        name: ''
      }
    }

    return {
      id: chat.id || chat._id,
      title: title || '',
      ext_id: chat.ext_id,
      username: chat.username || '',
      subtitle: subtitle || '',
      profile_image_url: chat.profile_pic || '',
      unread_message_count: chat.unread_message_count || 0,
      sort_field: moment(chat.sort_field).unix(),
      is_active: is_active,
      chat_referral: chat.chat_referral_id ? {
        id: chat.chat_referral_id.toString()
      } : null,
      stage: chatIntegration ? chatIntegration.vData.getOrderStage() : '',
      channel_id: channel.id,
      channel_type: channel.type,
      channel_name: channel.name,
      channel_ext_id: channel.ext_id || channel.id,
      is_paired: false,
      is_pinned: chat.pinned_at ? true : false,
      is_blocked: chat.is_blocked || false,
      date: moment(createdAt).unix(), // v23232
      date_ms: moment(createdAt).valueOf(),
      last_message_created_at: moment(createdAt).unix(), // depracated
      thinker_active: thinkerStatus || false,
      helobot_active: helobotStatus || false,
      customer_last_message_time: chat.customer_last_message_time ? moment(chat.customer_last_message_time).valueOf() : null,
      tags: [],
      team: teamData
    }

  },

  getMessageWithReply: async (messages) => {
    const formattedMessages = []
    for (const item of messages) {
      let messageData = await DashPresenter.getMessageItemCustomerOrAgent(item)

      if (item.content.reply_to) {
        const isMessage = messages.find(a => a.ext_id === item.content.reply_to)
        if (isMessage) {
          messageData.reply_message = await DashPresenter.getMessageItemCustomerOrAgent(isMessage)
        } else {
          const replyMessage = await Message.findOne({ ext_id: item.content.reply_to }).populate('user_id').populate('conversation_id')
          if (replyMessage) {
            messageData.reply_message = await DashPresenter.getMessageItemCustomerOrAgent(replyMessage)
          }
        }
      }
      formattedMessages.push(messageData)
    }
    return formattedMessages
  },

}

module.exports = DashPresenter
