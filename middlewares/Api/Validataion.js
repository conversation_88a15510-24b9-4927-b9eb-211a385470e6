const Joi = require("joi")
const { Error } = require("mongoose");

const Validation = {

  CompanyWebhook: async (req, res, next) => {
    try {
      const schema = Joi.object({
        url: Joi.string().required().error(new Error(req.t('Api.webhook.url_invalid'))),
        hash: Joi.string().required().error(new Error(req.t('Api.webhook.hash_invalid')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (e) {
      next(e)
    }
  },

  SendMessage: async (req, res, next) => {
    try {
      const schema = Joi.object({
        context: {
          caption: Joi.string().required().error(new Error(req.t("Api.message.content_caption_invalid"))),
          url: Joi.string().optional().error(new Error(req.t("Api.message.context_url_invalid"))),
        },
        type: Joi.string().valid('TEXT', 'IMAGE_URL', 'VIDEO_URL', 'AUDIO_URL', 'FILE_URL').error(new Error(req.t("Api.message.type_invalid"))),
        chat_id: Joi.string().required().error(new Error(req.t("Api.message.chat_id_invalid"))),
        channel_id: Joi.string().required().error(new Error(req.t("Api.message.channel_id_invalid")))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (e) {
      next(e)
    }
  },

  Chats: async (req, res, next) => {
    try {
      const schema = Joi.object({
        filter: Joi.object({
          title: Joi.string().allow(''),
          phone_number: Joi.string().allow(''),
          start_date: Joi.string().allow(''),
          end_date: Joi.string().allow(''),
          channel_ids: Joi.array().items(Joi.string()).optional(),
        }).allow({}),
        pagination: Joi.object({
          page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
          page_size: Joi.number().min(20).max(100).required().error(new Error(req.t('Global.errors.pagination_not_found'))),
          sort: Joi.number().valid(1, -1).optional().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid')))
        })
      })

      await schema.validateAsync(req.body)
      next()

    } catch (e) {
      next(e)
    }
  },

  Messages: async (req, res, next) => {
    try {
      const schema = Joi.object({
        filter: Joi.object({
          chat_id: Joi.string().optional().error(new Error(req.t('App.errors.integration.chat_id_cannot_be_empty'))),
          start_date: Joi.string().allow(''),
          end_date: Joi.string().allow(''),
          channel_ids: Joi.array().items(Joi.string()).optional(),
        }).allow({}),
        pagination: Joi.object({
          page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
          page_size: Joi.number().min(20).max(100).required().error(new Error(req.t('Global.errors.pagination_not_found'))),
          sort: Joi.number().valid(1, -1).optional().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid')))
        })
      })

      await schema.validateAsync(req.body)
      next()

    } catch (e) {
      next(e)
    }
  }
}

module.exports = Validation
