const Joi = require("joi")

const enums = require('../../libs/enums')

const Validation = {

  CheckList: async (req, res, next) => {
    try {
      const schema = Joi.object({
        search: Joi.string().allow(''),
        sort: {
          field: Joi.string().valid('created_at', 'name').required().error(new Error(req.t('Noc.errors.dashboard.sort_field_invalid'))),
          value: Joi.number().valid(1, -1).required().error(new Error(req.t('Noc.errors.dashboard.sort_value_invalid')))
        },
        is_active: Joi.boolean().optional(),
        is_demo: Joi.boolean().optional(),
        pagination: {
          page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
          page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found')))
        }
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CompanyCreate: async (req, res, next) => {
    try {
      const schema = Joi.object({
        name: Joi.string().required().error(new Error(req.t('Onboarding.errors.company_name_not_found'))),
        phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
        billtekrom_account_id: Joi.number().optional().optional(),
        package: Joi.object({
          price: Joi.number().optional(),
          monthly_conversation_limit: Joi.number().optional(),
          agent_settings: Joi.object({
            limit: Joi.object({
              limit: Joi.string().allow("").optional(),
              unlimit: Joi.boolean().required()
            }).required()
          }).required(),
          channel_settings: Joi.object({
            limit: Joi.object({
              whatsapp_channel: Joi.number().optional(),
              facebook_channel: Joi.number().optional(),
              instagram_channel: Joi.number().optional(),
              telegram_channel: Joi.number().optional(),
              livechat_channel: Joi.number().optional()
            }).optional()
          }).optional()
        }).required()
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CompanyEditPost: async (req, res, next) => {
    try {
      const schema = Joi.object({
        name: Joi.string().required().error(new Error(req.t('Onboarding.errors.company_name_not_found'))),
        phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
        billtekrom_account_id: Joi.number().optional(),
        profit_multiplier: Joi.number().optional(),
        is_demo: Joi.boolean().optional(),
        package: Joi.object({
          // monthly_conversation_limit: Joi.number().required().error(new Error(req.t('Onboarding.errors.conversation_count'))),
          monthly_conversation_limit: Joi.number().optional(),
          price: Joi.number().optional(),
          agent_settings: Joi.object({
            limit: Joi.object({
              limit: Joi.number().optional(),
              unlimit: Joi.boolean().required()
            }).required()
          }).required(),
          channel_settings: Joi.object({
            limit: Joi.object({
              whatsapp_channel: Joi.number().optional(),
              facebook_channel: Joi.number().optional(),
              instagram_channel: Joi.number().optional(),
              telegram_channel: Joi.number().optional(),
              livechat_channel: Joi.number().optional()
            }).optional()
          }).optional()
        }).required()
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelListAll: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelList: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })

      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  NewChannelPost: async (req, res, next) => {
    try {
      // const schema = Joi.object({
      //   integration_id: Joi.string().allow('').error(new Error(req.t('App.errors.auth.integration.integration_id_cannot_be_empty'))),
      //   container_id: Joi.string().allow('').error(new Error(req.t('Noc.errors.container.id_not_found'))),
      //   type: Joi.string().required().error(new Error(req.t('Noc.errors.channel.type_not_found'))),
      //   provider: Joi.string().required().error(new Error(req.t('Noc.errors.channel.provider_not_found'))),
      //   name: Joi.string().required().error(new Error(req.t('Onboarding.errors.company_name_not_found'))),
      //   widgetSize: Joi.string().allow('')
      // })

      // await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelEditPost: async (req, res, next) => {
    try {
      const schema = Joi.object({
        name: Joi.string().required().error(new Error(req.t('Noc.errors.channel.name_not_found'))),
        is_active: Joi.boolean().required().error(new Error(req.t('Noc.errors.channel.is_active'))),
        has_template_report: Joi.boolean().optional(),
        access_token: Joi.string().optional(),
        integration_id: Joi.string().optional(),
        whitelists: Joi.array().items(Joi.string()).optional()
      })
      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelCompleted: async (req, res, next) => {
    try {
      const schema = Joi.object({
        access_token: Joi.string().required().error(new Error(req.t('Noc.errors.channel.access_token_not_found'))),
        page_id: Joi.string().required().error(new Error(req.t('Noc.errors.channel.page_id_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelChatArchived: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('Noc.errors.channel.not_found'))),
        start_date: Joi.string().optional().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        end_date: Joi.string().optional().error(new Error(req.t('Noc.errors.dashboard.end_date_invalid'))),
      })
      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChannelUnCompleted: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })

      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  IntegrationListAll: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  IntegrationList: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  IntegrationEditPost: async (req, res, next) => {
    try {
      const validationFields = {
        TSOFT: {
          base_url: Joi.string().required().error(new Error(req.t('Noc.errors.integration.baseurl_not_found'))),
          username: Joi.string().required().error(new Error(req.t('Noc.errors.user.username_not_found'))),
          password: Joi.string().allow(''),
          version: Joi.string().allow('')
        },
        SHOPIFY: {
          store_name: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.store_name_not_found'))),
          store_front_key: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.store_front_key_not_found'))),
          admin_access_token: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.admin_access_token_not_found'))),
          api_version: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.api_version_not_found')))
        },
        HELOSCOPE: {
          some_other_field: Joi.string().required().error(new Error(req.t('Noc.errors.heloscope.some_other_field_not_found')))
        }
      }

      const schema = Joi.object({
        _id: Joi.string().required().error(new Error('_id fieldi mevcut değil')),
        type: Joi.string()
          .valid(...Object.keys(validationFields))
          .required()
          .error(new Error(req.t('Noc.errors.integration.type_not_found'))),
        name: Joi.string().required().error(new Error(req.t('Noc.errors.channel.name_not_found'))),
        ...Object.entries(validationFields).flatMap(([type, fields]) =>
          Object.entries(fields).map(([key, schema]) => ({
            [key]: Joi.when('type', {
              is: type,
              then: schema,
              otherwise: Joi.forbidden()
            })
          }))
        ).reduce((acc, field) => ({ ...acc, ...field }), {})
      }).required();

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  IntegrationNewPost: async (req, res, next) => {
    try {
      const validationFields = {
        TSOFT: {
          base_url: Joi.string().required().error(new Error(req.t('Noc.errors.integration.baseurl_not_found'))),
          username: Joi.string().required().error(new Error(req.t('Noc.errors.user.username_not_found'))),
          password: Joi.string().required().error(new Error(req.t('Noc.errors.user.password_not_found'))),
          version: Joi.string().allow('')
        },
        SHOPIFY: {
          store_name: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.store_name_not_found'))),
          store_front_key: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.store_front_key_not_found'))),
          admin_access_token: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.admin_access_token_not_found'))),
          api_version: Joi.string().required().error(new Error(req.t('Noc.errors.shopify.api_version_not_found')))
        },
        HELOSCOPE: {
          some_other_field: Joi.string().required().error(new Error(req.t('Noc.errors.heloscope.some_other_field_not_found')))
        }
      }

      const schema = Joi.object({
        type: Joi.string()
          .valid(...Object.keys(validationFields))
          .required()
          .error(new Error(req.t('Noc.errors.integration.type_not_found'))),
        name: Joi.string().required().error(new Error(req.t('Noc.errors.channel.name_not_found'))),
        ...Object.entries(validationFields).flatMap(([type, fields]) =>
          Object.entries(fields).map(([key, schema]) => ({
            [key]: Joi.when('type', {
              is: type,
              then: schema,
              otherwise: Joi.forbidden()
            })
          }))
        ).reduce((acc, field) => ({ ...acc, ...field }), {})
      }).required();

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  UsersList: async (req, res, next) => {
    try {
      const schema = Joi.object({
        body: {
          search: Joi.string().allow(''),
          sort: {
            field: Joi.string().valid('created_at', 'name').required().error(new Error(req.t('Noc.errors.dashboard.sort_field_invalid'))),
            value: Joi.number().valid(1, -1).required().error(new Error(req.t('Noc.errors.dashboard.sort_value_invalid')))
          },
          pagination: {
            page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
            page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found')))
          }
        },
        params: {
          id: Joi.string().required().error(new Error(req.t('Noc.errors.company_log.setup_id')))
        }
      })

      await schema.validateAsync({
        body: req.body,
        params: req.params
      })
      next()

    } catch (error) {
      next(error)
    }
  },

  UsersEditPost: async (req, res, next) => {
    try {
      const schema = Joi.object({
        type: Joi.string().required().error(new Error(req.t('Noc.errors.integration.type_not_found'))),
        name: Joi.string().required().error(new Error(req.t('Noc.errors.user.username_not_found'))),
        email: Joi.string().email().required().error(new Error(req.t('Noc.errors.user.email_regex'))),
        password: Joi.string().optional().allow(''),
        phone_number: Joi.string().allow('').error(new Error(req.t('Noc.errors.company.phone_not_found'))),
        team_ids: Joi.array().items(Joi.string()).optional(),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  UsersNewPost: async (req, res, next) => {
    try {
      const schema = Joi.object({
        type: Joi.string().required().error(new Error(req.t('Noc.errors.integration.type_not_found'))),
        name: Joi.string().required().error(new Error(req.t('Noc.errors.user.username_not_found'))),
        email: Joi.string().email().required().error(new Error(req.t('Noc.errors.user.email_regex'))),
        password: Joi.string().min(1).required().error(new Error(req.t('Noc.errors.user.password_not_found'))),
        team_ids: Joi.array().items(Joi.string()).optional(),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainersList: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  ListAvailableContainers: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainersNewPost: async (req, res, next) => {
    try {
      const schema = Joi.object({
        email: Joi.string().email().required().error(new Error(req.t('Onboarding.errors.invalid_email'))),
        company_name: Joi.string().required().error(new Error(req.t('Noc.errors.company.name_not_found'))),
        phone_number: Joi.string().required().error(new Error(req.t('App.errors.integration.phone_required')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainersSendConfirmCode: async (req, res, next) => {
    try {
      const schema = Joi.object({
        container_id: Joi.string().required().error(new Error(req.t('Noc.errors.container.id_not_found'))),
        certificate: Joi.string().required().error(new Error(req.t('Noc.errors.container.not_found_certificate'))),
        code_method: Joi.string().required().valid('sms', 'voice').error(new Error(req.t('Noc.errors.container.method')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainersConfirmCode: async (req, res, next) => {
    try {
      const schema = Joi.object({
        code: Joi.string().required().error(new Error(req.t('Noc.errors.container.code'))),
        container_id: Joi.string().required().error(new Error(req.t('Noc.errors.company_log.setup_id'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ChecknIntegration: async (req, res, next) => {
    try {
      const schema = Joi.object({
        type: Joi.string().required().error(new Error(req.t('Noc.errors.channel.type_not_found'))),
        base_url: Joi.string().required().error(new Error(req.t('Noc.errors.integration.baseurl'))),
        username: Joi.string().allow('').error(new Error(req.t('Noc.errors.user.username_not_found'))),
        password: Joi.string().allow('').error(new Error(req.t('Noc.errors.integration.password')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CheckContainer: async (req, res, next) => {
    try {
      const schema = Joi.object({
        email: Joi.string().email().required().error(new Error(req.t('Noc.errors.user.email_not_found'))),
        number: Joi.string().required().error(new Error(req.t('Noc.errors.container.not_found_phone'))),
        subdomain: Joi.string().required().error(new Error(req.t('Noc.errors.container.subdomain'))),
        code: Joi.string().required().error(new Error(req.t('Noc.errors.container.code'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CreateCompany: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company: Joi.object({
          name: Joi.string().required(),
          seller_code: Joi.string().optional(),
          phone_number: Joi.string().required(),
        }).required().error(new Error(req.t('Noc.errors.company.not_found'))),
        integration: Joi.object({
          type: Joi.string().required(),
          name: Joi.string().required(),
          is_active: Joi.boolean().required(),
          data: Joi.object({
            base_url: Joi.string().uri().required(),
            username: Joi.string().required(),
            password: Joi.string().required(),
            type: Joi.string().required(),
          }).required().error(new Error(req.t('Noc.errors.integration.not_found')))
        }).required().error(new Error(req.t('Noc.errors.integration.not_found'))),
        container: Joi.object({
          subdomain: Joi.string().required(),
          number: Joi.string().required(),
          code: Joi.string().required(),
          email: Joi.string().required(),
        }).required().error(new Error(req.t('Noc.errors.container.not_found'))),
        channel: Joi.object({
          name: Joi.string().required(),
          phone_number: Joi.string().required(),
          is_active: Joi.boolean().required()
        }).required().error(new Error(req.t('Noc.errors.channel.not_found'))),
        user: Joi.object({
          email: Joi.string().required(),
          name: Joi.string().required(),
          password: Joi.string().required()
        }).required().error(new Error(req.t('Noc.errors.user.not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  SendConfirmCodeForCompany: async (req, res, next) => {
    try {
      const schema = Joi.object({
        certificate: Joi.string().required().error(new Error(req.t('Noc.errors.container.not_found_certificate'))),
        code_method: Joi.string().valid('sms', 'voice').required().error(new Error(req.t('Noc.errors.container.method'))),
        id: Joi.string().required().error(new Error(req.t('Noc.errors.company_log.setup_id'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  IncompleteCompanies: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
      })

      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  CompanyConfirmCode: async (req, res, next) => {
    try {
      const schema = Joi.object({
        code: Joi.string().required().error(new Error(req.t('Noc.errors.container.code'))),
        id: Joi.string().required().error(new Error(req.t('Noc.errors.company_log.setup_id'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  FacebookConversationReport: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company_id: Joi.string().required().error(new Error(req.t('Global.errors.company_id_not_found'))),
        start_date: Joi.date().timestamp().required().error(new Error(req.t('App.errors.message_report.start_date_end_date_not_found'))),
        end_date: Joi.date().timestamp().required().error(new Error(req.t('App.errors.message_report.start_date_end_date_not_found'))),
        period: Joi.string().required().valid('HALF_HOUR', 'DAILY', 'MONTHLY').error(new Error(req.t('Noc.errors.report.period_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  FacebookMessageReport: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company_id: Joi.string().required().error(new Error(req.t('Global.errors.company_id_not_found'))),
        start_date: Joi.date().timestamp().required().error(new Error(req.t('App.errors.message_report.start_date_end_date_not_found'))),
        end_date: Joi.date().timestamp().required().error(new Error(req.t('App.errors.message_report.start_date_end_date_not_found'))),
        period: Joi.string().required().valid('HALF_HOUR', 'DAY', 'MONTH').error(new Error(req.t('Noc.errors.report.period_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  // SellersList: async (req, res, next) => {
  //   try {
  //     req.query.page = Number(req.query.page)
  //     req.query.page_size = Number(req.query.page_size)

  //     const schema = Joi.object({
  //       page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
  //       page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
  //     })

  //     await schema.validateAsync(req.query)
  //     next()
  //   } catch (error) {
  //     next(error)
  //   }
  // },

  // SellersCreate: async (req, res, next) => {
  //   try {
  //     const schema = Joi.object({
  //       name: Joi.string().required().error(new Error(req.t('Name and Code required'))),
  //       code: Joi.string().required().error(new Error(req.t('Name and Code required'))),
  //     })

  //     await schema.validateAsync(req.body)
  //     next()

  //   } catch (error) {
  //     next(error)
  //   }
  // },

  WebhooksCreate: async (req, res, next) => {
    try {
      const schema = Joi.object({
        phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
        webhook_url: Joi.string().required().error(new Error(req.t('Noc.errors.integration.baseurl_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  WebhooksDelete: async (req, res, next) => {
    try {
      const schema = Joi.object({
        phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
        webhook_url: Joi.string().required().error(new Error(req.t('Noc.errors.integration.baseurl_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  OnboardingWizardList: async (req, res, next) => {
    try {
      req.query.page = Number(req.query.page)
      req.query.page_size = Number(req.query.page_size)

      const schema = Joi.object({
        page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
        page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found'))),
        search: Joi.string().allow('')
      })
      await schema.validateAsync(req.query)
      next()

    } catch (error) {
      next(error)
    }
  },

  LoginAs: async (req, res, next) => {
    try {
      const schema = Joi.object({
        user_id: Joi.string().required().error(new Error(req.t('Site.errors.user.login.user_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainerEdit: async (req, res, next) => {
    try {
      const schema = Joi.object({
        phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
        container_id: Joi.string().required().error(new Error(req.t('Noc.errors.container.id_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ContainerDelete: async (req, res, next) => {
    try {
      const schema = Joi.object({
        container_id: Joi.string().required().error(new Error(req.t('Site.errors.user.login.user_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  MobileVersionEdit: async (req, res, next) => {
    try {

      const schema = Joi.object({
        id: Joi.string().required().error(new Error(req.t('Noc.errors.mobile.id_not_found'))),
        changelog: Joi.string().optional().error(new Error(req.t('Noc.errors.mobile.changelog_invalid'))),
        version: Joi.string().optional().error(new Error(req.t('Noc.errors.mobile.version_invalid'))),
        force_upgrade: Joi.boolean().optional().error(new Error(req.t('Noc.errors.mobile.force_upgrade_invalid'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  MobileVersionAdd: async (req, res, next) => {
    try {

      const schema = Joi.object({
        changelog: Joi.string().required().allow('').error(new Error(req.t('Noc.errors.mobile.changelog_invalid'))),
        version: Joi.string().required().error(new Error(req.t('Noc.errors.mobile.version_invalid'))),
        force_upgrade: Joi.boolean().required().error(new Error(req.t('Noc.errors.mobile.force_upgrade_invalid'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  MobileVersionDelete: async (req, res, next) => {
    try {

      const schema = Joi.object({
        id: Joi.string().required().error(new Error(req.t('Noc.errors.mobile.id_not_found')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  AddAdsMedias: async (req, res, next) => {
    try {

      const schema = Joi.object({
        name: Joi.string().required().error(new Error(req.t('Noc.errors.ads_medias.name_not_found'))),
        url: Joi.string().required().error(new Error(req.t('Noc.errors.ads_medias.url_not_found'))),
        redirect_url: Joi.string().optional().error(new Error(req.t('Noc.errors.ads_medias.redirect_url_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  EditAdsMedias: async (req, res, next) => {
    try {

      const schema = Joi.object({
        id: Joi.string().required().error(new Error(req.t('Noc.errors.ads_medias.id_not_found'))),
        name: Joi.string().optional().error(new Error(req.t('Noc.errors.ads_medias.name_not_found'))),
        url: Joi.string().optional().error(new Error(req.t('Noc.errors.ads_medias.url_not_found'))),
        redirect_url: Joi.string().optional().error(new Error(req.t('Noc.errors.ads_medias.redirect_url_not_found'))),
        is_active: Joi.boolean().optional().error(new Error(req.t('Noc.errors.ads_medias.is_active_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  DeleteAdsMedias: async (req, res, next) => {
    try {

      const schema = Joi.object({
        id: Joi.string().required().error(new Error(req.t('Noc.errors.ads_medias.id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  OrderAdsMedias: async (req, res, next) => {
    try {

      const schema = Joi.object({
        id: Joi.string().required().error(new Error(req.t('Noc.errors.ads_medias.id_not_found'))),
        new_index: Joi.number().required().error(new Error(req.t('Noc.errors.ads_medias.new_index_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  AddWhiteList: async (req, res, next) => {
    try {

      const schema = Joi.object({
        domain: Joi.string().uri().required(),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  DeleteWhiteList: async (req, res, next) => {
    try {

      const schema = Joi.object({
        domain: Joi.string().required(),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ConversationCount: async (req, res, next) => {
    try {
      const schema = Joi.object({
        start_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        end_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        name: Joi.string().optional().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        pagination: {
          sort: Joi.number().optional().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
          page: Joi.number().required().error(new Error(req.t('Global.errors.page_count_not_found'))),
          page_size: Joi.number().required().error(new Error(req.t('Global.errors.pagination_not_found')))
        }
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  MessageCount: async (req, res, next) => {
    try {

      const schema = Joi.object({
        start_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        end_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  ThinkerCount: async (req, res, next) => {
    try {

      const schema = Joi.object({
        start_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        end_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CompanyStatistics: async (req, res, next) => {
    try {

      const schema = Joi.object({
        start_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        end_date: Joi.string().required().error(new Error(req.t('Noc.errors.dashboard.start_date_invalid'))),
        company_name: Joi.string().optional(),
        is_active: Joi.boolean().optional(),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  MessageUpload: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
        contents: Joi.array().items(Joi.object({
          phone_number: Joi.string().required(),
          chat_name: Joi.string().required(),
          messages: Joi.array().items(Joi.object({
            id: Joi.string().required(),
            fromMe: Joi.boolean().required(),
            body: Joi.string().required(),
            time: Joi.number().required(),
            from: Joi.string().required(),
            to: Joi.string().required(),
            mediaUrl: Joi.string().optional(),
            mimetype: Joi.string().optional(),
            filehash: Joi.string().optional(),
            type: Joi.string().required()
          }))
        })).required()
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  SendConfirmCode: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
        phone_number_id: Joi.string().optional(),
        code_method: Joi.string().valid('SMS', 'VOICE').required().error(new Error(req.t('Noc.errors.container.invaid_method')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  VerifyConfirmCode: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
        code: Joi.string().required().error(new Error(req.t('Noc.errors.container.code')))
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  AddCreditLine: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  CheckCreditLine: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  RevokeCreditLine: async (req, res, next) => {
    try {
      const schema = Joi.object({
        channel_id: Joi.string().required().error(new Error(req.t('App.errors.channel.channel_id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  SetPackage: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company_id: Joi.string().required().error(new Error(req.t('Global.errors.company_id_not_found'))),
        package_id: Joi.string().required().error(new Error(req.t('Onboarding.errors.package_id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  EditPackage: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company_id: Joi.string().required().error(new Error(req.t('Global.errors.company_id_not_found'))),
        package_id: Joi.string().required().error(new Error(req.t('Onboarding.errors.package_id_not_found'))),
        name: Joi.string().required().error(new Error(req.t('App.errors.hepsiburada.name_not_found'))),
        type: Joi.string().optional(),
        description: Joi.string().required().error(new Error(req.t('App.errors.google.calendar.not_found_description'))),
        price: Joi.number().required().error(new Error(req.t('Onboarding.errors.package_price_not_found'))),
        currency: Joi.string().valid('₺', '$').required(),
        package_type: Joi.string().valid(enums.package_types.ANNUAL, enums.package_types.MONTHLY).required(),
        expiry_date: Joi.string().optional(),
        started_date: Joi.string().optional(),
        is_active: Joi.boolean().required().error(new Error(req.t('Noc.errors.channel.is_active'))),
        premium: Joi.boolean().optional().error(new Error(req.t('Noc.errors.channel.premium'))),
        data: Joi.any(),
        billtekrom_package_id: Joi.number().optional()
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

  DeletePackage: async (req, res, next) => {
    try {
      const schema = Joi.object({
        company_id: Joi.string().required().error(new Error(req.t('Global.errors.company_id_not_found'))),
        package_id: Joi.string().required().error(new Error(req.t('Onboarding.errors.package_id_not_found'))),
      })

      await schema.validateAsync(req.body)
      next()

    } catch (error) {
      next(error)
    }
  },

}

module.exports = Validation
