const jsonwebtoken = require('jsonwebtoken')
const pino = require('pino')()

const User = require('../../models/User')
const ServiceAccount = require('../../models/ServiceAccount')

const enums = require('../../libs/enums')

module.exports = async (req, res, next) => {
  try {

    const jwtToken = req.headers['authorization'] || null

    if (!jwtToken) {
      return res.modifiedResponse(401, {
        message: req.t('Global.errors.token_required')
      })
    }

    const token = jwtToken.split(' ')[1];
    const decoded = jsonwebtoken.decode(token, { complete: true });

    const user = await User.findById(decoded.payload.user_id).populate('company_id')

    if (!user) {
      return res.modifiedResponse(401, {
        message: req.t('Global.errors.not_authorized')
      })
    }

    if (user.tester === false) {
      if (!user.company_id.is_active) {
        return res.modifiedResponse(401, {
          message: req.t('Global.errors.not_authorized')
        })
      }
    }

    if (user.deleted_at) {
      return res.modifiedResponse(401, {
        message: req.t('Global.errors.not_authorized')
      })
    }

    if (![enums.acl_roles.COMPANY_OWNER, enums.acl_roles.COMPANY_USER, enums.acl_roles.SERVICE_ACCOUNT].includes(user.type)) {
      return res.modifiedResponse(401, {
        message: req.t('Global.errors.not_authorized')
      })
    }

    let platform = ''
    if (user.type === enums.acl_roles.SERVICE_ACCOUNT) {
      jsonwebtoken.verify(token, process.env.APP_SECRET_KEY + user.id)

      const serviceAccount = await ServiceAccount.findOne({
        user_id: user.id,
        deleted_at: {
          $exists: false
        }
      })

      if (!serviceAccount) {
        return res.modifiedResponse(401, {
          message: req.t('Global.errors.not_authorized')
        })
      }

      platform = enums.client_platforms.SERVICE
    } else {
      try {
        jsonwebtoken.verify(token, process.env.APP_SECRET_KEY + user.vData.getAgentAppSalt())
        platform = enums.client_platforms.WEB
      } catch (e) {
        jsonwebtoken.verify(token, process.env.APP_SECRET_KEY + user.vData.getMobileAppSalt())
        platform = enums.client_platforms.MOBILE
      }
    }

    req.user = user
    req.platform = platform
    return next()
  } catch (error) {
    pino.error({
      trace_id: req.trace_id,
      timestamp: new Date(),
      message: error.message
    })

    return res.modifiedResponse(401, {
      message: req.t('Global.errors.not_authorized')
    })
  }
}