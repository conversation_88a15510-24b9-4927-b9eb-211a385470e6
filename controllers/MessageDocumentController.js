const https = require('https')
const fs = require('fs')
const axios = require('axios')
const createError = require('http-errors')

const enums = require('../libs/enums')

const Message = require('./../models/Message')
const Channel = require('./../models/Channel')

const FileService = require('./../services/FileService')

const WhatsappApiService = require('./../integrations/Whatsapp/WhatsappApiService')

module.exports = {

  /**
   *
   * Mesajları listelememizi sağlayan controller
   *
   * @param req
   * @param res
   * @param next
   *
   */
  Index: (req, res, next) => {

    let messageId = req.params.id
    let messageHash = req.params.hash

    return Message.findById(messageId).populate('conversation_id').then(message => {

      if (!message) {
        throw new createError.NotFound('Mesaj detay elde edilemedi.')
      }

      if (message.hash !== messageHash) {
        throw new createError.NotFound('Mesaj detay elde edilemedi.')
      }

      return Channel.findById(message.conversation.channel_id).then(channel => {

        if (!channel) {
          throw new createError.NotFound('Kanal detay elde edilemedi.')
        }

        return Promise.resolve().then(() => {

          switch (channel.provider) {

            case enums.channel_providers.TEKROM:

              return WhatsappApiService.getContainerByChannel(channel.vSettings.getApiKey()).then(container => {

                let contentId = message.content.id
                if (message.content.referral) {
                  if (message.content.referral.image) {
                    contentId = message.content.referral.image.id
                  } else if (message.content.referral.video) {
                    contentId = message.content.referral.video.id
                  } else {
                    contentId = ''
                  }
                }

                return {
                  url: container.vBaseUrl + '/v1/media/' + contentId,
                  method: 'GET',
                  responseType: 'arraybuffer',
                  headers: {
                    Authorization: 'Bearer ' + container.token
                  },
                  httpsAgent: new https.Agent({
                    rejectUnauthorized: false
                  })
                }

              })

            default:

              throw new createError.NotFound('Provider yok!!!')

          }

        }).then(config => {

          return axios.request(config).then(response => {

            res.set('Accept-Ranges', 'bytes')
            res.set('Cache-Control', 'public')
            res.set('Content-Type', response.headers['content-type'])

            return res.send(response.data)

          }).catch(() => {

            return FileService.download(enums.helorobo_not_found).then(path => {

              process.nextTick(() => {
                fs.unlinkSync(`${process.cwd()}/${path}`)
              })

              return res.sendFile(`${process.cwd()}/${path}`)

            })

          })

        })

      })

    }).catch(next)

  }

}
