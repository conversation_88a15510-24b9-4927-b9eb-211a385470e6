const moment = require('moment')

const enums = require('../../../libs/enums')
const helpers = require('../../../libs/helpers')

const Company = require('../../../models/Company')
const Channel = require('../../../models/Channel')
const Chat = require('../../../models/Chat')
const User = require('../../../models/User')
const ChatReplyReport = require('../../../models/ChatReplyReport')
const AgentActiveStatus = require('../../../models/AgentActiveStatus')


module.exports = async (req, res, next) => {
  try {

    let startDate = req.body.start_date
    let endDate = req.body.end_date
    const companyName = req.body.company_name
    const isActive = req.body.is_active

    startDate = moment(startDate, 'YYYY-MM-DD').startOf('days')
    endDate = moment(endDate, 'YYYY-MM-DD').endOf('days')

    const response = []

    const query = {
      deleted_at: {
        $exists: false
      }
    }

    if (companyName) {
      query.name = new RegExp(companyName, 'i')
    }

    if (isActive === false || isActive === true) {
      query.is_active = isActive
    }

    const company = await Company.find(query).sort({ _id: -1 })
    for (const item of company) {
      const channels = await Channel.find({
        company_id: item._id,
        deleted_at: {
          $exists: false
        }
      })

      const chats = await Chat.countDocuments({
        channel_id: {
          $in: channels.map(channel => channel._id)
        },
        _id: {
          $gte: helpers.createObjectId(startDate.toDate()),
          $lte: helpers.createObjectId(endDate.toDate())
        }
      })

      const reply = await ChatReplyReport.countDocuments({
        channel_id: {
          $in: channels.map(channel => channel._id)
        },
        agent_id: {
          $exists: true
        },
        _id: {
          $gte: helpers.createObjectId(startDate.toDate()),
          $lte: helpers.createObjectId(endDate.toDate())
        }
      })

      const users = await User.find({
        company_id: item._id,
        type: {
          $in: [enums.acl_roles.COMPANY_USER, enums.acl_roles.COMPANY_OWNER]
        },
        tester: { $ne: true },
        deleted_at: {
          $exists: false
        }
      })

      const agentStatus = await AgentActiveStatus.find({
        agent_id: {
          $in: users.map(user => user._id)
        },
        _id: {
          $gte: helpers.createObjectId(startDate.toDate()),
          $lte: helpers.createObjectId(endDate.toDate())
        },
        online: {
          $exists: true
        },
        offline: {
          $exists: true
        }
      })

      const activeTimes = agentStatus.reduce((a, b) => a + moment(b.offline).diff(b.online, 'seconds'), 0)

      const lastAgent = await AgentActiveStatus.findOne({
        agent_id: {
          $in: users.map(user => user._id)
        },
        online: {
          $exists: true
        }
      }).sort({ _id: -1 }).populate('agent_id')

      response.push({
        company_name: item.name,
        chat_count: chats,
        channel_count: channels.length,
        reply_count: reply,
        user_count: users.length,
        agent_active_times: activeTimes,
        last_agent_data: lastAgent ? {
          id: lastAgent.user.id,
          name: lastAgent.user.name,
          active_time: lastAgent.online,
          platform: lastAgent.platform
        } : null
      })
    }

    return res.modifiedResponse(200, {
      success: true,
      items: response
    })

  } catch (error) {
    next(error)
  }
}