const faker = require('faker')
const moment = require('moment')
const bcrypt = require('bcrypt')
const { nanoid } = require('nanoid')
const createError = require('http-errors')
const { Types } = require('mongoose')

const enums = require('../../libs/enums')
const utils = require('../../libs/utils')
const helpers = require('../../libs/helpers')

const Company = require('../../models/Company')
const User = require('../../models/User')
const Team = require('../../models/Team')
const LoginWith = require('../../models/LoginWith')
const Package = require('../../models/Package')
const CompanyHasPackage = require('../../models/CompanyHasPackage')

const QueueService = require('../../services/QueueService')
const UserLogService = require('../../services/UserLogService')
const ThinkerService = require('../../services/ThinkerService')
const HeloBotService = require('../../services/HelobotService')
const BillTekromService = require('../../services/BillTekromService')

const CreateCompany = (req, name, phoneNumber, billtekromAccountId) => {

  let company = new Company()

  company.name = name
  company.socket_code = faker.random.uuid()
  company.phone_number = phoneNumber
  company.billtekrom_account_id = billtekromAccountId

  const createUnqiueNumber = () => {

    const number = Math.floor(Math.random() * ********)

    return Company.findOne({ company_code: number.toString() }).then(existCompany => {

      if (existCompany) {
        return createUnqiueNumber()
      }

      return number

    })

  }

  return createUnqiueNumber().then(number => {

    const package = req.body.package

    company.data = company.vData.getData()
    company.data.package = package

    company.markModified('data')
    company.company_code = number

    return company.save()

  })

}

const CreateDefaultAgent = async (company) => {
  const email = nanoid(5).toLowerCase()

  const user = await new User({
    name: 'HeloRobo Tester',
    company_id: company._id,
    email: `${email}.<EMAIL>`,
    tester: true,
    phone_number: '',
    is_active: true,
    type: enums.acl_roles.COMPANY_OWNER,
    socket_code: utils.generateHash(10)
  }).save()

  await new LoginWith({
    email: `${email}.<EMAIL>`,
    type: enums.login_with_types.EMAIL_PASSWORD,
    user_id: user._id,
    password: bcrypt.hashSync(faker.random.uuid(), 13)
  }).save()
}

const CompanyController = {

  List: async (req, res, next) => {
    try {
      const sort = req.body.sort
      const isActive = req.body.is_active
      const isDemo = req.body.is_demo
      const search = req.body.search
      const pagination = req.body.pagination

      const page = Number(pagination.page || 1)
      const page_size = Number(pagination.page_size || 20)

      const query = []

      if (search) {
        query.push({
          $match: {
            $or: [
              {
                name: new RegExp(search, 'i'),
              },
              {
                phone_number: new RegExp(search, "i")
              }
            ]
          }
        })
      }

      if (typeof isActive === 'boolean') {
        query.push({
          $match: {
            is_active: isActive
          }
        })
      }

      if (typeof isDemo === 'boolean') {
        query.push({
          $match: {
            is_demo: isDemo
          }
        })
      }

      if (sort?.field) {
        query.push(
          {
            $sort: {
              [`${sort.field}`]: sort.value
            }
          }
        )
      }

      const companies = await Company.aggregate([
        ...query,
        {
          $lookup: {
            from: 'sellers',
            localField: 'seller_id',
            foreignField: '_id',
            as: 'seller'
          }
        },
        {
          $facet: {
            items: [
              { $skip: (page - 1) * page_size },
              { $limit: page_size }
            ],
            total: [{ "$count": "count" }]
          },
        }
      ])

      const companyList = companies[0].items.map(company => {
        return {
          id: company._id,
          seller_code: company?.seller[0]?.code || '',
          name: company.name,
          is_active: company.is_active || false,
          is_demo: company.is_demo || false,
          phone_number: company.phone_number || '',
          code: company.code || '',
          created_at: company.created_at,
          billtekrom_account_id: company.billtekrom_account_id
        }
      })

      return res.modifiedResponse(200, {
        items: companyList,
        page: page,
        page_size: page_size,
        total: companies[0]?.total[0]?.count || 0
      })
    } catch (error) {
      next(error)
    }

  },

  Create: async (req, res, next) => {
    try {
      const name = req.body.name
      const phoneNumber = req.body.phone_number
      const billtekromAccountId = req.body.billtekrom_account_id

      const company = await Company.findOne({
        $or: [
          {
            name: name
          },
          {
            phone_number: phoneNumber
          }
        ]

      })

      if (company) {
        throw new createError.NotFound(req.t('Noc.errors.company.number_allready_using'))
      }

      const createdCompany = await CreateCompany(req, name, phoneNumber, billtekromAccountId)

      await CreateDefaultAgent(createdCompany)

      const user = req.user
      await UserLogService.createLog(createdCompany._id, user._id, enums.user_logs.create_company, {
        agent_name: user.name,
        name: name,
        phone_number: phoneNumber,
        billtekrom_account_id: billtekromAccountId
      }, true)

      return res.modifiedResponse(200, {
        id: createdCompany.id
      })

    } catch (error) {
      next(error)
    }
  },

  Get: async (req, res, next) => {

    try {
      const companyId = req.params.id

      let company = await Company.findById(companyId)

      if (!company) {
        throw new createError.BadRequest('Company Not Found')
      }

      return res.modifiedResponse(200, {
        id: company.id,
        name: company.name,
        is_active: company.is_active || false,
        phone_number: company.phone_number,
        code: company.code,
        billtekrom_account_id: company.billtekrom_account_id,
        created_at: moment(company.created_at)
      })

    } catch (error) {
      next(error)
    }
  },

  Edit: async (req, res, next) => {

    try {

      const companyId = req.params.id

      let company = await Company.findById(companyId)

      if (!company) {
        throw new createError.BadRequest('Company not found')
      }

      if (req.method === 'POST') {

        const oldCompany = company.toObject()
        const companyName = req.body.name
        const phoneNumber = req.body.phone_number
        const package = req.body.package
        const billtekromAccountId = req.body.billtekrom_account_id
        const profitMultiplier = req.body.profit_multiplier
        const isDemo = req.body.is_demo

        if (companyName.trim() === '') {
          throw new createError.NotFound(req.t('Noc.errors.company.name_not_found'))
        }

        if (phoneNumber.trim() === '') {
          throw new createError.NotFound(req.t('Noc.errors.company.phone_not_found'))
        }

        const agentCount = await User.countDocuments({
          company_id: companyId,
          type: { $in: [enums.acl_roles.COMPANY_OWNER, enums.acl_roles.COMPANY_USER] },
          deleted_at: { $exists: false }
        })

        if (!package.agent_settings.limit.unlimit && package.agent_settings.limit.limit < agentCount) {
          throw new createError.NotFound(req.t('Noc.errors.company.limit_number_small_count_agent'))
        }

        company.name = companyName
        company.phone_number = phoneNumber
        company.billtekrom_account_id = billtekromAccountId

        if (typeof isDemo === 'boolean') {
          company.is_demo = isDemo
        }

        const companyData = company.vData
        companyData.setCompanyPackage(package)

        if (Number(profitMultiplier)) {
          companyData.setWhatsappProfitMultiplier(profitMultiplier)
        }

        company.data = companyData.getData()
        company.markModified('data')

        await company.save()

        const nocUser = req.user
        const logs = await helpers.detectObjectChanges(oldCompany, company.toJSON())
        if (logs) {
          await UserLogService.createLog(company._id, nocUser._id, enums.user_logs.edit_company, {
            agent_name: helpers.getName(nocUser),
            company_name: company.name
          }, true, logs)
        }

        return res.modifiedResponse(200, { success: true })
      }

      return res.modifiedResponse(200, {
        id: company.id,
        name: company.name,
        phone_number: company.phone_number,
        is_active: true,
        created_at: company.created_at,
        package: company.vData.getCompanyPackage(),
        billtekrom_account_id: company.billtekrom_account_id,
        profit_multiplier: company.vData.getWhatsappProfitMultiplier(),
        is_demo: company.is_demo
      })

    } catch (error) {
      next(error)
    }

  },

  StatusChange: async (req, res, next) => {

    try {

      const company = await Company.findById(req.params.id)

      if (!company) {
        throw new createError.NotFound(req.t('App.errors.auth.login_as_agent.company_not_found'))
      }

      company.is_active = !company.is_active
      await company.save()

      QueueService.publishToAppSocket({
        event: enums.agent_app_socket_events.AGENT_LOGGED_OUT,
        socket_rooms: [company.vSocketCode],
      }, req.language)

      const user = req.user
      await UserLogService.createLog(company._id, user._id, enums.user_logs.status_change_company, { agent_name: user.name, name: company.name, status: company.is_active }, true)

      return res.modifiedResponse(200, { success: true })

    } catch (error) {
      next(error)
    }
  },

  GetCompanyTeam: async (req, res, next) => {
    try {

      const company = await Company.findById(req.query.company_id)

      if (!company) {
        throw new createError.NotFound(req.t('App.errors.auth.login_as_agent.company_not_found'))
      }

      const teams = await Team.find({ company_id: company._id, deleted_at: { $exists: false } })

      return res.modifiedResponse(200, {
        items: teams.map(a => {
          return {
            id: a.id,
            name: a.name,
            status: a.status
          }
        })
      })
    } catch (error) {
      next(error)
    }
  },

  GetPackage: async (req, res, next) => {
    try {
      const isPackage = await CompanyHasPackage.findOne({
        company_id: new Types.ObjectId(req.params.id),
        deleted_at: {
          $exists: false
        }
      }).lean()
      if (!isPackage) {
        throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
      }

      isPackage.id = isPackage._id.toString()

      delete isPackage._id

      return res.modifiedResponse(200, {
        data: isPackage
      })
    } catch (error) {
      next(error)
    }
  },

  SetPackage: async (req, res, next) => {
    try {
      const isPackage = await Package.findOne({
        _id: new Types.ObjectId(req.body.package_id),
        deleted_at: {
          $exists: false
        }
      })
      if (!isPackage) {
        throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
      }

      const isAdded = await CompanyHasPackage.findOne({
        company_id: new Types.ObjectId(req.body.company_id),
        package_id: new Types.ObjectId(req.body.package_id),
        deleted_at: {
          $exists: false
        }
      })

      if (!isAdded) {
        const { package, period } = helpers.packageSettings(isPackage)

        const companyPackage = await new CompanyHasPackage({
          company_id: new Types.ObjectId(req.body.company_id),
          package_id: new Types.ObjectId(req.body.package_id),
          type: package.type,
          name: package.name,
          description: package.description,
          is_active: package.is_active,
          price: package.price,
          data: package.data,
          package_type: package.package_type,
          expiry_date: period,
          currency: package.currency,
          billtekrom_package_id: package.billtekrom_package_id,
          started_date: new Date(),
          is_demo: enums.billtekrom_free_package_id === req.body.package_id
        }).save()

        await helpers.channelModulesSettings(package.data, companyPackage.company_id)

        if (helpers.isModuleTimeOut(package.data.thinker) === true) {
          process.nextTick(() => {
            ThinkerService.CompanyStopFlows(companyPackage.company_id, req.trace_id)
          })
        }

        if (helpers.isModuleTimeOut(package.data.helobot) === true) {
          process.nextTick(() => {
            HeloBotService.CompanyStopBots(companyPackage.company_id, req.trace_id)
          })
        }
      }

      return res.modifiedResponse(200, {
        success: true
      })
    } catch (error) {
      next(error)
    }
  },

  EditPackage: async (req, res, next) => {
    try {

      const isPackage = await CompanyHasPackage.findOne({
        _id: new Types.ObjectId(req.body.package_id),
        company_id: new Types.ObjectId(req.body.company_id),
        deleted_at: {
          $exists: false
        }
      })
      if (!isPackage) {
        throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
      }

      isPackage.name = req.body.name
      isPackage.type = req.body.type
      isPackage.description = req.body.description
      isPackage.price = req.body.price
      isPackage.currency = req.body.currency
      isPackage.is_active = req.body.is_active
      isPackage.package_type = req.body.package_type
      isPackage.expiry_date = req.body.expiry_date
      isPackage.started_date = req.body.started_date
      isPackage.billtekrom_package_id = req.body.billtekrom_package_id
      isPackage.premium = req.body.premium || false
      if (req.body.is_demo) {
        isPackage.is_demo = req.body.is_demo
      }
      isPackage.data = req.body.data
      isPackage.markModified('data')
      await isPackage.save()

      await helpers.channelModulesSettings(req.body.data, isPackage.company_id)

      return res.modifiedResponse(200, {
        success: true
      })
    } catch (error) {
      next(error)
    }
  },

  DeletePackage: async (req, res, next) => {
    try {
      const isPackage = await CompanyHasPackage.findOne({
        _id: new Types.ObjectId(req.body.package_id),
        company_id: new Types.ObjectId(req.body.company_id),
        deleted_at: {
          $exists: false
        }
      })
      if (!isPackage) {
        throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
      }

      isPackage.deleted_at = new Date()
      await isPackage.save()

      return res.modifiedResponse(200, {
        success: true
      })
    } catch (error) {
      next(error)
    }
  },

  SyncBilltekrom: async (req, res, next) => {
    try {
      const company = await Company.findOne({
        _id: new Types.ObjectId(req.params.id),
        is_active: true,
        deleted_at: { $exists: false }
      })

      if (!company) {
        throw new createError.NotFound(req.t('App.errors.auth.login_as_agent.company_not_found'))
      }

      if (!company.billtekrom_account_id) {
        throw new createError.NotFound(req.t('Global.errors.billtekrom_account_id_not_found'))
      }

      const accountServices = await BillTekromService.getAccountServices(company.billtekrom_account_id)

      const service = accountServices.data.find(a =>
        moment().unix() > moment(a.start_time).unix()
        &&
        moment().unix() < moment(a.end_time).unix()
        &&
        enums.billtekrom_package_ids.includes(a.package_id)
        &&
        a.is_active === true
      )

      if (!service) {
        throw new createError.NotFound(req.t('Global.errors.billtekrom_account_id_not_found'))
      }

      const isPackage = await Package.findOne({
        billtekrom_package_id: service.package_id,
        is_active: true,
        deleted_at: { $exists: false }
      })
      if (!isPackage) {
        throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
      }

      const hasCompanyPackage = await CompanyHasPackage.findOne({
        company_id: company._id,
        deleted_at: { $exists: false }
      })
      if (hasCompanyPackage) {
        hasCompanyPackage.name = isPackage.name
        hasCompanyPackage.description = isPackage.description
        hasCompanyPackage.billtekrom_package_id = service.package_id
        hasCompanyPackage.started_date = moment(service.start_time).utc(true).toDate()
        hasCompanyPackage.data.billtekrom_service_id = service.id
        hasCompanyPackage.expiry_date = moment(service.end_time).utc(true).toDate()
        hasCompanyPackage.price = service.price
        hasCompanyPackage.currency = service.currency === 'USD' ? '$' : '₺'
        hasCompanyPackage.is_demo = service.is_demo
        hasCompanyPackage.markModified('data')
        await hasCompanyPackage.save()
      } else {
        const packageType = service.package.package_time_type === 'month' ? enums.package_types.MONTHLY : enums.package_types.ANNUAL
        const { package } = helpers.packageSettings(isPackage)

        await new CompanyHasPackage({
          company_id: company._id,
          package_id: package._id,
          billtekrom_package_id: service.package_id,
          started_date: moment(service.start_time).utc(true).toDate(),
          data: {
            ...package.data,
            billtekrom_service_id: service.id,
            monthly_conversation_limit: company.data.package.monthly_conversation_limit,
            agent_settings: company.data.package.agent_settings,
            channel_settings: company.data.package.channel_settings,
          },
          expiry_date: moment(service.end_time).utc(true).toDate(),
          price: service.price,
          currency: service.currency === 'USD' ? '$' : '₺',
          name: package.name,
          description: package.description,
          is_active: true,
          package_type: packageType,
          is_demo: service.is_demo
        }).save()
      }

      return res.modifiedResponse(200, {
        success: true
      })
    } catch (error) {
      next(error)
    }
  }

}

module.exports = CompanyController

