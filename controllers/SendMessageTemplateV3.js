const createError = require('http-errors')
const pino = require('pino')()

const WhatsappApiService = require('../integrations/Whatsapp/WhatsappApiService')
const ShopifyService = require('../integrations/Shopify/ShopifyService')

const enums = require('../libs/enums')
const utils = require('../libs/utils')
const Channel = require('../models/Channel')

const Chat = require('../models/Chat')
const Container = require('../models/Container')
const Message = require('../models/Message')
const User = require('../models/User')
const MessageTemplateAction = require('../models/MessageTemplateAction')
const MessageTemplateActionMessages = require('../models/MessageTemplateActionMessages')
const Company = require('../models/Company')
const MessageTemplate = require('../models/MessageTemplate')

const QueueService = require('../services/QueueService')
const BillTekromService = require('../services/BillTekromService')
const ChannelService = require('../services/ChannelService')
const MessageTemplateService = require('../services/MessageTemplateService')

const DashPresenter = require('../presenters/Dash')

const RedisConnection = require('../modules/RedisConnection')

module.exports = async (req, res, next) => {

  const data = req.body.data
  const secretKey = req.body.secret_key

  const messageId = data.message_id
  const userId = data.user_id
  const messageTemplateComponents = data.message_template
  const requestMessageTemplate = data.request_message_template

  try {
    if (!new RegExp(/^[0-9]+$/).test(data.to_number)) {
      throw new createError.BadRequest('Lütfen Telefon Numarasında Sayıdan Başka Karakter Bulunudurmayın')
    }

    if (secretKey !== process.env.GET_CONTAINER_KEY) {
      throw new createError.BadRequest('Güvenlik Kodu yanlış')
    }

    if (!data) {
      throw new createError.BadRequest('Mesaj Gönderme için Data yok')
    }

    if (!messageId) {
      throw new createError.BadRequest('Template Job Id Yok')
    }

    // Kanal Bulunuyor
    const channel = await Channel.findOne({ ext_id: data.channel_number, type: enums.channel_types.WHATSAPP_NUMBER, deleted_at: { $exists: false } })
    if (!channel) {
      return res.status(400).json({ message: 'Channel Bulunamadı' })
    }

    const company = await Company.findOne({
      _id: channel.company_id,
      is_active: true,
      deleted_at: {
        $exists: false
      }
    })
    if (!company) {
      return res.status(400).json({ message: 'Company Bulunamadı' })
    }

    const messageTemplate = await MessageTemplate.findOne({ company_id: company._id, status: true })
    if (!messageTemplate) {
      throw new createError.NotFound(req.t('MessageTemplate.errors.message_template_not_found'))
    }

    // gönderim yapılan kanalın waba durumu kontrol ediliyor
    let wabaStatus = await RedisConnection.getWabaStatus(channel.id, channel.vSettings.getWabaId())
    if (!wabaStatus) {
      const status = await MessageTemplateService.GetWabaStatus(messageTemplate, data.channel_id, 'tr', req.trace_id)
      wabaStatus = status.data

      if (wabaStatus.status !== 'RESTRICTED') {
        await RedisConnection.setWabaStatus(channel.id, channel.vSettings.getWabaId(), JSON.stringify(wabaStatus))
      } else {
        if (data.send_wizard_id) {
          await MessageTemplateService.SendWizardStop(messageTemplate, {
            send_wizard_id: data.send_wizard_id,
            channel_id: data.channel_id,
            code: enums.message_template_codes[100]
          }, 'tr', req.trace_id).catch((error) => {
            pino.error({
              trace_id: req.trace_id,
              timestamp: new Date(),
              message: data.send_wizard_id + ' send wizardı durdurmaya çalışırken hata oluştu',
              error: JSON.stringify(error.response?.data || { message: 'İstek Yapılamadı' })
            })
          })
        }
      }
    }

    // gönderilen mesaj şablonu durumu kontrol ediliyor
    let templateStatus = await RedisConnection.getMessageTemplateStatus(channel.id, channel.vSettings.getWabaId(), data.message_template_id)
    if (!templateStatus) {
      const status = await MessageTemplateService.GetTemplate(messageTemplate, data.message_template_id, data.channel_id, 'tr', req.trace_id)
      templateStatus = status.data.message_template

      if (templateStatus.status === 'APPROVED') {
        await RedisConnection.setMessageTemplateStatus(channel.id, channel.vSettings.getWabaId(), data.message_template_id, JSON.stringify(templateStatus))
      } else {
        if (data.send_wizard_id) {
          await MessageTemplateService.SendWizardStop(messageTemplate, {
            send_wizard_id: data.send_wizard_id,
            channel_id: data.channel_id,
            code: enums.message_template_codes[101]
          }, 'tr', req.trace_id).catch((error) => {
            pino.error({
              trace_id: req.trace_id,
              timestamp: new Date(),
              message: data.send_wizard_id + ' send wizardı durdurmaya çalışırken hata oluştu',
              error: JSON.stringify(error.response?.data || { message: 'İstek Yapılamadı' })
            })
          })
        }
      }
    }

    // shopify müşterisi ise kanalın kredi durumu kontrol ediliyor
    if (company.shopify_info_id || company.billtekrom_account_id) {
      const cacheStatus = await RedisConnection.getCompanyWhatsappCreditStatus(company.id)
      if (cacheStatus) {
        if (cacheStatus === 'false') {
          pino.info({
            trace_id: req.trace_id,
            timestamp: new Date(),
            message: data.send_wizard_id + ' send wizardı kredi bittiği için duraklatıldı',
            data: JSON.stringify({
              company_id: company.id,
              channel_id: channel.id,
              channel_ext_id: channel.ext_id
            })
          })

          return res.status(400).json({ message: 'Kredi Bittiği için İşlem Duraklatıldı' })
        }
      } else {
        const wabaIds = await ChannelService.GetChannelWabaIdsHasAllocationId(company._id)
        if (wabaIds.length === 0) {
          return res.status(400).json({ message: 'Kanalların Wabası Yok' })
        }

        let usable = false
        if (company.billtekrom_account_id) {
          const billtekromData = await BillTekromService.GetMonthlyMessageLimit(req.trace_id, company, wabaIds)
          usable = billtekromData.usable
        } else if (company.shopify_info_id) {
          const shopifyData = await ShopifyService.CheckCreditStatus(req.trace_id, company, wabaIds)
          usable = shopifyData.usable
        }

        await RedisConnection.setCompanyWhatsappCreditStatus(company.id, usable)

        if (usable === false && data.send_wizard_id) {
          await MessageTemplateService.SendWizardStop(messageTemplate, {
            send_wizard_id: data.send_wizard_id,
            channel_id: data.channel_id,
            code: enums.message_template_codes[103]
          }, 'tr', req.trace_id).catch((error) => {
            pino.error({
              trace_id: req.trace_id,
              timestamp: new Date(),
              message: data.send_wizard_id + ' send wizardı durdurmaya çalışırken hata oluştu',
              error: JSON.stringify(error.response?.data || { message: 'İstek Yapılamadı' })
            })
          })

          pino.info({
            trace_id: req.trace_id,
            timestamp: new Date(),
            message: data.send_wizard_id + ' send wizardı kredi bittiği için duraklatıldı',
            data: JSON.stringify({
              company_id: company.id,
              channel_id: channel.id,
              channel_ext_id: channel.ext_id
            })
          })

          return res.status(400).json({ message: 'Kredi Bittiği için İşlem Duraklatıldı' })
        }
      }
    }

    let container = null
    if (channel.provider === enums.channel_providers.TEKROM) {
      // Container bulunuıyor
      container = await Container.findOne({ code: data.container_code, deleted_at: { $exists: false } })

      if (!container) {
        return res.status(400).json({ message: 'Container Bulunamadı' })
      }
    }

    let user
    // user id varsa agent app üzerinden gönderilmiştir. yoksa api ile gönderilmiştir
    if (userId) {
      user = await User.findById(userId).populate('company_id')
    } else {
      user = await User.findOne({
        type: enums.acl_roles.COMPANY_OWNER,
        company_id: channel.company_id,
        tester: { $ne: true },
        deleted_at: {
          $exists: false
        }
      }).populate('company_id').sort({ created_at: 1 })
    }
    if (!user) {
      return res.status(400).json({ message: 'Şirketin Kullanıcısı Bulunamadı' })
    }

    // Chat var mı konrol ediliyor
    let chat = await Chat.findOne({ ext_id: data.to_number, channel_id: channel._id })

    if (chat?.is_blocked) {
      return res.status(400).json({ message: 'Bu Konuşma Daha Önce Engellenmiştir.' })
    }

    if (!chat) {
      // Chat oluşturuluyor
      chat = await new Chat({
        channel_id: channel._id,
        title: data.full_name || data.to_number,
        ext_id: data.to_number,
        archived_at: Date.now(),
        archived: true,
        hide: true
      }).save()
    }

    if (!chat.title && data.full_name) {
      chat.title = data.full_name
      await chat.save()
    }

    if (chat.title === data.to_number) {
      chat.title = data.full_name || data.to_number
      await chat.save()
    }

    const content = {
      components: messageTemplateComponents || [],
      params: requestMessageTemplate,
      language: data.language,
      template_id: data.message_template_id
    }

    // Mesaj oluşturuluyor
    const message = await new Message({
      type: enums.message_types.WHATSAPP_TEMPLATEV2,
      content: content,
      conversation_id: chat._id,
      user_id: user.id,
      from_type: enums.message_from_types.AGENT,
      send_status: enums.message_send_statuses.QUEUED,
      channel_id: channel._id,
      data: {
        mark_as_seen_event: true,
        message_id: messageId,
        send_wizard_id: data.send_wizard_id
      },
      hash: utils.generateHash(),
      created_at: Date.now(),
      platform: req.platform
    }).save()

    chat.last_message_id = message._id
    if (!chat.owner_user_id) {
      chat.archived_at = new Date()
      chat.archived = true
      chat.hide = true
    }
    chat.sort_field = message.created_at
    await chat.save()

    let id = null
    // mesaj whatsapp tarafına iletiliyor
    const whatsappJson = {
      recipient_type: 'individual',
      to: data.to_number,
      type: 'template',
      template: {
        name: data.template_name,
        language: {
          policy: 'deterministic',
          code: data.language
        },
        components: requestMessageTemplate
      }
    }

    try {
      if (channel.provider === enums.channel_providers.CLOUD) {
        whatsappJson.messaging_product = 'whatsapp'

        id = await WhatsappApiService.sendMessageCloudRequest(req, channel, whatsappJson)
      } else {
        id = await WhatsappApiService.sendMessageSpecial(req, channel, whatsappJson)
      }
    } catch (err) {
      pino.error({
        trace_id: req.trace_id,
        message: err.message,
        error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' }),
        timestamp: new Date(),
        data: JSON.stringify({
          data: whatsappJson,
          channel_id: channel._id.toString()
        })
      })

      message.error_message = 'İstek Atılamadı'
      if (channel.provider === enums.channel_providers.CLOUD) {
        if (err.response?.data?.error) {
          message.error_message = err.response.data.error.message + ' -- ' + err.response.data.error.error_data?.details
        }

        message.error_data = err.response?.data || { message: 'İstek Atılamadı' }
      } else {
        if (err.response?.data?.errors[0]?.title) {
          message.error_message = err.response.data.errors[0].title + ' -- ' + err.response.data.errors[0].details
        }

        message.error_data = err.response?.data || { message: 'İstek Atılamadı' }
      }

      message.status = enums.message_send_statuses.SENT_FAILED
      await message.save()

      if (chat.owner_user_id) {
        const agent = await User.findById(chat.owner_user_id)

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.MESSAGE_SENT_FAILED,
          socket_rooms: [agent.vSocketCode],
          data: {
            chat_item: DashPresenter.getChatItem(chat, message, channel, undefined, chat.thinker_status, chat.helobot_status),
            message_item: await DashPresenter.getMessageItemCustomerOrAgent(message, user),
            temp_id: data.temp_id,
            notification_sound_status: channel.vSettings.getNotificationSoundStatus()
          }
        }, req.language)
      }

      return res.modifiedResponse(400, {
        response: err.response?.data || { message: message.error_message },
        message: message.error_message
      })
    }

    // mesaj ext_id si bizim tarafımıza kaydediliyor
    message.ext_id = id
    message.status = enums.message_send_statuses.SENT
    await message.save()

    const isCarousel = messageTemplateComponents.find(a => a.type === 'carousel')
    if (!isCarousel) {
      // Butonlu templete mesajları için bot sistemi devam ettirilir.
      const hasButtons = messageTemplateComponents.find(a => a.type === 'buttons' && a.buttons.find(b => b.type === 'quick_reply'))
      if (hasButtons) {
        const messageTemplateActionData = await MessageTemplateAction.findOne({ send_wizard_id: data.send_wizard_id })
        if (messageTemplateActionData) {
          await new MessageTemplateActionMessages({
            message_template_action_id: messageTemplateActionData._id,
            message_id: id
          }).save()
        }
      }
    }

    process.nextTick(async () => {

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'Message Template Soketten Gönderiliyor',
        request: JSON.stringify(req.body)
      })

      const chatItem = DashPresenter.getChatItem(chat, message, channel, undefined, chat.thinker_status, chat.helobot_status)

      if (chat.owner_user_id) {
        const agent = await User.findById(chat.owner_user_id)

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.MESSAGE_SENT,
          socket_rooms: [agent.vSocketCode],
          data: {
            chat_item: chatItem,
            message_item: await DashPresenter.getMessageItemCustomerOrAgent(message, agent),
            temp_id: ''
          }
        }, req.language)
      } else {
        const agent = await User.findById(message.user_id)

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.NEW_PUBLIC_MESSAGE_RECEVIED,
          socket_rooms: [channel.id],
          data: {
            chat_item: chatItem,
            message_item: await DashPresenter.getMessageItemCustomerOrAgent(message, agent),
            temp_id: ''
          }
        }, req.language)
      }

      pino.info({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'Message Template Soketten Gönderildi'
      })
    })

    return res.modifiedResponse(200, { message_id: id })

  } catch (error) {
    pino.error({
      trace_id: req.trace_id,
      timestamp: new Date(),
      message: 'Message Template Mesaj Gönderilemedi',
      request: JSON.stringify(req.body),
      error: JSON.stringify(error.response?.data || { message: error.message })
    })
    return next(error)
  }

}
