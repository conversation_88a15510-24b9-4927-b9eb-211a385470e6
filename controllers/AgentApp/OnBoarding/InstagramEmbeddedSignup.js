const Joi = require('joi')
const pino = require('pino')()
const createError = require('http-errors')

const enums = require('../../../libs/enums')

const User = require('../../../models/User')
const Channel = require('../../../models/Channel')
const Integration = require('../../../models/Integration')
const OnBoardingChannels = require('../../../models/OnBoardingChannels')

const FacebookApiService = require('../../../integrations/Facebook/FacebookApiService')
const InstagramApiService = require('../../../integrations/Instagram/InstagramApiService')
const FacebookService = require('../../../integrations/Facebook/FacebookService')

const ChannelService = require('../../../services/ChannelService')
const SocketService = require('../../../services/SocketService')

module.exports = async (req, res, next) => {

  try {

    const schema = Joi.object({
      page_id: Joi.string().required().error(new Error(req.t('Noc.errors.channel.access_token_not_found'))),
      is_installing: Joi.boolean().default(false)
    })
    await schema.validateAsync(req.body)

    const pageId = req.body.page_id
    const isInstalling = req.body.is_installing

    const stateDto = await req.getState()

    if (isInstalling) {
      const channelAddStatus = await ChannelService.CheckChannelLimit(enums.channel_types.INSTAGRAM_ACCOUNT, stateDto.getCompanyId(), stateDto.getCompany().vData.getCompanyPackageChannelSettings())

      if (!channelAddStatus) {
        throw new createError.NotFound(req.t('Noc.errors.channel.no_right_add_channels'))
      }
    }

    const onBoardingChannel = await OnBoardingChannels.findOne({ company_id: stateDto.getCompanyId() })
    if (!onBoardingChannel) {
      throw new createError.BadRequest('Onboarding channel not found')
    }

    const result = await FacebookApiService.getFacebookPageConnectedInstagram(onBoardingChannel.vInstagramEmbeddedInfo.getLongLivedToken())
    const selectedPage = result.data.find(a => a.id === pageId)
    const hasFacebookPageAndConnectedInstagramAccount = selectedPage && selectedPage.instagram_business_account?.id
    if (!hasFacebookPageAndConnectedInstagramAccount) {
      throw new createError.BadRequest(req.t('Onboarding.errors.instagram_account_not_exist'))
    }

    const tokenDebug = await FacebookApiService.debugToken(onBoardingChannel.vInstagramEmbeddedInfo.getLongLivedToken())

    const { errorMessage } = await FacebookService.validateToken(onBoardingChannel.vInstagramEmbeddedInfo.getLongLivedToken(), tokenDebug, selectedPage.id, req.t, true, selectedPage.instagram_business_account.id)
    if (errorMessage) {
      throw new createError.BadRequest(errorMessage)
    }

    // sayfa sub olunuyor.
    await FacebookApiService.setFacebookPageSubscription(selectedPage.id, selectedPage.access_token)

    const eventDataSet = await InstagramApiService.getEventDataSet(selectedPage.instagram_business_account.id, selectedPage.access_token)

    if (isInstalling) {
      const hasChannel = await Channel.findOne({
        ext_id: selectedPage.instagram_business_account.id,
        deleted_at: {
          $exists: false
        }
      })

      if (hasChannel) {
        throw new createError.BadRequest(req.t('Noc.errors.channel.allready_exist'))
      }

      const agents = await User.find({
        company_id: stateDto.getCompanyId(),
        type: { $in: [enums.acl_roles.COMPANY_OWNER] }
      }, { _id: 1 })

      const integration = await Integration.findOne({ company_id: stateDto.getCompanyId(), deleted_at: { $exists: false } })

      const channel = await new Channel({
        name: selectedPage.instagram_business_account?.username || selectedPage.name,
        type: enums.channel_types.INSTAGRAM_ACCOUNT,
        provider: enums.channel_providers.FACEBOOK,
        company_id: stateDto.getCompanyId(),
        integration_id: integration?._id,
        agents: agents.map(item => item._id),
        is_connection: true,
        is_active: true,
        ext_id: selectedPage.instagram_business_account.id,
        access_token_scopes: tokenDebug.data.scopes,
        settings: {
          access_token: selectedPage.access_token,
          username: selectedPage.instagram_business_account?.username,
          event_data_set: eventDataSet || []
        }
      }).save()

      process.nextTick(async () => {
        for (const agent of agents) {
          await SocketService.joinRoom(agent.socket_id, [channel.id]).catch(err => {
            pino.error({
              trace_id: req.trace_id,
              data: JSON.stringify({
                socket_id: agent.socket_id,
              }),
              message: 'Sokete Ekleme İşleminde Hata Oluştu',
              timestamp: new Date(),
              error: err.message
            })
          })
        }
      })
    } else {
      const foundChannel = await Channel.findOne({ ext_id: selectedPage.instagram_business_account.id, deleted_at: { $exists: false } })
      if (foundChannel) {
        if (foundChannel.company_id.toString() !== stateDto.getCompanyIdAsString()) {
          throw new createError.BadRequest(req.t('Onboarding.errors.instagram_account_not_exist'))
        }
      } else {
        throw new createError.BadRequest(req.t('Onboarding.errors.instagram_account_not_exist'))
      }

      if (foundChannel.ext_id != selectedPage.instagram_business_account.id) {
        throw new createError.BadRequest(req.t('Onboarding.errors.wrong_instagram_account_selected'))
      }

      if (!foundChannel.settings.event_data_set) {
        foundChannel.settings.event_data_set = eventDataSet || []
      }
      foundChannel.access_token_scopes = tokenDebug.data.scopes

      foundChannel.settings.access_token = selectedPage.access_token
      foundChannel.is_connection = true
      foundChannel.markModified('settings')
      await foundChannel.save()
    }

    onBoardingChannel.has_instagram_channel = true
    await onBoardingChannel.save()

    return res.modifiedResponse(200, { success: true })
  } catch (error) {
    next(error)
  }

}
