const Joi = require('joi')

const Company = require('../../../models/Company')

const enums = require("../../../libs/enums");
const helpers = require("../../../libs/helpers")

const UserLogService = require('../../../services/UserLogService')

module.exports = async (req, res, next) => {
  try {

    const schema = Joi.object({
      name: Joi.string().required().error(new Error(req.t('Onboarding.errors.company_name_not_found'))),
      phone_number: Joi.string().required().error(new Error(req.t('Onboarding.errors.phone_not_found'))),
      assign_chat_to_agent: Joi.boolean().required().error(new Error(req.t('App.errors.settings.assign_chat_to_agent_not_found'))),
      hide_phone_number: Joi.boolean().optional().error(new Error(req.t('App.errors.settings.hide_phone_number_not_found'))),
      chat_message_read_status: Joi.boolean().required().error(new Error(req.t('App.errors.settings.chat_message_read_status_not_found'))),
      team_status: Joi.boolean().optional().error(new Error(req.t('App.errors.team.team_status_not_found'))),
      company_owner_get_force_chat: Joi.boolean().optional().error(new Error(req.t('App.errors.settings.company_owner_get_force_chat_invalid'))),
      overtime_status: Joi.boolean().optional().error(new Error(req.t('App.errors.overtimes.status'))),
      report_options: Joi.object({
        daily_report: Joi.boolean().required().error(new Error(req.t('App.errors.settings.daily_report_not_found'))),
        weekly_report: Joi.boolean().required().error(new Error(req.t('App.errors.settings.weekly_report_not_found'))),
        monthly_report: Joi.boolean().required().error(new Error(req.t('App.errors.settings.monthly_report_not_found'))),
      }).optional(),
      payment_information_popup_status_for_users: Joi.boolean().optional(),
    })
    await schema.validateAsync(req.body)

    const stateDto = await req.getState()
    const company = await Company.findById(stateDto.getCompanyId())
    const oldCompany = company.toObject()

    const {
      name,
      phone_number,
      assign_chat_to_agent,
      hide_phone_number,
      chat_message_read_status,
      team_status,
      company_owner_get_force_chat,
      report_options,
      overtime_status,
      payment_information_popup_status_for_users
    } = req.body

    company.name = name
    company.phone_number = phone_number

    const companyData = company.vData

    companyData.setAssignChatToAgent(assign_chat_to_agent)
    companyData.setChatReadStatus(chat_message_read_status)

    if (stateDto.getUserType() === enums.acl_roles.COMPANY_OWNER) {
      companyData.setHidePhoneNumber(hide_phone_number)
      companyData.setTeamStatus(team_status)
      companyData.setCompanyOwnerGetForceChat(company_owner_get_force_chat)
      companyData.setOvertimeStatus(overtime_status)
      companyData.setPaymentInformationPopupStatusForUser(payment_information_popup_status_for_users)

      if (report_options) {
        companyData.setDailyReport(report_options.daily_report)
        companyData.setWeeklyReport(report_options.weekly_report)
        companyData.setMonthlyReport(report_options.monthly_report)
      }
    }

    company.data = companyData.getData()
    company.markModified('data')
    await company.save()

    const logs = await helpers.detectObjectChanges(oldCompany, company.toJSON())
    if (logs) {
      await UserLogService.createLog(company._id, stateDto.getUserId(), enums.user_logs.edit_company, {
        name: helpers.getName(stateDto.getUser())
      }, false, logs)
    }

    return res.modifiedResponse(200, { success: true })
  } catch (error) {
    next(error)
  }
}
