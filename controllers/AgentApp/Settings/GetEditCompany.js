const Company = require('../../../models/Company')
const enums = require('../../../libs/enums')

module.exports = async (req, res, next) => {
  try {

    const stateDto = await req.getState()
    const company = await Company.findById(stateDto.getCompanyId())

    const companyData = company.vData

    let response = {
      name: company.name,
      phone_number: company.phone_number,
      assign_chat_to_agent: companyData.getAssignChatToAgent(),
      chat_message_read_status: companyData.getChatReadStatus()
    }

    if (stateDto.getUserType() === enums.acl_roles.COMPANY_OWNER) {
      response.hide_phone_number = companyData.getHidePhoneNumber()
      response.team_status = companyData.getTeamStatus()
      response.company_owner_get_force_chat = companyData.getCompanyOwnerGetForceChat()
      response.overtime_status = companyData.getOvertimeStatus()
      response.payment_information_popup_status_for_users = companyData.getPaymentInformationPopupStatusForUser()
      response.report_options = {
        daily_report: companyData.getDailyReport(),
        weekly_report: companyData.getWeeklyReport(),
        monthly_report: companyData.getMonthlyReport()
      }
    }

    return res.modifiedResponse(200, { data: response })
  } catch (error) {
    return next(error)
  }
}
