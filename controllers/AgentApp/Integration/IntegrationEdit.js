const createError = require('http-errors')

const enums = require('../../../libs/enums')

const Integration = require('../../../models/Integration')

const TsoftIntegrationEdit = require('../../../integrations/Tsoft/AgentApp/IntegrationEdit')
const HeloscopeIntegrationEdit = require('../../../integrations/Heloscope/AgentApp/IntegrationEdit')
const ShopifyIntegrationEdit = require('../../../integrations/Shopify/AgentApp/IntegrationEdit')

module.exports = async (req, res, next) => {

  try {

    const integrationId = req.params.integrationId

    const stateDto = await req.getState()

    const integration = await Integration.findOne({ _id: integrationId, company_id: stateDto.getCompanyId(), deleted_at: { $exists: false } })
    if (!integration) {
      throw new createError.BadRequest(req.t('Global.errors.integration.not_found'))
    }

    let response = {}

    switch (integration.type) {
      case enums.INTEGRATION_TYPES.TSOFT:
        response = await TsoftIntegrationEdit(req, integration, stateDto.getUserType())
        response.integration_type = enums.INTEGRATION_TYPES.TSOFT
        break

      case enums.INTEGRATION_TYPES.HELOSCOPE:
        response = await HeloscopeIntegrationEdit(req, integration)
        response.integration_type = enums.INTEGRATION_TYPES.HELOSCOPE
        break

      case enums.INTEGRATION_TYPES.SHOPIFY:
        response = await ShopifyIntegrationEdit(req, integration, stateDto.getUserType())
        response.integration_type = enums.INTEGRATION_TYPES.SHOPIFY
        break

      default:
        throw new createError.BadRequest(req.t('App.errors.integration.integration_not_found'))
    }

    if (req.method === 'POST') {
      response = { success: true }
    }

    return res.modifiedResponse(200, response)

  } catch (error) {

    next(error)
  }

}
