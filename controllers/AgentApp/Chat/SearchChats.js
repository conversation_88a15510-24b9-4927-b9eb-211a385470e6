const { Types } = require('mongoose')

const DashPresenter = require('./../../../presenters/Dash')

const Chat = require('./../../../models/Chat')
const ChatHasTags = require('./../../../models/ChatHasTags')
const ElasticsearchService = require('../../../services/ElasticsearchService')

module.exports = async (req, res, next) => {
  try {

    const query = req.body.query || ''

    const stateDto = await req.getState()

    let mongoQuery = {}
    if (process.env.PRODUCTION === 'false') {
      mongoQuery = {
        channel_id: {
          $in: stateDto.getPermittedChannelIds()
        },
        $or: [
          {
            title: new RegExp(query, 'i'),
          },
          {
            ext_id: new RegExp(query, 'i'),
          },
          {
            username: new RegExp(query, 'i')
          }
        ]
      }
    } else {
      const filterChats = await ElasticsearchService.filterChats(0, 100, query, stateDto.getPermittedChannelIdsAsString()).catch(() => false)
      if (filterChats === false || filterChats.length === 0) {
        return res.modifiedResponse(200, { chats: [] })
      }

      mongoQuery = {
        _id: {
          $in: filterChats.map(a => new Types.ObjectId(a._source.id))
        }
      }
    }

    const chats = await Chat.aggregate([
      {
        $match: mongoQuery
      },
      {
        $limit: 100
      },
      {
        $lookup: {
          from: 'messages',
          localField: 'last_message_id',
          foreignField: '_id',
          as: 'lastMessage'
        }
      },
      {
        $unwind: {
          path: "$lastMessage",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'channels',
          localField: 'channel_id',
          foreignField: '_id',
          as: 'channel'
        }
      },
      {
        $unwind: {
          path: "$channel",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'owner_user_id',
          foreignField: '_id',
          as: 'owner'
        }
      },
      {
        $unwind: {
          path: "$owner",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'chat_integrations',
          localField: '_id',
          foreignField: 'chat_id',
          as: 'chatIntegration'
        }
      },
      {
        $unwind: {
          path: "$chatIntegration",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $lookup: {
          from: 'teams',
          localField: 'team_id',
          foreignField: '_id',
          as: 'team'
        }
      },
      {
        $unwind: {
          path: "$team",
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $group: {
          _id: '$_id',
          title: { $first: '$title' },
          is_blocked: { $first: '$is_blocked' },
          chat: {
            $first: {
              id: '$_id',
              title: '$title',
              owner_user_id: '$owner_user_id',
              update_at: '$updated_at',
              is_blocked: '$is_blocked',
              sort_field: '$sort_field',
              unread_message_count: '$unread_message_count',
              thinker_status: '$thinker_status',
              helobot_status: '$helobot_status',
              profile_pic: '$profile_pic',
              chat_referral_id: '$chat_referral_id',
              customer_last_message_time: '$customer_last_message_time',
              team: {
                id: '$team_id',
                name: '$team.name'
              }
            }
          },
          owner: { $first: '$owner' },
          channel: { $first: '$channel' },
          last_message: { $first: '$lastMessage' },
          chat_integration: { $first: '$chatIntegration' }
        }
      },
      {
        $sort: {
          'chat.sort_field': -1
        }
      }
    ])

    const chatTags = await ChatHasTags.find({ chat_id: { $in: chats.map(a => a.chat._id) } }).populate('tag_id')

    const chatsData = []
    for (const item of chats) {
      // Frontende gönderilecek datanın formatı hazırlanıyor
      let chat = DashPresenter.getChatItem(item.chat, item.last_message, item.channel, undefined, item.chat.thinker_status, item.chat.helobot_status)

      // Üyeliği var mı diye kontrol ediliyor
      chat.is_paired = !!item.chat_integration?.ext_id
      chat.owner_user = {}

      if (item.owner) {
        chat.owner_user = {
          id: item.owner._id.toString(),
          name: item.owner.name
        }
      }

      const tags = chatTags.filter(a => a.chat_id.toString() === item.chat._id.toString())

      chat.tags = tags.map(a => {
        return {
          id: a.tag_id.id,
          color: a.tag_id.color,
          name: a.tag_id.name
        }
      })

      chatsData.push(chat)
    }

    return res.modifiedResponse(200, { chats: chatsData })
  } catch (error) {
    next(error)
  }
}
