const createError = require('http-errors')

const enums = require('../../../libs/enums')
const helpers = require('../../../libs/helpers')

const ChatService = require('../../../services/ChatService')
const ChatActions = require('../../../services/ChatActions')

const AdminShopifyService = require('../../../integrations/Shopify/AdminShopifyService')

module.exports = async (req, res, next) => {
  try {
    const chatId = req.body.chat_id
    const discountCode = req.body.discount_code

    const stateDto = await req.getState()

    const result = await helpers.GetChatPopulate(req, chatId)

    const sendable = await ChatService.checkSendable(result.getChatId(), result.getChannelType())

    if (!sendable.sendable) {
      throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
    }

    if (!result.getChatIntegration().ext_id) {
      throw new createError.BadRequest(req.t('App.errors.campaign.not_member'))
    }

    //Kullanıcının Kampanya oluşturma yetkisi var mı kontrol edilir.
    if (stateDto.getUserType() !== enums.acl_roles.COMPANY_OWNER && stateDto.getUserType() !== enums.acl_roles.SERVICE_ACCOUNT) {
      if (!result.getIntegration().vData.getManuelDiscountPermissionAll() && stateDto.getUserType() === enums.acl_roles.COMPANY_USER) {
        throw new createError.Unauthorized(req.t('Global.errors.not_permissions'))
      }
    }

    await AdminShopifyService.SetDiscountCode(req, result.getIntegration(), result.getChatIntegration(), discountCode)

    await ChatActions(req, enums.chat_actions.campaign_code_applied, { name: stateDto.getUserName(), coupone_code: discountCode }, chatId, [stateDto.getUser().vSocketCode], stateDto.getTester())

    return res.modifiedResponse(200, { success: true })
  } catch (error) {
    next(error)
  }
}
