const createError = require('http-errors')

const enums = require('../../libs/enums')

const Chat = require('../../models/Chat')
const Company = require('../../models/Company')
const Integration = require('../../models/Integration')
const ChatIntegration = require('../../models/ChatIntegration')

const QueueService = require('../../services/QueueService')
const AdminShopifyService = require('../../integrations/Shopify/AdminShopifyService')

module.exports = (req, res, next) => {

  const chatId = req.params.chatId

  return req.getState().then(async stateDto => {

    const company = await Company.findById(stateDto.getCompanyId())
    const chat = await Chat.findById(chatId).populate('channel_id')

    if (!chat.owner_user_id) {
      throw new createError.Forbidden(req.t('App.errors.conversation.does_not_belongs_to_you'))
    }

    if (chat.owner_user_id && chat.owner_user_id.toString() !== stateDto.getUserId().toString()) {
      throw new createError.Forbidden(req.t('App.errors.conversation.does_not_belongs_to_you'))
    }

    let extId
    let integration
    let chatIntegration

    if (chat.channel.integration_id) {
      integration = await Integration.findOne({ _id: chat.channel.integration_id, deleted_at: { $exists: false } })
      chatIntegration = await ChatIntegration.findOne({ chat_id: chat._id, integration_id: chat.channel.integration_id })
      if (chatIntegration?.ext_id) {
        extId = chatIntegration.ext_id
      }
    }

    const data = req.body.data

    if (data.email) {
      data.email = data.email?.toLowerCase()
    }

    if (!extId && !integration?.vData?.getPhoneNumberRequired()) {
      if (data.email) {
        chat.email = data.email
      }
    }

    if (!data.first_name?.includes('undefined') && !data.last_name?.includes('undefined')) {
      let changed = false
      if (data.first_name) {
        chat.first_name = data.first_name
        chat.title = data.first_name

        changed = true
      }

      if (data.last_name) {
        chat.last_name = data.last_name
        chat.title = chat.title + ' ' + data.last_name

        changed = true
      }

      if (changed) {
        await QueueService.publishToFifoChat({
          update: true,
          id: chat.id,
          data: {
            title: chat.title,
            ext_id: chat.ext_id,
            username: chat.username,
            channel_id: chat.channel.id
          }
        })
      }
    }

    if (data.customer_note?.length > 250) {
      throw new createError.BadRequest(req.t('App.errors.integration.note_field_length'))
    }

    chat.email = data.email || ''

    let phoneNumber

    switch (chat.channel.type) {

      case enums.channel_types.WHATSAPP_NUMBER:
        phoneNumber = chat.ext_id
        break

      default:
        phoneNumber = data.phone_number
        break

    }

    if (company.vData.getHidePhoneNumber()) {
      if (stateDto.getUserType() !== enums.acl_roles.COMPANY_OWNER) {
        phoneNumber = ""
      }
    }

    let numberRegex = new RegExp(/^[0-9]*$/g)

    if (numberRegex.test(phoneNumber)) {
      chat.phone_number = phoneNumber
    }

    if (chat.note !== data.customer_note) {
      if (data.customer_note?.length > 250) {
        throw new createError.BadRequest(req.t('App.errors.integration.note_field_length'))
      }

      chat.note = data.customer_note
    }

    await chat.save()

    let emailStatus = false

    if (integration) {
      emailStatus = integration.vData.getPhoneNumberRequired()
    }

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.UPDATE_USER_PROFILE,
      socket_rooms: [stateDto.getUser().vSocketCode],
      data: {
        chat_id: chat.id,
        user_data: {
          first_name: chat.first_name,
          last_name: chat.last_name,
          name: chat.title,
          email: emailStatus ? undefined : chat.email,
          phone_number: chat.phone_number || ''
        }
      }
    }, req.language)

    await QueueService.publishToLiveChatSocket({
      event: enums.agent_app_socket_events.UPDATE_USER_PROFILE,
      socket_rooms: [chat.ext_id],
      data: {
        chat_id: chat.id,
        user_data: {
          first_name: chat.first_name,
          last_name: chat.last_name,
          name: chat.title,
          email: emailStatus ? undefined : chat.email,
          phone_number: chat.phone_number || ''
        }
      }
    }, req.language)

    if (integration?.type === enums.INTEGRATION_TYPES.SHOPIFY) {
      if (!extId) {
        await AdminShopifyService.CreateCustomer(req, chat, chatIntegration, integration, data.customer_note)
      } else {
        const customer = await AdminShopifyService.GetCustomer(req, chatIntegration, integration)
        if (customer.data.email !== chat.email) {
          chat.email = customer.data.email
          await chat.save()
        }

        await AdminShopifyService.UpdateCustomer(req, chat, chatIntegration, integration, data.customer_note)
      }
    }

    return res.modifiedResponse(200, { success: true })
  }).catch(next)

}
