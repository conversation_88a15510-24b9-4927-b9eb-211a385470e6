const mongoose = require('mongoose')
const createError = require('http-errors')

const enums = require('../../../libs/enums')

const Message = require('../../../models/Message')
const AgentMessageReport = require('../../../models/AgentMessageReport')

const DashPresenter = require('./../../../presenters/Dash')

module.exports = async (req, res, next) => {
  try {

    const stateDto = await req.getState()

    if (stateDto.getUserType() !== enums.acl_roles.COMPANY_OWNER && stateDto.getUserType() !== enums.acl_roles.SERVICE_ACCOUNT) {
      throw new createError.NotFound(req.t('Site.errors.user.login.not_permitted'))
    }

    const agentId = req.body.agent_id
    const chatId = req.body.chat_id
    const pagination = req.body.pagination

    if (!pagination) {
      throw new createError.BadRequest(req.t('Global.errors.pagination_not_found'))
    }

    const hasMessage = await Message.findOne({
      conversation_id: new mongoose.Types.ObjectId(chatId),
      user_id: new mongoose.Types.ObjectId(agentId)
    })

    const search = { conversation_id: new mongoose.Types.ObjectId(chatId) }
    const getMessages = await Message.find(search)
      .populate('user_id')
      .populate('conversation_id')
      .sort({ created_at: -1 })
      .skip((pagination.page - 1) * pagination.perpage)
      .limit(pagination.perpage)
    const totalMessages = await Message.countDocuments(search)
    const formattedMessages = await DashPresenter.getMessageWithReply(getMessages)

    return res.modifiedResponse(200, {
      message: !hasMessage ? req.t('App.errors.message.agent_only_view') : undefined,
      messages: formattedMessages,
      pagination: {
        page: pagination.page,
        perpage: pagination.perpage,
        total: totalMessages,
        has_next_page: pagination.page * pagination.perpage < totalMessages ? true : false
      }
    })

  } catch (error) {
    next(error)
  }
}