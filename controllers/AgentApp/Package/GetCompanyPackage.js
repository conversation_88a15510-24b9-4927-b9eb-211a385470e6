const createError = require('http-errors')
const moment = require('moment')

const enums = require('../../../libs/enums')

const Package = require('../../../models/Package')
const CompanyHasPackage = require('../../../models/CompanyHasPackage')

const BillTekromService = require('../../../services/BillTekromService')

module.exports = async (req, res, next) => {
  try {
    const stateDto = await req.getState()

    const query = {
      company_id: stateDto.getCompanyId(),
      deleted_at: { $exists: false }
    }

    const package = await CompanyHasPackage.findOne(query).sort({ _id: -1 })
    if (package) {
      const serviceData = await BillTekromService.GetActivePackage(stateDto.getBilltekromAccountId(), package).catch(() => false)

      if (serviceData !== false) {
        const isPackage = await Package.findOne({
          billtekrom_package_id: package.billtekrom_package_id,
          is_active: true,
          deleted_at: { $exists: false }
        })
        if (!isPackage) {
          throw new createError.NotFound(req.t('Onboarding.errors.package_not_found'))
        }

        if (Object.keys(serviceData.service).length === 0) {
          return res.modifiedResponse(200, {
            billtekrom: stateDto.isBilltekromAccountId(),
            shopify: stateDto.isShopifyCustomer(),
            data: package
          })
        }

        package.name = isPackage.name
        package.description = isPackage.description
        package.billtekrom_package_id = serviceData.service.package_id
        package.started_date = moment(serviceData.service.start_time).utc(true).toDate()
        package.data.billtekrom_service_id = serviceData.service.id
        package.expiry_date = moment(serviceData.service.end_time).utc(true).toDate()
        package.price = serviceData.service.price
        package.currency = serviceData.service.currency === 'USD' ? '$' : '₺'
        package.is_demo = serviceData.service.is_demo
        package.markModified('data')
        await package.save()
      }

      package.id = package._id.toString()

      delete package._id
    }

    return res.modifiedResponse(200, {
      billtekrom: stateDto.isBilltekromAccountId(),
      shopify: stateDto.isShopifyCustomer(),
      data: package || null,
      company_data: {
        payment_information_popup_status_for_users: stateDto.getUserType() === enums.acl_roles.COMPANY_OWNER ? true : stateDto.getCompany().vData.getPaymentInformationPopupStatusForUser()
      }
    })
  } catch
  (error) {
    next(error)
  }
}
