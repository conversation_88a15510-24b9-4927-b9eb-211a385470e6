const createError = require('http-errors')

class InstagramPostAdcreativeDto {

	name
	objectId
	instagramUserId
	sourceInstagramMediaId

	callToAction
	assetFeedSpec
	pageWelcomeMessage

	constructor({name, objectId, source_instagram_media_id, call_to_action, asset_feed_spec, page_welcome_message}) {
		this.name = name
		this.objectId = objectId
		this.sourceInstagramMediaId = source_instagram_media_id
		this.callToAction = call_to_action
		this.assetFeedSpec = asset_feed_spec
		this.pageWelcomeMessage = page_welcome_message
	}

	setObjectId(pageId) {
		this.objectId = pageId
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	validate(t) {
		if ( ! this.name) {
			throw new createError.BadRequest(t('App.errors.meta_ads.name_not_found'))
		}
		if ( ! this.sourceInstagramMediaId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.source_instagram_media_id_not_found'))
		}
	}

	toJson() {
		const json = {
			name: this.name,
			object_id: this.objectId,
			instagram_user_id: this.instagramUserId,
			source_instagram_media_id: this.sourceInstagramMediaId,
			call_to_action: this.callToAction,
			asset_feed_spec: this.assetFeedSpec
			// call_to_action: {
			// 	type: 'INSTAGRAM_MESSAGE',
			// 	value: {
			// 		link: 'https://www.helorobo.com'
			// 	}
			// }
		}

		if(this.pageWelcomeMessage){
			json.page_welcome_message = this.pageWelcomeMessage
		}
		return json
	}

}


module.exports = InstagramPostAdcreativeDto
