// Bu Facebook postunu isntagram ads olarak kullanmak için.
const createError = require('http-errors')

class VideoAdcreativeDto {

	videoId
	message
	imageUrl

	objectId
	instagramUserId

	callToAction
	assetFeedSpec
	pageWelcomeMessage

	constructor({video_id, message, image_url, call_to_action, asset_feed_spec, page_welcome_message}) {
		this.videoId = video_id
		this.imageUrl = image_url
		this.message = message
		this.callToAction = call_to_action
		this.assetFeedSpec = asset_feed_spec
		this.pageWelcomeMessage = page_welcome_message
	}

	validate(t) {
		if ( ! this.objectId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.object_story_id_not_found'))
		}
		if ( ! this.videoId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.vide_id_not_found'))
		}
		if ( ! this.imageUrl) {
			throw new createError.BadRequest(t('App.errors.meta_ads.image_url_not_found'))
		}
		// if ( ! this.instagramUserId) {
		// 	throw new createError.BadRequest(t('App.errors.meta_ads.instagram_user_id_not_found'))
		// }
	}

	setObjectId(objectId) {
		this.objectId = objectId
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	toJson() {
		const json = {
			name: 'videoAdcreative',
			object_story_spec: {
				page_id: this.objectId,
				video_data: {
					// link: "https://www.helorobo.com",
					// page_welcome_message: // sonradan gelcek
					image_url: this.imageUrl,
					message: this.message,
					video_id: this.videoId,
					call_to_action: this.callToAction
				}
			},
			asset_feed_spec: this.assetFeedSpec
		}
		if (this.instagramUserId) {
			json.object_story_spec.instagram_user_id = this.instagramUserId
		}
		if (this.pageWelcomeMessage) {
			json.object_story_spec.video_data.page_welcome_message = this.pageWelcomeMessage
		}
		return json
	}

}

// json.object_story_spec.link_data.call_to_action = {
// 	type: 'INSTAGRAM_MESSAGE',
// 	value: {
// 		app_destination: 'INSTAGRAM_DIRECT'
// 	}
// }

// json.object_story_spec.link_data.call_to_action = {
// 	type:'MESSAGE_PAGE',
// 	value: {
// 		app_destination: 'MESSENGER'
// 	}
// }

// json.object_story_spec.link_data.call_to_action = {
// 	type: 'WHATSAPP_MESSAGE',
// 	value: {
// 		app_destination: 'WHATSAPP'
// 	}
// }
// json.asset_feed_spec = {
// 	optimization_type: 'DOF_MESSAGING_DESTINATION',
// 	call_to_actions: [
// 		{
// 			type: 'MESSAGE_PAGE',
// 			value: {
// 				app_destination: 'MESSENGER',
// 				link: 'https://fb.com/messenger_doc/'
// 			}
// 		},
// 		{
// 			type: 'WHATSAPP_MESSAGE',
// 			value: {
// 				app_destination: 'WHATSAPP',
// 				link: 'https://api.whatsapp.com/send'
// 			}
// 		},
// 		{
// 			type: 'INSTAGRAM_MESSAGE',
// 			value: {
// 				app_destination: 'INSTAGRAM_DIRECT',
// 				link: 'https://www.instagram.com'
// 			}
// 		}
// 	]
// }


module.exports = VideoAdcreativeDto
