// Bu Facebook postunu isntagram ads olarak kullanmak için.
const createError = require('http-errors')

class ImageAdcreativeDto {

	name
	imageHash
	message
	caption
	description

	objectId
	instagramUserId

	callToAction
	assetFeedSpec
	pageWelcomeMessage

	constructor({
		            name,
		            image_hash,
		            message,
		            caption,
		            description,
		            call_to_action,
		            asset_feed_spec,
		            page_welcome_message
	            }) {
		this.name = name
		this.imageHash = image_hash
		this.message = message
		this.caption = caption
		this.description = description
		this.callToAction = call_to_action
		this.assetFeedSpec = asset_feed_spec
	}

	validate(t) {
		if ( ! this.name) {
			throw new createError.BadRequest(t('App.errors.meta_ads.name_not_found'))
		}
		if ( ! this.objectId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.object_story_id_not_found'))
		}
		if ( ! this.imageHash) {
			throw new createError.BadRequest(t('App.errors.meta_ads.image_hash_not_found'))
		}
		// if ( ! this.instagramUserId) {
		// 	throw new createError.BadRequest(t('App.errors.meta_ads.instagram_user_id_not_found'))
		// }
	}

	setObjectId(objectId) {
		this.objectId = objectId
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	toJson() {
		const json = {
			name: this.name,
			object_story_spec: {
				page_id: this.objectId,
				link_data: {
					image_hash: this.imageHash,
					name: this.name,
					message: this.message,					// corausel için zorunlu.
					// caption:this.caption, // değerlendirilecek
					// description: // değerlendirilecek
					// page_welcome_message: // sonradan gelcek
					// link: 'https://www.helorobo.com',
					call_to_action: this.callToAction
				}
			},
			asset_feed_spec: this.assetFeedSpec
		}
		if (this.instagramUserId) {
			json.object_story_spec.instagram_user_id = this.instagramUserId
		}
		if (this.pageWelcomeMessage) {
			json.object_story_spec.link_data.page_welcome_message = this.pageWelcomeMessage
		}
		return json
	}

}

// json.object_story_spec.link_data.call_to_action = {
// 	type: 'INSTAGRAM_MESSAGE',
// 	value: {
// 		app_destination: 'INSTAGRAM_DIRECT'
// 	}
// }

// json.object_story_spec.link_data.call_to_action = {
// 	type:'MESSAGE_PAGE',
// 	value: {
// 		app_destination: 'MESSENGER'
// 	}
// }

// json.object_story_spec.link_data.call_to_action = {
// 	type: 'WHATSAPP_MESSAGE',
// 	value: {
// 		app_destination: 'WHATSAPP'
// 	}
// }
// json.asset_feed_spec = {
// 	optimization_type: 'DOF_MESSAGING_DESTINATION',
// 	call_to_actions: [
// 		{
// 			type: 'MESSAGE_PAGE',
// 			value: {
// 				app_destination: 'MESSENGER',
// 				link: 'https://fb.com/messenger_doc/'
// 			}
// 		},
// 		{
// 			type: 'WHATSAPP_MESSAGE',
// 			value: {
// 				app_destination: 'WHATSAPP',
// 				link: 'https://api.whatsapp.com/send'
// 			}
// 		},
// 		{
// 			type: 'INSTAGRAM_MESSAGE',
// 			value: {
// 				app_destination: 'INSTAGRAM_DIRECT',
// 				link: 'https://www.instagram.com'
// 			}
// 		}
// 	]
// }


module.exports = ImageAdcreativeDto
