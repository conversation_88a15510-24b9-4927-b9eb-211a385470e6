// Bu Facebook postunu isntagram ads olarak kullanmak için.
const createError = require('http-errors')

class FacebookPostAdcreativeDto {

	name
	objectStoryId
	instagramUserId

	callToAction
	assetFeedSpec
	pageWelcomeMessage

	constructor({name, call_to_action, asset_feed_spec, object_story_id, page_welcome_message}) {
		this.name = name
		this.callToAction = call_to_action
		this.assetFeedSpec = asset_feed_spec
		this.objectStoryId = object_story_id
		this.pageWelcomeMessage = page_welcome_message
	}

	setInstagramUserId(instagramUserId) {
		this.instagramUserId = instagramUserId
	}

	validate(t) {
		if ( ! this.name) {
			throw new createError.BadRequest(t('App.errors.meta_ads.name_not_found'))
		}
		if ( ! this.objectStoryId) {
			throw new createError.BadRequest(t('App.errors.meta_ads.object_story_id_not_found'))
		}
	}

	toJson() {
		const json = {
			name: this.name,
			object_story_id: this.objectStoryId,
			instagram_user_id: this.instagramUserId,
			call_to_action: this.callToAction,
			asset_feed_spec: this.assetFeedSpec
		}
		if (this.pageWelcomeMessage) {
			json.page_welcome_message = this.pageWelcomeMessage
		}
		return json

	}

}


module.exports = FacebookPostAdcreativeDto
