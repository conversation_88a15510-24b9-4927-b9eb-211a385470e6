const MetaAdsChildBms = require( '../../../models/MetaAdsChildBms')

module.exports = async (req, res, next) => {
	try {

		const stateDto = await req.getState()

		const childBms = await MetaAdsChildBms.find({company_id: stateDto.getCompanyId(),deleted_at: {$exists: false}})

		if (childBms.length === 0) {
			return res.modifiedResponse(200, {success: false, child_bms: []})
		}
		childBms.forEach(item => { // for security purposes
			delete item._doc.system_user_access_token
		})


		return res.modifiedResponse(200, {success: true, child_bms: childBms})
	} catch (error) {
		next(error)
	}
}
