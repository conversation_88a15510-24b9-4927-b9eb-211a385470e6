const InstagramPostAdCreative = require('../Dtos/InstagramPostAdcreativeDto')
const FacebookPostAdCreative = require('../Dtos/FacebookPostAdcreativeDto')
const ImageAdcreativeDto = require('../Dtos/ImageAdcreativeDto')
const VideoAdcreativeDto = require('../Dtos/VideoAdcreativeDto')

class AdCreativeFactory {
	static create(type, data, metaAdsChildBm) {
		const creators = {
			'INSTAGRAM_POST': () => AdCreativeFactory.createInstagramPost(data, metaAdsChildBm),
			'FACEBOOK_POST': () => AdCreativeFactory.createFacebookPost(data, metaAdsChildBm),
			'IMAGE_ADCREATIVE': () => AdCreativeFactory.createImageAdcreative(data, metaAdsChildBm),
			'VIDEO_ADCREATIVE': () => AdCreativeFactory.createVideoAdcreative(data, metaAdsChildBm)
			// 'VIDEO_AD': () => this.createVideoAd(data, metaAdsChildBm),
			// 'CAROUSEL_AD': () => this.createCarouselAd(data, metaAdsChildBm)
		}

		if ( ! creators[type]) {
			throw new Error(`Unsupported AdCreative type: ${type}`)
		}

		return creators[type]()
	}

	static createInstagramPost(data, metaAdsChildBm) {
		const adCreative = new InstagramPostAdCreative(data)
		adCreative.setObjectId(metaAdsChildBm.primary_page_id)
		adCreative.setInstagramUserId(metaAdsChildBm.connected_instagram_account_id)
		return adCreative
	}

	static createFacebookPost(data, metaAdsChildBm) {
		const adCreative = new FacebookPostAdCreative(data)
		adCreative.setInstagramUserId(metaAdsChildBm.connected_instagram_account_id)
		return adCreative
	}

	static createImageAdcreative(data, metaAdsChildBm) {
		const adCreative = new ImageAdcreativeDto(data)
		adCreative.setObjectId(metaAdsChildBm.primary_page_id)
		adCreative.setInstagramUserId(metaAdsChildBm.connected_instagram_account_id)

		return adCreative
	}

	static createVideoAdcreative(data, metaAdsChildBm) {
		const adCreative = new VideoAdcreativeDto(data)
		adCreative.setObjectId(metaAdsChildBm.primary_page_id)
		adCreative.setInstagramUserId(metaAdsChildBm.connected_instagram_account_id)

		return adCreative
	}
}

module.exports = AdCreativeFactory
