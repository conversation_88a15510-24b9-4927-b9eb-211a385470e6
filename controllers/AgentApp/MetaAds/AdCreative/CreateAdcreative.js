const createError = require('http-errors')

const MetaAdsChildBms = require('../../../../models/MetaAdsChildBms')

const FacebookAdsApiService = require('../../../../integrations/Facebook/FacebookAdsApiService')
const AdCreativeFactory = require('../Factories/AdCreativeFactory')


module.exports = async (req, res, next) => {
	try {

		const stateDto = await req.getState()

		const childBmId = req.params.childBmId

		const metaAdsChildBm = await MetaAdsChildBms.findOne({
			_id: childBmId,
			company_id: stateDto.getCompanyId(),
			deleted_at: {$exists: false}
		}).populate('meta_ads_user_id')

		if ( ! metaAdsChildBm) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_not_found'))
		}

		const adcreativeInstance = AdCreativeFactory.create(req.body.adcreative_type, req.body, metaAdsChildBm)
		adcreativeInstance.validate(req.t)

		const adcreativesResponse = await FacebookAdsApiService.createAdcreative(metaAdsChildBm.ad_account_id, metaAdsChildBm.system_user_access_token,adcreativeInstance)

		return res.modifiedResponse(200, {
			success: true,
			adcreative_id: adcreativesResponse.data.id
		})

	} catch (error) {
		next(error)
	}
}
