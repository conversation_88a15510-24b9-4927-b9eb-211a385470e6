const createError = require('http-errors')

const MetaAdsChildBms = require('../../../../models/MetaAdsChildBms')

const FacebookAdsService = require('../../../../integrations/Facebook/FacebookAdsService')
const FacebookAdsApiService = require('../../../../integrations/Facebook/FacebookAdsApiService')

module.exports = async (req, res, next) => {
	try {

		const stateDto = await req.getState()

		const childBmId = req.params.childBmId

		const metaAdsChildBm = await MetaAdsChildBms.findOne({
			_id: childBmId,
			company_id: stateDto.getCompanyId(),
			deleted_at: {$exists: false}
		})

		if ( ! metaAdsChildBm) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.child_bm_not_found'))
		}


		const instagramPostsResponse = await FacebookAdsApiService.getInstagramPosts(metaAdsChildBm.connected_instagram_account_id, metaAdsChildBm.primary_page_access_token)

		return res.modifiedResponse(200, {
			success: true,
			instagram_posts: instagramPostsResponse.data.data
		})

	} catch (error) {
		next(error)
	}
}
