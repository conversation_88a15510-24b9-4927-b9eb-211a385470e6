const createError = require('http-errors')

const MetaAdsUsers = require('../../../models/MetaAdsUsers')
const FacebookAdsApiService = require('../../../integrations/Facebook/FacebookAdsApiService')

module.exports = async (req, res, next) => {
	try {

		const stateDto = await req.getState()

		let metaAdsUser = await MetaAdsUsers.findOne({
			company_id: stateDto.getCompanyId()
		})

		//create or update(replace).
		if ( ! metaAdsUser) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.user_not_found'))
		}

		const params = req.body.params
		if ( ! params) {
			throw new createError.BadRequest(req.t('App.errors.meta_ads.params_not_found'))
		}

		const searchResponse = await FacebookAdsApiService.search(params,metaAdsUser.user_access_token)

		return res.modifiedResponse(200, {
			success: true,
			data: searchResponse.data.data
		})
	} catch (error) {
		next(error)
	}
}
