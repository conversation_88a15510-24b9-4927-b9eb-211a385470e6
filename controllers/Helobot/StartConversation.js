const Joi = require('joi')
const createError = require('http-errors')

const HelobotAccount = require('../../models/HelobotAccount')
const HelobotKnowledgeBase = require('../../models/HelobotKnowledgebase')

const HelobotService = require('../../services/HelobotService')

module.exports = async (req, res, next) => {
  try {

    const schema = Joi.object({
      question: Joi.string().required().error(new Error(req.t("App.errors.helobot.question_not_found"))),
      kb_id: Joi.string().required().error(new Error(req.t("App.errors.helobot.conversation_id_not_found"))),
    })

    await schema.validateAsync(req.body)

    const stateDto = await req.getState()

    const { question, kb_id } = req.body

    const account = await HelobotAccount.findOne({ company_id: stateDto.getCompanyId() })

    if (!account) {
      throw new createError.BadRequest(req.t('App.errors.helobot.account_not_found'))
    }

    const isHasKnowledgebase = await HelobotKnowledgeBase.findOne({ helobot_account_id: account._id, knowledge_base_id: kb_id })
    if (!isHasKnowledgebase) {
      throw new createError.NotFound(req.t('App.errors.helobot.kb_not_found'))
    }

    const newChat = await HelobotService.ConversationCreate(account, kb_id)

    const serviceResponse = await HelobotService.ConversationStart(account, true, newChat._id, question, isHasKnowledgebase.platform)
    res.setHeader('Cache-Control', 'no-cache')
    res.setHeader('Content-Type', 'text/event-stream; charset=UTF-8')
    res.setHeader('X-Accel-Buffering', 'no')
    res.setHeader('Transfer-Encoding', 'chunked')
    res.flushHeaders()

    serviceResponse.data.on('data', (data) => {
      res.write(data.toString())
      // res.uncork()
    })

    serviceResponse.data.on('end', () => {
      res.end()
    })
  } catch (error) {
    next(error)
  }
} 