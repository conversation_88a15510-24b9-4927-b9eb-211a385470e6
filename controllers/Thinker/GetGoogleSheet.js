const createError = require("http-errors");

const Thinker = require('../../models/Thinker')
const ThinkerService = require("../../services/ThinkerService")

module.exports = async (req, res, next) => {
  try {

    const stateDto = await req.getState()

    const thinker = await Thinker.findOne({ company_id: stateDto.getCompanyId(), status: true })
    if (!thinker) {
      throw new createError.NotFound(req.t('Thinker.errors.thinker_not_found'))
    }

    const response = await ThinkerService.GetGoogleSheetList(thinker, req.body, stateDto.getUser().vData.getAppLangCode(), req.trace_id)

    return res.modifiedResponse(200, {
      success: true,
      items: response.items
    })
  } catch (e) {
    next(e)
  }
}
