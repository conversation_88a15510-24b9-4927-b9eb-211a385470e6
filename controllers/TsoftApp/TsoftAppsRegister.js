const Joi = require('joi')
const faker = require('faker')
const moment = require('moment')
const bcrypt = require('bcrypt')
const mongoose = require('mongoose')
const pino = require('pino')()
const { RandomHash } = require('random-hash')

const enums = require('../../libs/enums')
const utils = require('../../libs/utils')
const helpers = require('../../libs/helpers')

const Company = require('../../models/Company')
const Channel = require('../../models/Channel')
const Package = require('../../models/Package')
const Integration = require('../../models/Integration')
const CompanyHasPackage = require('../../models/CompanyHasPackage')
const User = require('../../models/User')
const LoginWith = require('../../models/LoginWith')
const Config = require('../../models/Config')
const ServiceAccount = require('../../models/ServiceAccount')
const LivechatScript = require('../../models/LivechatScript')

const MailerService = require('../../services/Mailer')
const UploadService = require('../../services/UploadService')

const TsoftService = require('../../integrations/Tsoft/AgentApp/TsoftService')

async function createScriptCode(companyId, channelId) {
  const data = helpers.getDefaultLiveChatCodeForShopify(channelId)

  const url = await UploadService.uploadFileFromPresignedUrl(faker.random.uuid(), data, 'application/javascript')

  const script = await new LivechatScript({
    company_id: companyId,
    channel_id: new mongoose.Types.ObjectId(channelId),
    data: data,
    url: url
  }).save()

  return script
}

async function HaveCompany(company, ws_username, ws_password) {
  // integration web servis bilgileri burada tekrardan güncellenicek.
  const integration = await Integration.findOne({ company_id: company._id, deleted_at: { $exists: false } })
  if (integration) {
    const integrationData1 = integration.vData
    integrationData1.setUserName(ws_username)
    integrationData1.setPassword(helpers.encrypt(process.env.APP_SECRET_KEY, ws_username, ws_password))

    integration.data = integrationData1.getData()
    integration.markModified('data')
    await integration.save()
  }

  const hasLivechat = await Channel.find({
    company_id: company._id,
    type: enums.channel_types.LIVE_CHAT,
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: -1 })

  let channelId = ''
  if (hasLivechat.length > 0) {
    channelId = hasLivechat[0].id
  }

  if (!channelId) {
    const users = await User.find({
      company_id: company._id,
      type: {
        $in: [enums.acl_roles.COMPANY_OWNER, enums.acl_roles.COMPANY_USER]
      },
      tester: { $ne: true },
      deleted_at: { $exists: false },
    })

    const channel = await new Channel({
      type: enums.channel_types.LIVE_CHAT,
      provider: enums.channel_providers.LIVE_CHAT,
      name: enums.channel_types.LIVE_CHAT,
      company_id: company._id,
      integration_id: integration?._id,
      is_active: true,
      agents: users.map(a => a._id)
    }).save()

    channelId = channel.id
  }

  let script = await LivechatScript.findOne({
    company_id: company._id,
    channel_id: new mongoose.Types.ObjectId(channelId),
    deleted_at: {
      $exists: false
    }
  }).sort({ _id: -1 })

  if (!script) {
    script = await createScriptCode(company._id, channelId)
  }

  return {
    channelId: channelId,
    script_url: script.url
  }
}

async function createServiceAccountUser(company) {
  const hasUser = await ServiceAccount.findOne({
    company_id: company._id,
    tsoft_app: true,
  })
  if (!hasUser) {
    const serviceAccountUser = await new User({
      company_id: company._id,
      type: enums.acl_roles.SERVICE_ACCOUNT,
      name: 'Tsoft-API',
      tester: false,
      socket_code: utils.generateHash(10)
    }).save()

    const password = new RandomHash({
      length: 40,
      charset: 'abcdefghijklmnopqrstuvwxyzABCDEF'
    })()

    await new ServiceAccount({
      company_id: company._id,
      user_id: serviceAccountUser._id,
      name: 'Tsoft-API',
      tsoft_app: true,
      key: password
    }).save()

    return password
  }

  return hasUser.key
}

async function createPackage(company) {
  const hasPackage = await CompanyHasPackage.findOne({
    company_id: company._id,
    deleted_at: {
      $exists: false
    }
  })
  if (hasPackage) {
    return
  }

  // şirkete paket tanımlanıyor
  const isPackage = await Package.findById(enums.tsoft_free_package_id)

  const { package } = helpers.packageSettings(isPackage)

  await new CompanyHasPackage({
    company_id: company._id,
    package_id: package._id,
    started_date: new Date(),
    name: package.name,
    type: package.type,
    description: package.description,
    is_active: true,
    price: package.price,
    data: package.data,
    package_type: package.package_type,
    currency: package.currency,
    expiry_date: moment().add(3, 'months').toDate(),
    premium: package.premium || false
  }).save()
}

module.exports = async (req, res, next) => {

  let newCompany = null
  let integrationDoc = null
  let isEmail = null
  try {

    const token = req.headers.token
    if (process.env.TSOFT_APP_TOKEN !== token) {
      return res.modifiedResponse(200, {
        success: false,
        data: {
          message: "Token Doğrulama Hatası."
        }
      })
    }

    const schema = Joi.object({
      username: Joi.string().required(),
      email: Joi.string().email().required(),
      domain: Joi.string().required(),
      ws_username: Joi.string().required(),
      ws_password: Joi.string().required(),
    })

    try {
      await schema.validateAsync(req.body)
    } catch (error) {
      return res.modifiedResponse(200, {
        success: false,
        data: {
          message: "Gönderdiğiniz Bilgileri Kontrol Ediniz."
        }
      })
    }

    const {
      username,
      email,
      domain,
      ws_username,
      ws_password
    } = req.body

    // Şirket kaydı var mı diye bakılıyor
    const checkCompany = await Company.findOne({
      tsoft_domain: domain,
      deleted_at: {
        $exists: false
      }
    })

    if (checkCompany) {
      if (checkCompany.tsoft_app_id) {
        checkCompany.is_active = true
        await checkCompany.save()

        const { channelId, script_url } = await HaveCompany(checkCompany, ws_username, ws_password)

        const serviceUserKey = await createServiceAccountUser(checkCompany)

        return res.modifiedResponse(200, {
          success: true,
          data: {
            company_id: checkCompany.tsoft_app_id,
            livechat_id: channelId,
            script_url: script_url,
            service_account_key: serviceUserKey
          }
        })
      }

      return res.modifiedResponse(200, {
        success: false,
        data: {
          isAccount: false,
          message: "Bu Şirket Zaten Var. Lütfen Farklı Bir Şirket Adı Giriniz"
        }
      })
    } else {
      const hasIntegration = await Integration.findOne({
        'data.base_url': domain,
        deleted_at: { $exists: false },
      }).populate('company_id')

      if (hasIntegration) {
        const hasUsers = await User.find({
          company_id: hasIntegration.company._id,
          type: {
            $in: [enums.acl_roles.COMPANY_OWNER, enums.acl_roles.COMPANY_USER]
          },
          tester: { $ne: true },
          deleted_at: { $exists: false },
        })
        if (hasUsers.length > 0) {
          const logins = await LoginWith.find({
            user_id: {
              $in: hasUsers.map(a => a._id)
            },
            deleted_at: {
              $exists: false
            }
          })

          const hasEmail = logins.find(a => a.email === email)
          if (hasEmail) {
            if (hasIntegration.company.is_active === false) {
              await createPackage(hasIntegration.company)
            }

            if (!hasIntegration.company.tsoft_app_id) {
              hasIntegration.company.tsoft_app_id = faker.random.uuid()
              hasIntegration.company.tsoft_domain = domain
              hasIntegration.company.is_active = true
              await hasIntegration.company.save()
            }

            const { channelId, script_url } = await HaveCompany(hasIntegration.company, ws_username, ws_password)

            const serviceUserKey = await createServiceAccountUser(hasIntegration.company)

            return res.modifiedResponse(200, {
              success: true,
              data: {
                company_id: hasIntegration.company.tsoft_app_id,
                livechat_id: channelId,
                script_url: script_url,
                service_account_key: serviceUserKey
              }
            })
          } else {
            return res.modifiedResponse(200, {
              success: false,
              data: {
                isAccount: true,
                message: "Bu Şirket Zaten Var. Kullanıcı Bulunamadı. Lütfen Doğru Email Adresi Giriniz"
              }
            })
          }
        } else {
          return res.modifiedResponse(200, {
            success: false,
            data: {
              isAccount: true,
              message: "Bu Şirket Zaten Var. Lütfen Doğru Email Adresi Giriniz"
            }
          })
        }
      }
    }

    isEmail = await LoginWith.findOne({ email: email, deleted_at: { $exists: false } })
    if (isEmail) {
      return res.status(200).json({
        success: false,
        data: {
          message: "Bu Email Zaten Kullanılıyor."
        }
      })
    }

    // Şirket Kaydı yapılıyor
    newCompany = await new Company({
      name: domain,
      socket_code: faker.random.uuid(),
      tsoft_app_id: faker.random.uuid(),
      tsoft_domain: domain
    }).save()

    await utils.CreateDefaultAgent(newCompany)

    await createPackage(newCompany)

    // Entegrasyon kaydı yapılıyor
    integrationDoc = await new Integration({
      type: enums.INTEGRATION_TYPES.TSOFT,
      name: enums.INTEGRATION_TYPES.TSOFT,
      is_active: true,
      company_id: newCompany.id,
      data: {
        base_url: domain,
        username: ws_username,
        password: helpers.encrypt(process.env.APP_SECRET_KEY, ws_username, ws_password),
        order_created_message_settings: {
          is_active_default: true,
          is_active: true
        }
      }
    }).save()

    // entegrasyonun bütün deskteklediği para birimi alınıyor
    const currenyCodes = await TsoftService.getCurrencyCodes(req, integrationDoc)

    // default para birimi alınır
    const defaultCurrencyCode = await TsoftService.getDefaultCurrencyCode(req, integrationDoc)

    const integrationData = integrationDoc.vData
    integrationData.setCurrencyCodes(currenyCodes.items.map(a => {
      return {
        id: a.CurrencyId,
        currency: a.Currency,
        default: a.Currency === defaultCurrencyCode ? true : false
      }
    }))

    const langs = await TsoftService.getLangs(req, integrationDoc)
    integrationData.setLangs(langs.items)

    integrationDoc.data = integrationData.getData()
    integrationDoc.markModified('data')
    await integrationDoc.save()

    const channelDoc = await new Channel({
      type: enums.channel_types.LIVE_CHAT,
      provider: enums.channel_providers.LIVE_CHAT,
      name: enums.channel_types.LIVE_CHAT,
      company_id: newCompany._id,
      integration_id: integrationDoc._id,
      is_active: true,
      agents: []
    }).save()

    const { first_name, last_name } = helpers.getCustomerName(username)
    const newUser = await new User({
      type: enums.acl_roles.COMPANY_OWNER,
      name: username,
      first_name: first_name,
      last_name: last_name,
      company_id: newCompany._id,
      email: email,
      is_active: true,
      socket_code: utils.generateHash(10)
    }).save()

    channelDoc.agents = [newUser._id]
    await channelDoc.save()

    // User için giriş kaydı oluşturuluyor
    const userPassword = utils.generateHash(10)
    await new LoginWith({
      user_id: newUser._id,
      email: email,
      password: bcrypt.hashSync(userPassword, 13),
      type: enums.login_with_types.EMAIL_PASSWORD
    }).save()

    const serviceUserKey = await createServiceAccountUser(newCompany)

    // Mail Bilgileri veritabaından alınıyor
    const settings = await Config.getSmtpSettings()

    // Mail Gönderiliyor
    await MailerService.send(settings, req.t('Onboarding.info.login_header'), MailerService.getMailFormatHtml(false, email, userPassword), username, email).catch((error) => {
      pino.error({
        trace_id: req.trace_id,
        message: "Mail Gönderilemedi",
        error: error?.message,
        data: JSON.stringify({
          email: email,
          username: username,
        }),
        company_id: newCompany._id?.toString(),
        timestamp: new Date()
      })
    })

    const scriptNew = await createScriptCode(newCompany._id, channelDoc.id)

    return res.modifiedResponse(200, {
      success: true,
      data: {
        company_id: newCompany.tsoft_app_id,
        livechat_id: channelDoc.id,
        service_account_key: serviceUserKey,
        script_url: scriptNew.url
      }
    })
  } catch (error) {
    if (newCompany) {
      await Company.deleteOne({ _id: newCompany._id })
    }
    if (integrationDoc) {
      await Integration.deleteOne({ _id: integrationDoc._id })
    }
    if (isEmail) {
      await LoginWith.deleteOne({ _id: isEmail._id, deleted_at: { $exists: false } })
    }
    return next(error)
  }
}
