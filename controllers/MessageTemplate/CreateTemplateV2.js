const createError = require("http-errors")

const MessageTemplate = require('../../models/MessageTemplate')
const MessageTemplateService = require("../../services/MessageTemplateService")

module.exports = async (req, res, next) => {
  try {

    const body = req.body

    const stateDto = await req.getState()

    const messageTemplate = await MessageTemplate.findOne({ company_id: stateDto.getCompanyId(), status: true })
    if (!messageTemplate) {
      throw new createError.NotFound(req.t('MessageTemplate.errors.message_template_not_found'))
    }

    await MessageTemplateService.CreateMessageTemplateV2(messageTemplate, body, stateDto.getUser().vData.getAppLangCode(), req.trace_id)

    return res.modifiedResponse(200, { success: true })
  } catch (e) {
    next(e)
  }
}
