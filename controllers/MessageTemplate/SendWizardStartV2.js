const createError = require("http-errors")

const MessageTemplate = require('../../models/MessageTemplate')
const ShopifyPayment = require('../../models/ShopifyPayment')

const MessageTemplateService = require("../../services/MessageTemplateService")

module.exports = async (req, res, next) => {
  try {

    const body = req.body

    const stateDto = await req.getState()

    const messageTemplate = await MessageTemplate.findOne({ company_id: stateDto.getCompanyId(), status: true })
    if (!messageTemplate) {
      throw new createError.NotFound(req.t('MessageTemplate.errors.message_template_not_found'))
    }

    if (stateDto.isShopifyCustomer()) {
      const query = {
        company_id: stateDto.getCompanyId(),
        deleted_at: { $exists: false }
      }

      if (process.env.PRODUCTION === 'true') {
        query.is_test = false
      }

      const hasPayment = await ShopifyPayment.findOne(query)

      if (!hasPayment) {
        throw new createError.BadRequest(req.t('Global.errors.conversation_package'))
      }
    }

    body.user_id = stateDto.getUserId()

    const response = await MessageTemplateService.SendWizardStartV2(messageTemplate, body, stateDto.getUser().vData.getAppLangCode(), req.trace_id)

    return res.status(200).json({ success: true, data: response.data })
  } catch (e) {
    next(e)
  }
}
