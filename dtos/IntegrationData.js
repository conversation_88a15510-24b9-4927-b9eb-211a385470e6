const enums = require('../libs/enums')

class IntegrationData {

  constructor(data) {

    this.data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    this.data.remittance_informations = this.data.remittance_informations || []

    // [{sub_payment_option_id: 1, is_active: true}]
    this.data.pay_at_door_options = this.data.pay_at_door_options || []

    // [{payment_option_id: 1, order_status_id: 2}]
    this.data.order_statuses = this.data.order_statuses || []

    // ürün listesinde ürüne ait hangi resim size kullanılacak?
    this.data.product_image_size = this.data.product_image_size || ''

    // agent müşteri oluşturmak istediğinde müşteriden onay almak zorunda kalacaksa true yoksa false
    this.data.request_confirmation_for_create_customer = this.data.request_confirmation_for_create_customer || false

    this.data.gdpr_url = this.data.gdpr_url || ''

    this.data.terms_of_use_url = this.data.terms_of_use_url || ''

    // fiyat gösterimlerde virgülden sonraki basamak ayarı
    this.data.price_precision = this.data.price_precision || 2

    // ürünlere ait en boy oranı
    this.data.ratio = this.data.ratio || 1

    // her satırda gösterilecek ürün sayısı
    this.data.row_item_count = this.data.row_item_count || 4

    // ürünün yeni olduğu bilgisini göstermek isterse true göstermek istemezse false
    this.data.show_is_new = typeof this.data.show_is_new === 'undefined' ?
      true : this.data.show_is_new

    // ürün listesinde ürüne ait marka bilgisi gösterilecekse true gösterilmeyecekse false
    this.data.show_brand = typeof this.data.show_brand === 'undefined' ?
      true : this.data.show_brand

    // entegrasyon için kullanılan dil kodları, ['tr', 'en']
    this.data.lang_codes = this.data.lang_codes || []

    // entegrasyon için kullanılan para birim kodları
    this.data.currency_codes = this.data.currency_codes || []

    //Entegrasyon bazında seçili ülke kodu için
    this.data.default_country_code = this.data.default_country_code || "TR"

    // Sayfalama bilgisi
    this.data.perpage = this.data.perpage || 10

    this.data.is_active_welcome_messaging = this.data.is_active_welcome_messaging || false


    this.data.is_active_default_welcome_messages = typeof this.data.is_active_default_welcome_messages === 'undefined' ?

      true : this.data.is_active_default_welcome_messages


    this.data.welcome_messages = this.data.welcome_messages || {}


    // is_active_out_of_working_hours_messaging

    this.data.is_active_oowh_messaging = this.data.is_active_oowh_messaging || false


    // is_active_default_out_of_working_hours_messages

    this.data.is_active_default_oowh_messages = typeof this.data.is_active_default_oowh_messages === 'undefined' ? true : this.data.is_active_default_oowh_messages


    // out_of_working_hours_messages

    this.data.oowh_messages = this.data.oowh_messages || {}


    // [{start: 12:00, end: 18:00}]

    this.data.working_hours = this.data.working_hours || []

    this.data.order_created_message_settings = this.data.order_created_message_settings || {
      is_active_default: true,
      is_active: true
    }

    this.data.warn_unregister_purchase = this.data.warn_unregister_purchase || false

    this.data.send_product_price_without_vat = this.data.send_product_price_without_vat || false

    this.data.calculate_vat0_on_international_invoices = this.data.calculate_vat0_on_international_invoices || false

    this.data.message_medias = this.data.message_medias || {}
  }

  //Bu fonksiyon ilerde Değişebilir
  getCurrencyCodes() {
    return this.data.currency_codes
  }

  getDefaultCurrencyCode() {
    return this.data.currency_codes.find(a => a.default)?.id || '0'
  }

  getBaseUrl() {
    return this.data.base_url.replace('/api', '')
  }

  setRemittanceInformations(data) {
    this.data.remittance_informations = data
  }

  getData() {
    return this.data
  }

  getRemittanceInformations() {
    return this.data.remittance_informations
  }

  setPayAtDoorOptions(new_data) {
    this.data.pay_at_door_options = new_data
  }

  getPayAtDoorOptionsExtended(req) {

    //pay_at_door_optionsun default değerlerini verdik.
    const paymentOptions = [
      {
        name: req.t('App.integration.cash'),
        sub_payment_option_id: -1,
        is_active: true
      },
      {
        name: req.t('App.integration.credit_card'),
        sub_payment_option_id: -2,
        is_active: true
      }
    ]

    this.data.pay_at_door_options.forEach(payAtDoorOption => {

      switch (parseInt(payAtDoorOption.sub_payment_option_id)) {

        case -1:
          paymentOptions[0].is_active = payAtDoorOption.is_active
          break

        case -2:
          paymentOptions[1].is_active = payAtDoorOption.is_active
          break

        default:
          throw 'Pay at door option is not found'
      }

    })

    return paymentOptions

  }

  getOrderStatuses() {
    return this.data.order_statuses
  }

  setOrderStatuses(data) {
    return this.data.order_statuses = data
  }

  getProductImageSize() {
    return this.data.product_image_size
  }

  setProductImageSize(size) {
    this.data.product_image_size = size
  }

  getRequestConfirmationForCreateCustomer() {
    return this.data.request_confirmation_for_create_customer
  }

  setRequestConfirmationForCreateCustomer(result) {
    this.data.request_confirmation_for_create_customer = result
  }

  getGdprUrl() {
    return this.data.gdpr_url
  }

  setGdprUrl(url) {
    this.data.gdpr_url = url
  }

  getTermsOfUseUrl() {
    return this.data.terms_of_use_url
  }

  setTermsOfUseUrl(url) {
    this.data.terms_of_use_url = url
  }

  getPricePrecision() {
    return this.data.price_precision
  }

  setPricePrecision(precision) {
    this.data.price_precision = precision
  }

  getRatio() {
    return this.data.ratio
  }

  setRatio(ratio) {
    this.data.ratio = ratio
  }

  getRowItemCount() {
    return this.data.row_item_count
  }

  setRowItemCount(count) {
    this.data.row_item_count = count
  }

  getShowIsNew() {
    return this.data.show_is_new
  }

  setShowIsNew(show) {
    this.data.show_is_new = show
  }

  getShowBrand() {
    return this.data.show_brand
  }

  setShowBrand(show) {
    this.data.show_brand = show
  }

  getLangCodes() {
    return this.data.lang_codes
  }

  getPaymentOptionIds() {
    return ["-2", "-6", "-7", "-8", "-9", "-11", "-13", "-14", "-15", "-16", "-17", "-18", "-20", "-26", "-21"]
  }

  /**
   * Ödeme yöntem bilgisine göre siparişe ait durum bilgisini döner. Öncelikle entegrasyon ayarlarında böyle
   * bir kayıt var mı diye bakar, eğer yoksa varsayılan ödeme yöntemlerinden birisini döner
   *
   * @param paymentOptionId
   *
   * @return {number}
   */
  getOrderStatusId(paymentOptionId) {

    // eğer kredi kartı ise, order status 1 olarak dönülecek.
    // eğer iyzico ise, order status 1 olarak dönülecek
    // https://tsoftetc.atlassian.net/browse/HLRB-1106
    if (this.getPaymentOptionIds().includes(paymentOptionId)) {
      return 1
    }

    const selected = this.getOrderStatuses().find(item => item.payment_option_id == paymentOptionId)

    if (selected) {
      return selected.order_status_id
    }

    switch (parseInt(paymentOptionId)) {

      // havale ise
      case -1:
        return 2
      // kredi kartı ise
      case -2:
        return 1
      // kapıda ödeme ise
      case -3:
        return 13
      default:
        return 2 // varsayılan olarak 2 dönüyoruz, daha sonra güncellenecek
    }

  }

  getDefaultCountryCode() {
    return this.data.default_country_code
  }

  setPerPage(data) {
    this.data.perpage = data
  }

  getPerPage() {
    return this.data.perpage
  }

  //---- Tsoft için
  setBaseUrl(data) {
    this.data.base_url = data
  }

  setPassword(data) {
    this.data.password = data
  }

  getPassword() {
    return this.data.password
  }

  setUserName(data) {
    this.data.username = data
  }

  getPhoneNumberRequired() {
    return this.data.phone_number_required == true
  }

  setPhoneNumberRequired(data) {
    this.data.phone_number_required = data
  }

  setCalculateVAT0onInternationalInvoices(data) {
    this.data.calculate_vat0_on_international_invoices = data
  }

  getCalculateVAT0onInternationalInvoices() {
    return this.data.calculate_vat0_on_international_invoices
  }

  getUserName() {
    return this.data.username
  }

  setOrderCreatedMessages(data) {
    this.data.order_created_message_settings.messages = data
  }

  getManuelDiscountPermissionAll() {
    return this.data.manuel_discount_permission_all || false
  }

  setManuelDiscountPermissionAll(data) {
    this.data.manuel_discount_permission_all = data
  }

  getOrderCreatedMessages() {
    return this.data.order_created_message_settings.messages
  }

  getIsActiveOrderCreatedMessaging() {
    return this.data.order_created_message_settings.is_active || false
  }

  setIsActiveOrderCreatedMessaging(data) {
    this.data.order_created_message_settings.is_active = data
  }

  getIsActiveDefaultOrderCreatedMessaging() {
    return this.data.order_created_message_settings.is_active_default || false
  }

  setIsActiveDefaultOrderCreatedMessaging(data) {
    this.data.order_created_message_settings.is_active_default = data
  }

  getOrderCreatedMessageContentByLangCode(code) {
    return this.data.order_created_message_settings.messages?.[code] || ''
  }
  //----- Tsoft

  //----- Shopify
  setStoreName(data) {
    this.data.store_name = data
  }

  getStoreName() {
    return this.data.store_name
  }

  setApiKey(data) {
    this.data.api_key = data
  }

  getApiKey() {
    return this.data.api_key
  }

  setApiPassword(data) {
    this.data.api_password = data
  }

  getApiPassword() {
    return this.data.api_password
  }

  setApiVersion(data) {
    this.data.api_version = data
  }

  getApiVersion() {
    return this.data.api_version
  }

  setStoreFrontKey(data) {
    this.data.store_front_key = data
  }

  getStoreFrontKey() {
    return this.data.store_front_key
  }

  setAdminAccessToken(data) {
    this.data.admin_access_token = data
  }

  getAdminAccessToken() {
    return this.data.admin_access_token
  }
  //----- Shopify

  //ToDo: Bu kısım daha sonra silinecek

  setIsActiveWelcomeMessaging(active) {
    this.data.is_active_welcome_messaging = active
  }


  setIsActiveDefaultWelcomeMessaging(active) {
    this.data.is_active_default_welcome_messages = active
  }


  getIsActiveWelcomeMessaging() {
    return this.data.is_active_welcome_messaging
  }


  getIsActiveDefaultWelcomeMessages() {
    return this.data.is_active_default_welcome_messages
  }


  setWelcomeMessages(items) {
    this.data.welcome_messages = items
  }


  getWelcomeMessages() {
    return this.data.welcome_messages
  }


  getWelcomeMessageContentByLangCode(code) {
    return this.data.welcome_messages[code] || ''
  }


  setIsActiveOowhMessaging(active) {
    this.data.is_active_oowh_messaging = active
  }


  getIsActiveOowhMessaging() {
    return this.data.is_active_oowh_messaging
  }


  getIsActiveDefaultOowhMessages() {
    return this.data.is_active_default_oowh_messages
  }


  setIsActiveDefaultOowhMessages(active) {
    this.data.is_active_default_oowh_messages = active
  }


  setOowhMessages(items) {
    this.data.oowh_messages = items
  }


  getOowhMessages() {
    return this.data.oowh_messages
  }

  getOowhMessageContentByLangCode(code) {
    return this.data.oowh_messages[code] || ''
  }


  getWorkingHours() {
    return this.data.working_hours
  }

  setWorkingHours(hours) {
    this.data.working_hours = hours
  }

  setDefaultCurrencyCode(id) {
    const index = this.data.currency_codes.findIndex(item => item.id === id)
    if (index !== -1) {
      for (let i = 0; i < this.data.currency_codes.length; i++) {
        this.data.currency_codes[i].default = false
      }

      this.data.currency_codes[index].default = true;
    }
  }

  setCurrencyCodes(data) {
    const defaultCode = data.find(a => a.default === true)
    if (!defaultCode) {
      if (typeof data[0]?.default === 'boolean') {
        data[0].default = true
      }
    }

    this.data.currency_codes = data
  }

  setLangs(data) {
    this.data.lang_codes = data
  }

  getCreditCardRequirementForNonMembers() {
    return this.data.warn_unregister_purchase
  }

  setCreditCardRequirementForNonMembers(data) {
    this.data.warn_unregister_purchase = data
  }

  getSendProductPriceWithoutVat() {
    return this.data.send_product_price_without_vat
  }

  setSendProductPriceWithoutVat(data) {
    this.data.send_product_price_without_vat = data
  }

  getOrderMessageImageUrl() {
    return this.data.message_medias.order_message_image_url
  }

  setOrderMessageImageUrl(url) {
    this.data.message_medias.order_message_image_url = url
  }

  getBasketSummaryMessageImageUrl() {
    return this.data.message_medias.basket_summary_message_image_url
  }

  setBasketSummaryMessageImageUrl(url) {
    this.data.message_medias.basket_summary_message_image_url = url
  }

  resetMessageImageUrl() {
    this.data.message_medias = {}
  }

  getMessageImageUrl() {
    return {
      order_message_image_url: this.data.message_medias?.order_message_image_url || enums.order_message_image_url,
      basket_summary_message_image_url: this.data.message_medias?.basket_summary_message_image_url || enums.basket_summary_message_image_url,
    }
  }

}

module.exports = IntegrationData
