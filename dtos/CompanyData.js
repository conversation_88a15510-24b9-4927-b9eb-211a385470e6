class CompanyData {

  constructor(data) {
    this.data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    this.data.advermind = this.data.advermind || {}

    this.data.hide_phone_number = this.data.hide_phone_number || false

    this.data.company_owner_get_force_chat = this.data.company_owner_get_force_chat || false

    this.data.package = this.data.package || {
      price: 69, // default paket ücreti
      monthly_conversation_limit: 1000,
      agent_settings: {
        limit: {
          unlimit: false,
          limit: 5
        }
      },
      channel_settings: {
        limit: {
          whatsapp_channel: 1,
          facebook_channel: 1,
          instagram_channel: 1,
          telegram_channel: 1,
          livechat_channel: 1
        }
      },
      thinker: {
        status: true,
        expire: null // string olarak date gelcek
      },
      helobot: {
        status: true,
        expire: null // string olarak date gelcek
      }
    }

    this.data.report_options = this.data.report_options || {
      daily_report: false,
      weekly_report: false,
      monthly_report: false
    }

    this.data.overtime_status = this.data.overtime_status
    this.data.profit_multiplier = this.data.profit_multiplier || 40
  }

  getWhatsappProfitMultiplier() {
    return this.data.profit_multiplier
  }

  getWhatsappProfitMultiplierForCost() {
    return (this.data.profit_multiplier / 100) + 1
  }

  setWhatsappProfitMultiplier(data) {
    this.data.profit_multiplier = data
  }

  getData() {
    return this.data
  }

  getAdvermindToken() {
    return this.data.advermind.token
  }

  setAdvermindToken(data) {
    this.data.advermind.token = data
  }

  setAdvermindLoginLivedAccessToken(data) {
    this.data.advermind.access_token = data
  }

  setAdvermindCustomizationsHash(data) {
    this.data.advermind.customizationsHash = data
  }

  getAdvermindLoginLivedAccessToken() {
    return this.data.advermind.access_token
  }

  getAssignChatToAgent() {
    return this.data.assign_chat_to_agent || false
  }

  setAssignChatToAgent(data) {
    this.data.assign_chat_to_agent = data
  }

  getHidePhoneNumber() {
    return this.data.hide_phone_number
  }

  setHidePhoneNumber(data) {
    this.data.hide_phone_number = data
  }

  setTeamStatus(data) {
    this.data.team_status = data
  }

  getTeamStatus() {
    return this.data.team_status || false
  }

  getChatReadStatus() {
    return this.data.chat_message_read_status || false
  }

  setChatReadStatus(data) {
    this.data.chat_message_read_status = data
  }

  getDataWebhookUrl() {
    return this.data.url || ""
  }

  setDataWebhookUrl(data) {
    return this.data.url = data
  }

  setDataWebhookUrlHash(data) {
    return this.data.url_hash = data
  }

  getDataWebhookUrlHash() {
    return this.data.url_hash || ''
  }

  getUsingAsBridge() {
    return this.data.using_as_bridge || false
  }

  setUsingAsBridge(data) {
    return this.data.using_as_bridge = data
  }

  getPackageAgentUnlimit() {
    return this.data.package.agent_settings.limit.unlimit
  }

  getPackageAgentLimit() {
    return this.data.package.agent_settings.limit.limit
  }

  getMonthlyConverstaionLimit() {
    return this.data.package.monthly_conversation_limit || 1000
  }

  getMonthlyPackagePrice() {
    return this.data.package.price || 69
  }

  getCompanyOwnerGetForceChat() {
    return this.data.company_owner_get_force_chat
  }

  setCompanyOwnerGetForceChat(data) {
    return this.data.company_owner_get_force_chat = data
  }

  getCompanyPackage() {
    return this.data.package
  }

  setCompanyPackage(data) {
    this.data.package = data
  }

  getCompanyPackageChannelSettings() {
    return this.data.package.channel_settings
  }

  setDailyReport(status) {
    this.data.report_options.daily_report = status
  }

  setWeeklyReport(status) {
    this.data.report_options.weekly_report = status
  }

  setMonthlyReport(status) {
    this.data.report_options.monthly_report = status
  }

  getDailyReport() {
    return this.data.report_options.daily_report
  }

  getWeeklyReport() {
    return this.data.report_options.weekly_report
  }

  getMonthlyReport() {
    return this.data.report_options.monthly_report
  }

  setOvertimeStatus(data = false) {
    this.data.overtime_status = data
  }

  getOvertimeStatus() {
    return this.data.overtime_status || false
  }

  setPaymentInformationPopupStatusForUser(data) {
    this.data.payment_information_popup_status_for_users = data
  }

  getPaymentInformationPopupStatusForUser() {
    return this.data.payment_information_popup_status_for_users || false
  }

}

module.exports = CompanyData
