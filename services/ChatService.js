const moment = require('moment')
const mongoose = require('mongoose')
const createError = require('http-errors')
const pino = require('pino')()

const enums = require('../libs/enums')
const utils = require('../libs/utils')
const helpers = require('../libs/helpers')

const Job = require('./../models/Job')
const User = require('./../models/User')
const Chat = require('./../models/Chat')
const Message = require('./../models/Message')
const Channel = require('./../models/Channel')
const WebPushToken = require('../models/WebPushToken')
const Team = require('../models/Team')
const ThinkerHasChat = require('../models/ThinkerHasChat')
const HelobotHasChat = require('../models/HelobotHasChat')
const AbandonedCart = require('../models/AbandonedCart')
const Company = require('../models/Company')
const ChatIntegration = require('../models/ChatIntegration')
const AbandonedCartHasChat = require('../models/AbandonedCartHasChat')

const AgentReports = require('./Report/AgentReports')

const MessageRepo = require('../repos/MessageRepo')

const QueueService = require('../services/QueueService')
const ChatActions = require('../services/ChatActions')
const ThinkerService = require('../services/ThinkerService')
const HelobotService = require('../services/HelobotService')
const MetaEventService = require('../services/MetaEventService')

const IntegrationService = require('../modules/AgentApp/IntegrationService')
const HeloscopeService = require('../integrations/Heloscope/HeloscopeService')

const FirebaseService = require('../integrations/Firebase/FirebaseService')

const DashPresenter = require('../presenters/Dash')

const WhatsappApiService = require('../integrations/Whatsapp/WhatsappApiService')

const FacebookApiService = require('../integrations/Facebook/FacebookApiService')

const MessageCustomerEvaluation = require('../jobs/MessageCustomerEvaluation')
const SendMessageJob = require('../jobs/SendMessageJob')
const ShopifyService = require('../integrations/Shopify/ShopifyService')

const TelegramApiService = require('../integrations/Telegram/TelegramApiService')
const RedisConnection = require('../modules/RedisConnection')
const ChatReplyReportService = require('./ChatReplyReportService')
const ElasticsearchService = require('./ElasticsearchService')

const sendSocketMessage = async (event, rooms, data, language) => {
  await QueueService.publishToAppSocket({
    event: event,
    socket_rooms: rooms,
    data: data
  }, language)
}

const ChatService = {

  newMessageAdded: (chat, message, isCustomer = true) => {

    if (isCustomer) {
      chat.customer_last_message_time = message.created_at

      // okunmayan mesajın olduğunu bilmek için
      chat.has_unreaded_message = true

      // conversationdaki okunmayan mesaj sayısını 1 arttırmak için.
      chat.unread_message_count += 1
    }

    // son eklenen mesaj bilgisini saklayalım
    chat.last_message_id = message._id

    // sıralama mesajın oluşma zamanına göre yapılsın
    chat.sort_field = message.created_at

    return chat.save()
  },

  getOrCreateConversation: async (title, channelId, extId, profilePic, createdAt = Date.now()) => {
    let chat = await Chat.findOne({
      channel_id: channelId,
      ext_id: extId
    })

    if (!chat) {
      chat = await new Chat({
        channel_id: channelId,
        ext_id: extId,
        title: title,
        created_at: createdAt,
        profile_pic: profilePic
      }).save()

      // elastice gönderiliyor
      await QueueService.publishToFifoChat({
        create: true,
        data: {
          id: chat.id,
          title: chat.title,
          ext_id: chat.ext_id,
          username: chat.username,
          channel_id: chat.channel_id,
          timestamp: chat.created_at,
          created_at: chat.created_at
        }
      })
    } else {
      chat.title = title
      chat.profile_pic = profilePic
      await chat.save()

      // elastice gönderiliyor
      await QueueService.publishToFifoChat({
        update: true,
        id: chat.id,
        data: {
          title: chat.title,
          ext_id: chat.ext_id,
          username: chat.username,
          channel_id: chat.channel_id,
          timestamp: new Date()
        }
      })
    }

    return chat
  },

  checkNotReplied: async (req, data) => {

    const customerLastMessageId = data.customer_last_message_id
    const userId = data.user_id

    const customerMessage = await Message.findById(customerLastMessageId)

    // Conversation'daki en son agent'in ya da sistem mesajını getir.
    const lastMessage = await Message.findOne({
      conversation_id: customerMessage.conversation_id,
      from_type: { $in: [enums.message_from_types.AGENT, enums.message_from_types.SYSTEM] }
    }).sort({ 'created_at': -1 })

    if (lastMessage && (lastMessage.created_at > customerMessage.created_at)) {
      pino.info({
        trace_id: data.trace_id,
        timestamp: new Date(),
        message: customerMessage.conversation_id.toString() + ' chat_id li kişiye cevap verildiği için bkeleyene düşürülmedi.',
        action_time: data.time
      })

      // Agent veye sistem conversation'a cevap vermiş. Birşey yapma
      return true
    }

    // Agent conversatioına cevap vermemiş... Public'e gönder.
    const chat = await Chat.findOne({
      _id: customerMessage.conversation_id,
      owner_user_id: userId
    }).populate('last_message_id').populate({
      path: 'channel_id',
      populate: {
        path: 'integration_id',
      }
    })

    if (!chat) {
      throw new createError.NotFound(req.t('App.errors.conversation.not_found'))
    }

    if (chat.pinned_at) {
      return true
    }

    const isActiveChat = await RedisConnection.getAgentLastCustomer(chat.owner_user_id.toString())
    if (isActiveChat === chat.id) {
      return
    }

    chat.pinned_at = undefined
    chat.sort_field = Date.now()
    chat.owner_user_id = undefined
    chat.owner_user = false

    await chat.save()

    await ChatActions(req, enums.chat_actions.system_moved_to_public, null, chat._id)

    const chatIntegration = await ChatIntegration.findOne({ chat_id: chat._id })

    const chatItem = DashPresenter.getChatItem(
      chat,
      chat.lastMessage,
      chat.channel,
      chatIntegration,
      chat.thinker_status,
      chat.helobot_status
    )

    await sendSocketMessage(enums.agent_app_socket_events.CHAT_TIMEOUT, [chat.channel.id], {
      chat_item: chatItem
    }, req.language)

    pino.info({
      trace_id: data.trace_id,
      timestamp: new Date(),
      message: chat.id + ' chat_id li kişiye cevap verilmediği için aktiften bekleyene düşürüldü.',
      action: enums.agent_app_socket_events.CHAT_TIMEOUT,
      action_time: data.time
    })
  },

  checkCustomerNotReplied: async (req, data) => {

    const lastMessage = await Message.findOne({ conversation_id: new mongoose.Types.ObjectId(data.chat_id) }).sort({ _id: -1 })

    if (!data.force_run && lastMessage.from_type === enums.message_from_types.CUSTOMER) {
      pino.info({
        trace_id: data.trace_id,
        timestamp: new Date(),
        message: data.chat_id + ' chat_id li kişi cevap verdiği için arşive gönderme işlemi yapılmadı.',
        action_time: data.time
      })

      return
    }

    if (!data.force_run) {
      // job ın işlemindeki mesaj id ile müşterinin son mesaj id olması dikkate alınıyor.
      if (lastMessage.from_type === enums.message_from_types.AGENT && lastMessage.id !== data.message_id) {
        return
      }
    }

    const chat = await Chat.findById(data.chat_id)
      .populate({
        path: 'channel_id',
        populate: {
          path: 'company_id',
        }
      }).populate('owner_user_id')

    if (!chat) {
      return
    }

    if (chat.pinned_at) {
      return
    }

    if (chat.archived_at) {
      return
    }

    let socket_rooms = []

    if (!chat.owner_user_id) {
      socket_rooms = [chat.channel_id.company_id.vSocketCode]
    } else {
      socket_rooms = [chat.owner_user_id.vSocketCode]
    }

    if (!chat.channel_id.company_id.vData.data.assign_chat_to_agent) {
      if (chat.owner_user_id) {
        const isActiveChat = await RedisConnection.getAgentLastCustomer(chat.owner_user_id._id.toString())
        if (isActiveChat === chat.id) {
          return
        }

        chat.owner_user_id = undefined
        chat.owner_user = false
      }
    }

    chat.archived_at = Date.now()
    chat.archived = true
    await chat.save()

    const channelSettings = chat.channel.vSettings

    if (channelSettings.getSendMessageWhileArchived()) {
      if (channelSettings.getArchiveThinkerStatus() && channelSettings.getArchiveThinkerFlowId()) {
        let customerMessage = lastMessage
        if (lastMessage.from_type !== enums.message_from_types.CUSTOMER) {
          customerMessage = await Message.findOne({
            conversation_id: new mongoose.Types.ObjectId(data.chat_id),
            from_type: enums.message_from_types.CUSTOMER
          }).sort({ created_at: -1 })
        }

        await ThinkerService.StartProcedure(chat, chat.channel_id.company_id, chat.channel_id._id.toString(), channelSettings.getArchiveThinkerFlowId(), req.trace_id, customerMessage.vContentText, 'now')

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.CHAT_HIDE,
          socket_rooms: socket_rooms,
          data: {
            chat_id: chat.id
          }
        }, 'tr')

        return QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
          socket_rooms: [chat.channel_id.id],
          data: {
            chat_id: chat.id
          }
        }, 'tr')
      } else {
        const langCode = utils.getLangCodeAsShort(chat.vData.getChatLangCode())

        let text = helpers.getArchiveMessage(req, langCode, channelSettings)

        if (text) {
          await ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
            text: text
          }, { mark_as_seen_event: false })
        }
      }
    }

    await ChatActions(req, enums.chat_actions.system_chat_hide, null, chat._id, socket_rooms)

    await sendSocketMessage(
      enums.agent_app_socket_events.CHAT_HIDE,
      socket_rooms,
      {
        chat_id: chat._id.toString()
      }, req.language)

    pino.info({
      trace_id: data.trace_id,
      timestamp: new Date(),
      message: chat.id + ' chat_id li kişi cevap vermediği için arşive gönderildi.',
      action: enums.agent_app_socket_events.CHAT_HIDE,
      action_time: data.time
    })
  },

  queueCheckNotAnswered: async (req, message, chat) => {

    if (!chat.owner_user_id) {
      return null
    }

    // Eğer konuşma arşivde ise 5 DK kontrolü yapılacak. yoksa adamın private'ndakini global'a göndermesin 😄
    const channel = await Channel.findById(chat.channel_id).populate('integration_id')

    //  Chat Arşivdemiydi ?
    if (chat.vIsArchived) {
      return
    }

    if (!chat.owner_user_id) {
      return
    }

    const agent = await User.findById(chat.owner_user_id)

    if (!agent) {
      return
    }

    if (!channel.vSettings.getFromActiveTimeout()) {
      return null
    }

    const timeoutAsMs = channel.vSettings.getFromActiveTimeout() * 1000//1000 * 60 * 5 // 10000 denek için bunu kullanabiliriz.

    // last message user type
    let jobDoc = new Job()

    jobDoc.type = enums.job_types.NOT_REPLIED
    jobDoc.status = enums.job_statuses.CREATED
    jobDoc.data = {
      customer_last_message_id: message._id,
      user_id: chat.owner_user_id,
      trace_id: req.trace_id,
      time: timeoutAsMs
    }

    await jobDoc.save()

    await QueueService.publishToDelayedWorker({
      job_id: jobDoc.id
    }, req.language, timeoutAsMs)

    await AgentReports.setInterviewReportsFinishTime(agent._id, chat._id)

  },

  checkCustomerEvaluationMessage: async (req, customerEvaluationStatus, customerEvaluationOptions, chat, channel, isIntegration, option, chatOwnerId) => {
    if (customerEvaluationStatus) {
      if (!customerEvaluationOptions) {
        await ChatService.queueCustomerEvaluation(req, chat, channel, !!isIntegration, chatOwnerId)
      } else {
        if (customerEvaluationOptions === option) {
          await ChatService.queueCustomerEvaluation(req, chat, channel, !!isIntegration, chatOwnerId)
        }
      }
    }
  },

  queueTeamMessage: async (req, teamStatus, optionStatus, chat, channelType, timeout = 2 * 60 * 60 * 1000) => { //defaultta 2 saat
    try {
      if (teamStatus && optionStatus) {
        const jobDoc = await ChatService.addTeamMessage(req, chat, channelType)

        await QueueService.publishToTeamMessage({
          job_id: jobDoc.id
        }, req.i18n.language, timeout)
      }
    } catch (error) {
      pino.error({
        trace_id: req.trace_id,
        timestamp: new Date(),
        message: 'Ekip Mesajı gönderiminde kuyruğa alma işleminde hata oluştu',
        chat_id: chat.id,
        channel_type: channelType,
        error: typeof error === 'object' ? JSON.stringify(error) : ""
      })
    }
  },

  queueCustomerEvaluation: async (req, chat, channel, isIntegration, chatOwnerId) => {
    const channelSettings = channel.vSettings

    if (!channel.vSettings.getCustomerEvaluationOptions()) {
      // ilerde burası kaldırılabilir.
      if (isIntegration) {
        channelSettings.setCustomerEvaluationOption(enums.customer_evaluation_options.create_order)
      } else {
        channelSettings.setCustomerEvaluationOption(enums.customer_evaluation_options.archived)
      }

      channel.settings = channelSettings.getData()
      channel.markModified('settings')
      await channel.save()
    }

    const jobDoc = await MessageCustomerEvaluation.add(req, chat, channel.type, chatOwnerId)

    const timeoutAsMs = channel.vSettings.getCustomerEvaluationTimeout() * 60 * 1000

    await QueueService.publishToCustomerEvaluation({
      job_id: jobDoc.id
    }, req.i18n.language, timeoutAsMs)

  },

  /**
   * @param req
   * @param chat
   * @param channel
   * @param agent
   *
   * @return {Promise<Chat>}
   */
  markAsSeen: (req, chat, channel, agent) => {

    return Promise.resolve().then(() => {

      if (!chat.has_unreaded_message) {
        return chat
      }

      chat.has_unreaded_message = false
      chat.unread_message_count = 0

      return chat.save().then(chat => {

        switch (channel.type) {

          case enums.channel_types.FACEBOOK_PAGE:

            // facebook mark_seen işlemi arka planda gerçekleşsin
            FacebookApiService.markAsSeen(req, chat.ext_id, channel.vSettings.getAccessToken())

            return chat

          case enums.channel_types.WHATSAPP_NUMBER:

            // müşteriye ait son mesajı elde edelim
            return MessageRepo.getCustomerLastMessageByConversationId(chat.id).then(message => {

              if (!message) {
                return chat
              }

              switch (channel.provider) {

                case enums.channel_providers.TEKROM:

                  WhatsappApiService.markAsSeen(req, channel, message)

                  return chat

                case enums.channel_providers.CLOUD:

                  WhatsappApiService.markAsSeenCloud(req, channel, message.ext_id)

                  return chat

              }


            })

          case enums.channel_types.LIVE_CHAT:

            chat.unread_message_count = 0

            return chat

          case enums.channel_types.TELEGRAM_ACCOUNT:
            TelegramApiService.markAsSeen(chat, channel)
            return chat
          default:

            return chat

        }

      }).then(chat => {

        if (agent) {

          req.app.services.QueueService.publishToAppSocket({
            event: enums.agent_app_socket_events.MARKED_AS_SEEN,
            socket_rooms: [agent.vSocketCode],
            data: {
              chat_item: DashPresenter.getChatItem(chat, chat.lastMessage, chat.channel, undefined, chat.thinker_status, chat.helobot_status)
            }
          }, req.language)

        }

      })

    })

  },

  /**
   * @param chatId
   * @param channelType
   *
   * @return {Promise<boolean>}
   */
  checkSendable: async (chatId, channelType) => {

    const data = {
      isTag: false,
      sendable: false
    }

    const message = await MessageRepo.getCustomerLastMessageByConversationId(chatId)

    if (!message) {
      return data
    }

    if (channelType === enums.channel_types.TELEGRAM_ACCOUNT || channelType === enums.channel_types.LIVE_CHAT) {
      data.sendable = true
      return data
    }

    const hoursPassed = moment().diff(message.created_at, 'hours')
    const sendableHour = 24

    //instagram için tag ile birlikte 7 gün içinde mesaj atılabiliyor
    if (channelType === enums.channel_types.INSTAGRAM_ACCOUNT || channelType === enums.channel_types.FACEBOOK_PAGE) {
      if (hoursPassed >= sendableHour && hoursPassed < sendableHour * 7) {
        data.isTag = true
        data.sendable = true
        return data
      }

    }

    if (hoursPassed < sendableHour) {
      data.sendable = true
    } else {
      data.sendable = false
    }

    return data
  },

  /**
   * firmaya ait kullanıcıların listesini alıp, her bir kullanıcı için web push tokenlarına bakarak
   * firebase üzerinden notification gönderilecek
   *
   * @param {string} companyId
   */
  sendFirebaseNotification: companyId => {

    // firmaya ait kullanıcıların listesini alıp, her bir kullanıcı için web push tokenlarına bakarak
    // firebase üzerinden notification gönderilecek
    User.find({
      type: enums.acl_roles.COMPANY_USER,
      is_active: true,
      company_id: companyId
    }).then(users => users.map(user => user.id)).then(userIds => {

      return WebPushToken.find({
        user_id: {
          '$in': userIds
        }
      })

    }).then(webPushTokens => {

      webPushTokens.forEach(webPushToken => {

        FirebaseService.sendMessage(webPushToken.token, 'Yeni Mesaj', 'Görmek için tıklayınız.').then(() => undefined)

      })

    })

  },

  /**
   * @untested
   *
   * @param req
   * @param {string} chatId
   * @param {string} type
   * @param {object} data
   * @param {string} agentId
   *
   * @return {Promise<void>}
   */
  sendAgentMessage: (req, chatId, type, data, agentId) => {

    if (!type) {
      throw new createError.BadRequest('Type bilgisini göndermelisiniz.')
    }

    let content = {}

    switch (type) {

      case enums.message_types.IMAGE_URL:

        if (!data.url) {
          throw new createError.BadRequest('Url bilgisini lütfen boş bırakmayalım')
        }

        content = {
          url: data.url,
          caption: data.caption,
          hide_image: data.hide_image || false,// @todo bu bilgi false ise gönderilmeye gerek yok, sendAgentMessage fonkisyonunun kullanımına bakılarak kod refaktör edilecek
          bb_code: data.bb_code,
          next_action: data.next_action,
          agent_id: data.agent_id,
          language: data.language
        }

        break;

      case enums.message_types.TEXT:

        if (!data.text) {
          throw new createError.BadRequest(req.t('App.errors.conversation.message_cannot_be_empty'))
        }

        content = {
          text: data.text,
          bb_code: data.bb_code,
          bot_data: data.bot_data,
          hide_image: data.hide_image || false,
          next_action: data.next_action,
          agent_id: data.agent_id,
          language: data.language
        }

        break;

      default:

        throw new createError.BadRequest(req.t('App.errors.messaging.sending_message_type_unsupported'))

    }

    return ChatService.addAgentMessage(req, chatId, type, content, agentId)

  },

  /**
   * @untested
   *
   * @param req
   * @param {string} chatId
   * @param {string} type
   * @param {object} content
   * @param {string} agentId
   * @param {string} tempId
   *
   *
   * @return {Promise<void>}
   */

  addAgentMessage: async (req, chatId, type, content, agentId, tempId = undefined, data = {}) => {

    const chat = await Chat.findById(chatId).populate('channel_id')

    if (chat.channel.type === enums.channel_types.LIVE_CHAT) {
      await helpers.sleepFunction(1000)
    }

    if (chat.channel.is_active === false) {
      throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
    }

    const message = await MessageRepo.create({
      type: type,
      content: content,
      chatId: chatId,
      userId: agentId,
      fromType: enums.message_from_types.AGENT,
      sendStatus: enums.message_send_statuses.QUEUED,
      data: data,
      time: moment().unix(),
      platform: req.platform,
    }, chat.channel.company_id.toString(), chat.channel.id)

    ChatService.messageSetSortTime(chatId)

    const job = await SendMessageJob.add(message._id, agentId, agentId, tempId)

    await QueueService.publishToFifoWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, req.language)

    job.status = enums.job_statuses.QUEUED

    await job.save()

    if (chat.channel_id.vSettings.getSendChatToArchiveStatus()) {
      // bot action işlemleri dahil edilmeyecek
      if (content.next_action) {
        return
      }

      await ChatService.addJobCustomerNotReplied(req, chat, agentId, message.id, false, false)
    }

    await AgentReports.addAgentInterviewReport(agentId, chat._id, enums.AGENT_INTERVIEW_REPORTS.ASSIGNED_TO_ME)

    process.nextTick(() => {
      ChatReplyReportService.addAgentMessage(chat._id, chat.channel._id, agentId, message)
    })
  },

  sendOnlineWatcherEvent: async (companyId, chatId, agentId, eventName, data = {}) => {
    // agent izleme için event gönderiliyor
    process.nextTick(async () => {
      const companyOwners = await User.find({
        company_id: companyId,
        type: enums.acl_roles.COMPANY_OWNER,
        deleted_at: {
          $exists: false
        }
      })

      for (const owner of companyOwners) {
        await QueueService.publishToAppSocket({
          event: eventName,
          socket_rooms: [owner.vSocketCode],
          data: {
            chat_id: chatId,
            agent_id: agentId,
            ...data
          }
        })
      }
    })
  },

  addJobCustomerNotReplied: async (req, chat, agentId, messageId, forceRun = false) => {
    const timeoutAsMs = chat.channel_id.vSettings.getSendChatToArchiveTimeout() * 60 * 1000

    // last message user type
    let jobDoc = new Job()

    jobDoc.type = enums.job_types.CUSTOMER_NOT_REPLIED
    jobDoc.status = enums.job_statuses.CREATED
    jobDoc.data = {
      chat_id: chat.id,
      user_id: agentId,
      trace_id: req.trace_id,
      time: timeoutAsMs,
      message_id: messageId,
      force_run: forceRun,
    }

    await jobDoc.save()

    await QueueService.publishToDelayedWorker({
      job_id: jobDoc.id
    }, req.language, timeoutAsMs)
  },

  addThinkerAgentMessage: async (req, chatId, agentId, type, content) => {

    const chat = await Chat.findById(chatId).populate('channel_id')

    if (chat.channel.is_active === false) {
      throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
    }

    const message = await MessageRepo.create({
      type: type,
      content: content,
      chatId: chatId,
      userId: new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID),
      fromType: enums.message_from_types.SYSTEM,
      sendStatus: enums.message_send_statuses.QUEUED,
      data: {
        mark_as_seen_event: false
      },
      platform: req.platform
    }, chat.channel.company_id.toString(), chat.channel.id)

    ChatService.messageSetSortTime(chatId)

    const job = await SendMessageJob.add(message._id, new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID), agentId)

    await QueueService.publishToFifoWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, req.language)

    job.status = enums.job_statuses.QUEUED

    return job.save()
  },

  addSystemMessage: async (req, chat, type, content, data, template = false) => {

    const newChat = await Chat.findById(chat.id).populate('channel_id')

    if (!newChat) {
      throw new createError.NotFound(req.t('App.errors.conversation.not_found'))
    }

    if (!newChat.channel.is_active) {
      throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
    }

    // instagram üzerinden mesaj gönderimini herhangi bir engel olmadan sağlamak için
    if (template) {
      const sendable = await ChatService.checkSendable(newChat.id, newChat.channel.type)
      if (!sendable.sendable) {
        throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
      }
    }

    const message = await MessageRepo.create({
      type: type,
      content: content,
      data: data,
      chatId: newChat.id,
      userId: new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID),
      fromType: enums.message_from_types.SYSTEM,
      sendStatus: enums.message_send_statuses.QUEUED,
      time: moment().unix(),
      platform: req.platform
    }, newChat.channel.company_id.toString(), newChat.channel.id)

    newChat.sort_field = Date.now()
    await newChat.save()

    //system message da temp_id bilgisi kullanılmadığı için undefined o parametre.
    const job = await SendMessageJob.add(message._id, new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID), newChat.owner_user_id)

    await req.app.services.QueueService.publishToFifoWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, req.language)

    job.status = enums.job_statuses.QUEUED

    return job.save()
  },

  addInstagramSystemMessage: async (req, chatId, type, content, data) => {
    const chat = await Chat.findById(chatId).populate('channel_id').populate('last_message_id')

    if (chat.channel.is_active === false) {
      throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
    }

    const message = await MessageRepo.create({
      type: type,
      content: content,
      data: data,
      chatId: chat.id,
      userId: new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID),
      fromType: enums.message_from_types.SYSTEM,
      sendStatus: enums.message_send_statuses.QUEUED,
      time: moment().unix(),
      platform: req.platform
    }, chat.channel.company_id.toString(), chat.channel.id)

    chat.sort_field = Date.now()
    await chat.save()

    //system message da temp_id bilgisi kullanılmadığı için undefined o parametre.
    const job = await SendMessageJob.add(message._id, new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID), chat.owner_user_id)

    await req.app.services.QueueService.publishToFifoWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, req.language)

    job.status = enums.job_statuses.QUEUED

    await job.save()
  },

  addHelobotMessage: async (req, chatData, content, type) => {
    const newChat = await Chat.findById(chatData.id).populate('channel_id')

    if (!newChat) {
      throw new createError.NotFound(req.t('App.errors.conversation.not_found'))
    }

    if (!newChat.channel.is_active) {
      throw new createError.NotFound(req.t('App.errors.channel.is_not_active'))
    }

    const sendable = await ChatService.checkSendable(newChat.id, newChat.channel.type)

    if (!sendable.sendable) {
      throw new createError.BadRequest(req.t('App.errors.dash.conversation_active_time_expired'))
    }

    const message = await MessageRepo.create({
      type: type,
      content: content,
      chatId: newChat.id,
      userId: new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID),
      fromType: enums.message_from_types.SYSTEM,
      sendStatus: enums.message_send_statuses.QUEUED,
      data: {
        mark_as_seen_event: false
      },
      time: moment().unix(),
      platform: req.platform
    }, newChat.channel.company_id.toString(), newChat.channel.id)

    newChat.sort_field = Date.now()
    newChat.helobot_status = true
    await newChat.save()

    //system message da temp_id bilgisi kullanılmadığı için undefined o parametre.
    const job = await SendMessageJob.add(message._id, new mongoose.Types.ObjectId(process.env.SYSTEM_USER_ID), newChat.owner_user_id)

    await req.app.services.QueueService.publishToFifoWorker({
      job_id: job.id,
      trace_id: req.trace_id
    }, req.language)

    job.status = enums.job_statuses.QUEUED

    await job.save()

    const helobotChat = await HelobotHasChat.findOne({ chat_id: newChat._id, status: true })
    if (helobotChat) {
      await HelobotService.checkHeloBotTimeout(chatData.id, helobotChat.helobot_conversation_id, helobotChat.timeout)
    }

    const chat = await Chat.findById(newChat._id).populate('channel_id')

    const lastMessage = await Message.findById(chat.last_message_id).populate('user_id').populate('conversation_id')

    await QueueService.publishToAppSocket({
      event: enums.agent_app_socket_events.HELOBOT_CHAT,
      socket_rooms: [chat.channel._id.toString()],
      data: {
        chat_item: DashPresenter.getChatItem(chat, chat.lastMessage, chat.channel, undefined, chat.thinker_status, chat.helobot_status),
        message_item: await DashPresenter.getMessageItemCustomerOrAgent(lastMessage),
      }
    }, 'tr')

    return true
  },

  /**
   * @param req
   * @param agent
   * @param chat
   * @param channel
   * @param integration
   * @param chatCustomerProfileImage
   * @param chatOwnedByNewAgent
   *
   * @return {Promise<object>}
   */
  getChatDetail: async (req, stateDto, chat, channel, integration, chatCustomerProfileImage, chatOwnedByNewAgent = false, getForceChatData = {}, mobileApp = false) => {

    const chatData = chat.vData

    const selectedChat = {
      id: chat.id,
      type: channel.type, // deprecated
      channel_type: channel.type,
      channel_id: channel._id,
      channel_provider: channel.provider || '',
      channel_ext_id: channel.ext_id || channel.id,
      unread_message_count: chat.unread_message_count,
      lang_code: chat.lang_code, //todo: Bakılacak
      is_pinned: chat.pinned_at ? true : false,
      is_active: true,
      chat_langs: utils.getSelectedLangCodes(chatData.hasChatLangCode() ? chatData.getChatLangCode() : 'tr'),
      has_integration: false,
      is_blocked: chat.is_blocked || false,
      profile_pic: chatCustomerProfileImage,
      chat_referral: chat.chat_referral_id ? {
        id: chat.chat_referral_id.toString()
      } : null,
      integration: {
        id: '',
        langs: [],
        currencies: [],
        type: "",
        has_heloscope_integration: false
      },
      customer: {
        id: chat.id,
        name: chat.title,
        username: chat.username || '',
        title: helpers.getName(chat),
        is_paired: false,
        phone_number: channel.type === enums.channel_types.LIVE_CHAT ? '' : chat.phone_number || chat.ext_id || '',
        profile_image_url: chat.profile_pic || ''
      },
      cheid: helpers.encryptPassword(channel.ext_id, process.env.GET_CONTAINER_KEY),
      thinker_active: false,
      helobot_active: false
    }

    if (integration) {
      const integrationData = integration.vData
      selectedChat.has_integration = true
      selectedChat.integration.id = integration.id

      const chatIntegration = await IntegrationService.getOrCreateChatIntegration(chat, integration)
      selectedChat.customer.order_stage = chatIntegration.vData.getOrderStage()
      selectedChat.customer.is_paired = !!chatIntegration.ext_id

      switch (integration.type) {
        case enums.INTEGRATION_TYPES.HELOSCOPE:
          selectedChat.integration.langs = await HeloscopeService.getLangs(req.language, integration, chatIntegration, chatIntegration.ext_id, req.trace_id).catch(() => [])
          selectedChat.integration.currencies = integrationData.getCurrencyCodes()
          selectedChat.integration.type = enums.INTEGRATION_TYPES.HELOSCOPE
          selectedChat.integration.base_url = integrationData.getBaseUrl()
          selectedChat.integration.has_heloscope_integration = true
          break

        case enums.INTEGRATION_TYPES.TSOFT:
          selectedChat.integration.type = enums.INTEGRATION_TYPES.TSOFT
          selectedChat.integration.langs = utils.getIntegrationLangCodes(integrationData.getLangCodes(), chatIntegration.vData.hasIntegrationLangCode() ? chatIntegration.vData.getIntegrationLangCode() : 'tr')
          selectedChat.integration.currencies = utils.selectCurrencyCode(integrationData.getCurrencyCodes(), chatIntegration.vData.getIntegrationCurrency())
          break

        case enums.INTEGRATION_TYPES.SHOPIFY:
          selectedChat.integration.type = enums.INTEGRATION_TYPES.SHOPIFY
          selectedChat.integration.langs = ['tr', 'en']
          selectedChat.integration.currencies = await ShopifyService.getCurrencyCode(req, integration).catch(() => [])
          break
      }
    }

    if (getForceChatData.status) {
      await sendSocketMessage(
        enums.agent_app_socket_events.FORCE_TOOK_CHAT,
        [getForceChatData.owner_agent.vSocketCode],
        {
          chat_id: chat.id,
          message: req.t('App.errors.conversation.chat_received', {
            name: helpers.getName(stateDto.getUser()),
            chat_name: chat.title,
            interpolation: { escapeValue: false }
          })
        }, req.language)

      await ChatActions(req, enums.chat_actions.owner_forced_chat, { name: stateDto.getUserName() }, chat._id, [getForceChatData.owner_agent.vSocketCode])

    } else if (chatOwnedByNewAgent || stateDto.getCompany().vData.getAssignChatToAgent()) {
      // agent üzerine atama işleminde şirket içerisindeki bilgi göz önünde bulunduruluyor
      let socketRooms = [channel.id]
      if (stateDto.getCompany().vData.getTeamStatus() && stateDto.getUserType() !== enums.acl_roles.COMPANY_OWNER) {
        if (chat.team_id) {
          socketRooms = [chat.team_id.toString()]
        }
      }

      await sendSocketMessage(enums.agent_app_socket_events.CHAT_OWNED_BY_AGENT, socketRooms, {
        chat_id: chat.id,
        agent_id: stateDto.getUser().id
      }, req.language)
    }
    if (stateDto.getCompany().vData.getHidePhoneNumber()) {
      if (stateDto.getUser().type !== enums.acl_roles.COMPANY_OWNER) {
        selectedChat.customer.phone_number = ""
      }
    }

    const hasThinker = await ThinkerHasChat.findOne({ chat_id: chat._id, status: true })
    if (hasThinker) {
      selectedChat.thinker_active = true
    }

    const hasHelobot = await HelobotHasChat.findOne({ chat_id: chat._id, status: true })
    if (hasHelobot) {
      selectedChat.helobot_active = true
    } else {
      if (chat.helobot_status) {
        chat.helobot_status = false
        await chat.save()
      }
    }

    return selectedChat

  },

  teamMessage: async (req, chat, channel, company, chatText) => {
    let buttons = []
    if (company.vData.getTeamStatus()) {
      // aktif olan ekipler sadece mesaj olaran gönderilir
      for (const item of channel.vSettings.getWelcomeMessageActionsButtonsForTeam()) {
        const isTeamActive = await Team.findById(item.team_ids[0])
        if (isTeamActive.status) {
          buttons.push({
            id: item.id,
            title: item.button_text.substring(0, 23),
            description: '',
          })
        }
      }
    } else {
      buttons = channel.vSettings.getWelcomeMessageActionsButtonsForAgent().map(item => {
        return {
          id: item.id,
          title: item.button_text.substring(0, 23),
          description: '',
        }
      })
    }

    if (buttons.length === 0) {
      return
    }

    if (channel.type === enums.channel_types.WHATSAPP_NUMBER) {
      return ChatService.addSystemMessage(req, chat, enums.message_types.WHATSAPP_INTERACTIVE, {
        text: chatText,
        language: req.language,
        buttons: [{
          title: req.t('Global.chat_message.select', { lng: chat.vData.getChatLangCode() }),
          rows: buttons
        }],
        sub_type: enums.message_types.LIST
      }, { mark_as_seen_event: false })
    } else {

      chatText += '[BR][/BR]'
      for (let i = 0; i < buttons.length; i++) {
        chatText += utils.getMessageEmoji(i + 1, channel.type) + ` ${buttons[i].title}` + '[BR][/BR]'
      }

      return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
        text: chatText,
        language: req.language,
        bb_code: true,
        select_agent: company.vData.getTeamStatus() ? channel.vSettings.getWelcomeMessageActionsButtonsForTeam() : channel.vSettings.getWelcomeMessageActionsButtonsForAgent()
      }, { mark_as_seen_event: false })
    }
  },

  unarchiveMessage: async (req, createdChatMessage, chatItem) => {

    const channelSettings = createdChatMessage.getChannel().vSettings

    const timeToPass = channelSettings.getUnarchiveMessagingTimeout() * 60 * 1000

    if (new Date().getTime() > createdChatMessage.getChat().archived_at.getTime() + timeToPass) {

      // helobot veya thinker aktif ise burda sonlanacak
      if (createdChatMessage.getChat().helobot_status === true || createdChatMessage.getChat().thinker_status === true) {
        return
      }

      // Süreç Thinker'a aktarılıyor
      if (channelSettings.getUnarchiveThinkerStatus() && channelSettings.getUnarchiveThinkerFlowId()) {
        await ThinkerService.StartProcedure(createdChatMessage.getChat(), createdChatMessage.getCompany(), createdChatMessage.getChannel().id, channelSettings.getUnarchiveThinkerFlowId(), req.trace_id, createdChatMessage.getMessage().vContentText)

        chatItem.thinker_active = true

        return QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
          socket_rooms: [createdChatMessage.getChannel().id],
          data: {
            chat_id: createdChatMessage.getChat().id
          }
        }, 'tr')
      } else if (channelSettings.getUnarchiveHelobotStatus() && channelSettings.getUnarchiveHelobotKnowledgeBaseId()) {

        const helobotMessage = await HelobotService.BotReplyMessage(req, createdChatMessage.getCompany(), createdChatMessage.getMessage().vContentText, createdChatMessage.getChat(), createdChatMessage.getChannel(), true, channelSettings.getUnarchiveHelobotKnowledgeBaseId(), channelSettings.getUnarchiveHelobotTimeout())
        if (!helobotMessage) {
          return false
        }

        return QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.HELOBOT_STARTED,
          socket_rooms: [createdChatMessage.getChannel().id],
          data: {
            chat_id: createdChatMessage.getChat().id
          }
        }, 'tr')
      }

      let chatText = helpers.getUnarchiveMessage(req, channelSettings)

      const channel = createdChatMessage.getChannel()

      if (!channelSettings.getIsActiveDefaultUnarchiveMessages()) {

        let buttons = []
        if (channelSettings.getUnarchiveMessageActionsIsActive()) {
          if (createdChatMessage.getCompany().vData.getTeamStatus()) {

            for (const item of channelSettings.getUnarchiveMessageActionsButtonsForTeam()) {
              const isTeamActive = await Team.findById(item.team_ids[0])
              if (isTeamActive.status) {
                buttons.push({
                  id: item.id,
                  title: item.button_text.substring(0, 23),
                  description: '',
                })
              }
            }
          } else {
            buttons = channelSettings.getUnarchiveMessageActionsButtonsForAgent().map(item => {
              return {
                id: item.id,
                title: item.button_text.substring(0, 23),
                description: '',
              }
            })
          }

          if (buttons.length === 0) {
            return
          }

          if (channel.type === enums.channel_types.WHATSAPP_NUMBER) {
            return ChatService.addSystemMessage(req, createdChatMessage.getChat(), enums.message_types.WHATSAPP_INTERACTIVE, {
              text: chatText,
              language: req.language,
              buttons: [{
                title: req.t('Global.chat_message.select', { lng: createdChatMessage.getChat().vData.getChatLangCode() }),
                rows: buttons
              }],
              sub_type: enums.message_types.LIST
            }, { mark_as_seen_event: false })
          } else {

            chatText += '[BR][/BR]'
            for (let i = 0; i < buttons.length; i++) {
              chatText += utils.getMessageEmoji(i + 1, channel.type) + ` ${buttons[i].title}` + '[BR][/BR]'
            }

            return ChatService.addSystemMessage(req, createdChatMessage.getChat(), enums.message_types.TEXT, {
              text: chatText,
              language: req.language,
              bb_code: true,
              select_agent: createdChatMessage.getCompany().vData.getTeamStatus() ? channelSettings.getUnarchiveMessageActionsButtonsForTeam() : channelSettings.getUnarchiveMessageActionsButtonsForAgent()
            }, { mark_as_seen_event: false })
          }
        } else {
          let text = ''

          if (channelSettings.getIsActiveDefaultUnarchiveMessages()) {
            text = req.t('App.integration.send_out_of_hours_message')
          } else {

            const langCode = utils.getLangCodeAsShort(createdChatMessage.getChat().vData.getChatLangCode())

            if (channelSettings.getUnarchiveMessages()) {
              text = channelSettings.getUnarchiveMessages()[langCode] || ''
            }
          }

          if (text.length === 0) {
            return false
          }

          return ChatService.addSystemMessage(req, createdChatMessage.getChat(), enums.message_types.TEXT, {
            text: text
          }, { mark_as_seen_event: false })
        }
      } else {
        return ChatService.addSystemMessage(req, createdChatMessage.getChat(), enums.message_types.TEXT, {
          text: chatText
        }, { mark_as_seen_event: false })
      }
    }
  },

  addTeamMessage: (req, chat, channelType) => {

    let job = new Job()

    job.type = enums.job_types.TEAM_MESSAGE
    job.status = enums.job_statuses.CREATED
    job.data = {
      chat_id: chat.id,
      agent_id: chat.owner_user_id,
      channel_type: channelType,
      language: req.language
    }

    return job.save()
  },

  runTeamMessage: async (req, data) => {
    const chat = await Chat.findById(data.chat_id).populate('channel_id')
    const company = await Company.findById(chat.channel.company_id)

    await ChatService.teamMessage(req, chat, chat.channel, company, helpers.getFirstWelcomeMessage(req, chat.channel.vSettings))
  },

  messageSetSortTime: async (chatId) => {
    const chat = await Chat.findById(chatId)
    chat.sort_field = Date.now()
    await chat.save()
  },

  helobotTimeout: async (req, data) => {
    const isActiveHelobot = await HelobotHasChat.findOne({
      chat_id: new mongoose.Types.ObjectId(data.chat_id),
      helobot_conversation_id: data.helobot_conversation_id,
      status: true
    })
    if (!isActiveHelobot) {
      return
    }

    const lastMessage = await Message.findOne({
      conversation_id: new mongoose.Types.ObjectId(data.chat_id),
      from_type: enums.message_from_types.SYSTEM
    }).sort({ _id: -1 })

    if (lastMessage) {
      const chat = await Chat.findById(data.chat_id)

      if (chat.helobot_status) {
        // helobot u bitirme işlemi yapılır
        if (moment().diff(moment(lastMessage.created_at), 'seconds') >= (isActiveHelobot.timeout * 60)) {
          chat.helobot_status = false
          await chat.save()

          isActiveHelobot.status = false
          await isActiveHelobot.save()

          let socketRooms = []
          if (chat.owner_user_id) {
            const user = await User.findOne({ _id: chat.owner_user_id, deleted_at: { $exists: false } })

            socketRooms.push(user.vSocketCode)
          } else {
            socketRooms.push(chat.channel_id.toString())
          }

          await ChatActions(req, enums.chat_actions.helobot_timeout, {}, chat._id, socketRooms, false)

          await QueueService.publishToAppSocket({
            event: enums.agent_app_socket_events.HELOBOT_STOP,
            socket_rooms: socketRooms,
            data: {
              chat_id: data.chat_id
            }
          }, 'tr')
        }
      }
    }
  },

  SendManuelChatMessage: async (req, chat) => {
    try {
      if (chat.channel_id.vSettings.getManuelArchiveThinkerStatus() && chat.channel_id.vSettings.getManuelArchiveThinkerFlowId()) {
        const lastMessage = await Message.findOne({
          conversation_id: chat._id,
          from_type: enums.message_from_types.CUSTOMER
        }).sort({ created_at: -1 })

        await ThinkerService.StartProcedure(chat, chat.channel_id.company_id, chat.channel_id._id.toString(), chat.channel_id.vSettings.getManuelArchiveThinkerFlowId(), req.trace_id, lastMessage.vContentText, 'now')

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.CHAT_HIDE,
          socket_rooms: [chat.channel_id.id],
          data: {
            chat_id: chat.id
          }
        }, req.language)

        return QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.THINKER_BOT_STARTED,
          socket_rooms: [chat.channel_id.id],
          data: {
            chat_id: chat.id
          }
        }, req.language)
      } else {
        const langCode = utils.getLangCodeAsShort(chat.vData.getChatLangCode())

        let text = helpers.getManuelArchiveMessage(req, langCode, chat.channel_id.vSettings)

        return ChatService.addSystemMessage(req, chat, enums.message_types.TEXT, {
          text: text
        }, { mark_as_seen_event: false })
      }
    } catch (error) {
      pino.error({
        trace_id: req.trace_id,
        message: error.message,
        data: JSON.stringify({
          chat_id: chat.id
        }),
        timestamp: new Date()
      })
    }
  },

  addAbandonedCartJob: async (req, abandonedCartChatId, chat, hour) => {
    let jobDoc = new Job()

    jobDoc.type = enums.job_types.ABANDONED_CART_MESSAGE
    jobDoc.status = enums.job_statuses.CREATED
    jobDoc.data = {
      abandoned_cart_chat_id: abandonedCartChatId,
      chat_id: chat.id,
      trace_id: req.trace_id,
      language: chat.vData.getChatLangCode(),
      hour: hour * 1000 * 60 * 60 // saat olarak işlem yapılıyor
    }

    await jobDoc.save()

    await QueueService.publishToDelayedWorker({
      job_id: jobDoc.id
    }, req.language, jobDoc.data.hour)
  },

  abandonedCartProcess: async (req, data) => {
    const hasAbandonedCartHasChat = await AbandonedCartHasChat.findOne({
      _id: new mongoose.Types.ObjectId(data.abandoned_cart_chat_id),
      is_active: true
    }).populate('chat_id').populate('abandoned_cart_id')
    if (!hasAbandonedCartHasChat) {
      return
    }

    const channel = await Channel.findById(hasAbandonedCartHasChat.chat.channel_id)

    const sendable = await ChatService.checkSendable(hasAbandonedCartHasChat.chat._id, channel.type)
    if (sendable.sendable) {
      await ChatService.addSystemMessage(
        req,
        hasAbandonedCartHasChat.chat,
        enums.message_types.TEXT,
        {
          text: hasAbandonedCartHasChat.abandoned_cart.data.messages[data.language]
        },
        {
          mark_as_seen_event: true,
          tag: sendable.isTag ? enums.message_types.HUMAN_AGENT : false
        }
      )

      hasAbandonedCartHasChat.sended_time = new Date()
    }

    hasAbandonedCartHasChat.is_active = false
    hasAbandonedCartHasChat.data = {
      language: data.language
    }
    hasAbandonedCartHasChat.markModified('data')
    await hasAbandonedCartHasChat.save()

    MetaEventService.SendEvent(channel, hasAbandonedCartHasChat.chat, enums.data_set_events.CartAbandoned, data.trace_id)
  },

  SendHeloBotProductMessages: async (req, helobotMessage, chat) => {
    if (helobotMessage.products.length > 0) {
      await ChatService.addHelobotMessage(req, chat, {
        text: helobotMessage.text,
        bb_code: true
      }, enums.message_types.TEXT)

      await helpers.sleepFunction(700)

      for (const item of helobotMessage.products) {
        // resimli mesaj 
        await ChatService.addHelobotMessage(req, chat, {
          caption: item.title + '[BR][/BR][BR][/BR]' + item.url,
          url: item.image,
          redirect_url: item.url,
          bb_code: true
        }, enums.message_types.IMAGE_URL)
      }
    } else {
      await ChatService.addHelobotMessage(req, chat, {
        text: helobotMessage.text,
        bb_code: true
      }, enums.message_types.TEXT)
    }
  }
}

module.exports = ChatService
