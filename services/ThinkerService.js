const { default: axios } = require('axios')
const moment = require('moment')
const jsonwebtoken = require('jsonwebtoken')
const pino = require('pino')()

const helpers = require('../libs/helpers')
const enums = require('../libs/enums')
const utils = require('../libs/utils')

const Thinker = require('../models/Thinker')
const User = require('../models/User')
const ThinkerHasChat = require('../models/ThinkerHasChat')
const CompanyHasPackage = require('../models/CompanyHasPackage')

const QueueService = require('../services/QueueService')
const UserService = require('../services/UserService')

async function retryLogin(thinker, traceId) {
  const realPassword = helpers.decrypt(process.env.APP_SECRET_KEY, thinker.company_id.toString(), thinker.password)

  const loginData = await ThinkerService.Login(thinker.email, realPassword, traceId)

  thinker.token = loginData.data.token
  await thinker.save()
}

async function checkTokenExpireTime(thinker, traceId) {
  const { payload } = jsonwebtoken.decode(thinker.token, { complete: true })
  const currentTime = moment().unix();

  if (payload.exp < currentTime) {
    await retryLogin(thinker, traceId)
  }
}

async function ThinkerUserProcess(companyId) {
  let hasThinkerUser = await User.findOne({ company_id: companyId, type: enums.acl_roles.THINKER_USER, deleted_at: { $exists: false } })
  if (!hasThinkerUser) {
    hasThinkerUser = await new User({
      type: enums.acl_roles.THINKER_USER,
      name: 'Thinker Root',
      first_name: 'Thinker',
      last_name: 'Root',
      company_id: companyId,
      is_active: true,
      socket_code: utils.generateHash(10)
    }).save()

    const hash = utils.generateHash(12)

    const userData = hasThinkerUser.vData
    userData.setThinkerSalt(hash)
    hasThinkerUser.data = userData.getData()
    hasThinkerUser.markModified('data')
  }

  return hasThinkerUser.save()
}

const ThinkerService = {

  Register: async (companyName, userName, email, password, agentLangCode, traceId) => {
    const config = {
      url: process.env.THINKER_API_BASE_URL + '/register',
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'X-Trace-ID': traceId
      },
      data: {
        company_name: companyName,
        name: userName,
        email: email,
        password: password
      },
    }

    return await axios.request(config).catch(err => {
      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  Login: async (email, password, traceId, agentLangCode = 'tr') => {
    const config = {
      url: process.env.THINKER_API_BASE_URL + '/login',
      headers: {
        'Accept-Language': agentLangCode,
        'X-Trace-ID': traceId
      },
      method: 'POST',
      data: {
        email: email,
        password: password
      }
    }

    return await axios.request(config).then((response) => response.data).catch(err => {
      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  Credential: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/client-credential',
      method: 'POST',
      data: {}
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetFlow: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow',
      method: 'GET',
      data: {}
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddFlow: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  DeleteFlow: async (thinker, agentLangCode, flow_id, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/' + flow_id,
      method: 'DELETE',
      data: {}
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  WebhookUrlToFlow: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/url',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  DeleteWebhookUrl: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/url',
      method: 'DELETE',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  StartFlow: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        "Accept-Language": agentLangCode,
        "Authorization": `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/start',
      method: "POST",
      data: data
    }

    return await axios.request(config).then(response => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  StopFlow: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        "Accept-Language": agentLangCode,
        "Authorization": `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/stop',
      method: "POST",
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        force_stop: err.response?.status ? true : false,
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddMessageUrl: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/message/url',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  DeleteMessageUrl: async (thinker, agentLangCode, data, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/message/url',
      method: 'DELETE',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  SendMessageWebhookToThinker: async (chatLang, chatId, data, credential, traceId) => {

    const timestamp = moment().unix()

    const hmac = helpers.createdHmac(credential.secret_hash, credential.secret_id, timestamp)

    const config = {
      headers: {
        'Accept-Language': chatLang,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_WEBHOOK_BASE_URL + `/webhook/message?hash=${credential.secret_id}&hmac=${hmac}&timestamp=${timestamp}`,
      method: 'POST',
      data: data
    }

    axios.request(config).then(response => {
      pino.info({
        message: 'Thinker a mesaj Gönderildi',
        chat_id: chatId,
        timestamp: new Date(),
        data: JSON.stringify(response.data)
      })
    }).catch(err => {
      pino.error({
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu',
        timestamp: new Date(),
      })
    })
  },

  GetMessageUrls: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/message/url',
      method: 'GET',
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetFlowByChannel: async (thinker, agentLangCode, channel, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/channel?channel=' + channel,
      method: 'GET'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetFlowById: async (thinker, flowId, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/' + flowId,
      method: 'GET'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  StartProcedure: async (chat, company, channelId, flowId, traceId, textMessage, startType = 'wait') => {
    const thinker = await Thinker.findOne({ company_id: company._id, status: true })

    // şirkete ait thinker aktif mi değil mi kontrol ediliyor
    if (thinker) {
      const companyPackage = await CompanyHasPackage.findOne({
        company_id: company._id,
        deleted_at: {
          $exists: false
        }
      })
      if (companyPackage) {
        const status = helpers.isModuleTimeOut(companyPackage.data.thinker)
        if (status) {
          return
        }
      }

      const isChatThinker = await ThinkerHasChat.findOne({ chat_id: chat._id, status: true })
      // eğer chat in akışı başlamadıysa akış başlatma işlemi yapmamız gerekiyor
      if (!isChatThinker) {
        const response = await ThinkerService.StartFlow(thinker, chat.vData.getChatLangCode(), {
          flow_id: flowId,
          start_type: startType,
          chat_id: chat.id,
          data: {
            chat_ext_id: chat.ext_id,
            chat_name: chat.title,
            message: textMessage
          }
        }, traceId)

        await new ThinkerHasChat({
          chat_id: chat._id,
          start_type: startType,
          started_flow_id: response.data.id,
          status: true,
          thinker_id: thinker._id,
          flow_id: flowId
        }).save()

        chat.thinker_status = true
        await chat.save()

        await QueueService.publishToAppSocket({
          event: enums.agent_app_socket_events.CHAT_OWNED_BY_AGENT,
          socket_rooms: [channelId],
          data: {
            chat_id: chat.id,
            agent_id: '' // hiç kimseye gitmeyeceğini gösterir. amaç chati sadece bekleyenden almak
          }
        })
      }
    }
  },

  SendMessage: async (chat, company, channel, message, traceId) => {
    const thinker = await Thinker.findOne({ company_id: company._id, status: true })
    // şirkete ait thinker aktif mi değil mi kontrol ediliyor
    if (!thinker) {
      return
    }

    const isChatThinker = await ThinkerHasChat.findOne({ chat_id: chat._id, status: true })
    if (!isChatThinker) {
      return
    }

    const companyPackage = await CompanyHasPackage.findOne({
      company_id: company._id,
      deleted_at: {
        $exists: false
      }
    })
    if (companyPackage) {
      const status = helpers.isModuleTimeOut(companyPackage.data.thinker)
      if (status) {
        isChatThinker.status = false
        await isChatThinker.save()
        return
      }
    }

    let text = null
    switch (channel.type) {
      case enums.channel_types.WHATSAPP_NUMBER:
        if (message.vContent.interactive) {
          if (message.vContent.interactive.button_reply) {
            text = message.vContent.interactive.button_reply.title
          } else {
            text = message.vContent.interactive.list_reply.title
          }
        } else {
          text = message.vContentText || message.vContentCaption || message.vContent.button?.text
        }
        break
      case enums.channel_types.FACEBOOK_PAGE:
        text = message.vContentText
        break
      case enums.channel_types.INSTAGRAM_ACCOUNT:
        text = message.vContentText
        break
      case enums.channel_types.LIVE_CHAT:
        text = message.vContentText || message.vContentCaption
        break
    }

    if (!text) {
      // return
      text = ''
    }

    ThinkerService.SendMessageWebhookToThinker(chat.vData.getChatLangCode(), chat.id, {
      id: isChatThinker.started_flow_id,
      text: text
    }, thinker.credential, traceId)
  },

  GetStartedFlows: async (thinker, data, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/started-flow',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  StopAll: async (thinker, data, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow/stop-all',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetApps: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/apps',
      method: 'GET',
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  RegisterApp: async (thinker, data, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/apps/register',
      method: 'POST',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  UnRegisterApp: async (thinker, data, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/apps/unregister',
      method: 'DELETE',
      data: data
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetChannels: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/company-channels',
      method: 'GET'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddWhatsappChannel: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/company-channels',
      method: 'POST',
      data: {
        channel_id: '3d539211-f3cc-48a2-9b58-bc3c6a01210f'
      }
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddFacebookChannel: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/company-channels',
      method: 'POST',
      data: {
        channel_id: '220c849c-dede-4908-9c18-173989467306'
      }
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddInstagramChannel: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/company-channels',
      method: 'POST',
      data: {
        channel_id: 'd0f4cc72-5ada-4f5c-9289-3430136f1f6a'
      }
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddLiveChatChannel: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/company-channels',
      method: 'POST',
      data: {
        channel_id: 'c854e538-efd6-422d-a0a5-f5c57a2a1afd'
      }
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetFlowCategories: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow-category',
      method: 'GET',
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  GetFlowStore: async (thinker, body, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow-store/get',
      method: 'POST',
      data: body
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  AddFlowFromStore: async (thinker, body, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/flow-store/flow',
      method: 'POST',
      data: body
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },

  RegisterHeloRoboApp: async (companyId, thinkerAccount, langcode, traceId) => {
    const thinkerUser = await ThinkerUserProcess(companyId)

    await ThinkerService.RegisterApp(thinkerAccount, {
      id: '741539e1-55bb-4fe7-a086-02b51601c98d', // thinker tarafındaki HeloRobo id si
      data: {
        token: 'Bearer ' + UserService.getJwtToken(thinkerUser, thinkerUser.vData.getThinkerSalt())
      }
    }, langcode, traceId).catch(() => false)
  },

  RegisterMessageTemplateApp: async (token, thinkerAccount, langcode, traceId) => {
    await ThinkerService.RegisterApp(thinkerAccount, {
      id: '45d9d4db-0377-4a84-9b9c-59214ff21fcf', // thinker tarafındaki Message Template id si
      data: {
        token: 'Bearer ' + token
      }
    }, langcode, traceId).catch(() => false)
  },
  GetStartedFlowDetail: async (thinker, body, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/started-flow/get',
      method: 'POST',
      data: body
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  GetGooogleAccountList: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/accounts',
      method: 'GET',
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  GetGooogleOauth2: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/oauth2/url',
      method: 'GET',
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  DeleteGoogleAccount: async (thinker, id, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/account/' + id,
      method: 'DELETE'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  CreateGoogleAccountSheet: async (thinker, body, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/sheet',
      method: 'POST',
      data: body
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  GetGoogleAccountSheetList: async (thinker, id, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/sheet/' + id,
      method: 'GET'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  GetGoogleSheetList: async (thinker, agentLangCode, traceId) => {
    await checkTokenExpireTime(thinker, traceId)

    const config = {
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${thinker.token}`,
        'X-Trace-ID': traceId
      },
      url: process.env.THINKER_API_BASE_URL + '/google/sheet',
      method: 'GET'
    }

    return await axios.request(config).then((response) => response.data).catch(async err => {
      if (err.response?.status === 401) {
        return retryLogin(thinker)
      }

      throw {
        message: 'Thinker: ' + err?.response?.data?.message || 'Thinker: Beklenmedik Bir Hata Oluştu'
      }
    })
  },
  CompanyStopFlows: async (companyId, traceId) => {
    try {
      const thinker = await Thinker.findOne({
        company_id: companyId,
        deleted_at: {
          $exists: false
        }
      })
      if (!thinker) {
        return
      }

      const activeFlows = await ThinkerHasChat.find({
        thinker_id: thinker._id,
        status: true,
      })

      for (const item of activeFlows) {
        const status = await ThinkerService.StopFlow(thinker, 'tr', { id: item.started_flow_id }, traceId).catch((error) => {
          pino.error({
            trace_id: traceId,
            timestamp: new Date(),
            data: JSON.stringify({
              chat_id: item.chat_id.toString(),
              thinker_has_chat_id: item.id
            }),
            message: 'akışı durdurma işlemi sırasında hata oluştu',
            error: error.message
          })

          return false
        })
        if (status === false) {
          continue
        }

        item.status = false
        await item.save()
      }
    } catch (error) {
      pino.error({
        trace_id: traceId,
        timestamp: new Date(),
        message: 'CompanyStopFlows toplu durdurma işlemi sırasında hata oluştu',
        error: error.message
      })
    }
  }
}

module.exports = ThinkerService
