const { default: axios } = require('axios')

const readAuth = Buffer.from(`${process.env.ELASTICSEARCH_USERNAME}:${process.env.ELASTICSEARCH_PASSWORD}`).toString('base64')

const adminAuth = Buffer.from(`${process.env.ELASTICSEARCH_ADMIN_USERNAME}:${process.env.ELASTICSEARCH_ADMIN_PASSWORD}`).toString('base64')

const ElasticsearchService = {
  filter: async (from, perpage, text, company_id, channelIds) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/messages/_search`, {
      sort: [
        {
          timestamp: 'desc'
        }
      ],
      from: from,
      size: perpage,
      query: {
        bool: {
          must: [
            {
              match: {
                company_id: company_id
              }
            },
            {
              terms: {
                channel_id: channelIds
              }
            }
          ],
          filter: [
            {
              wildcard: {
                text: `*${text}*`
              }
            }
          ]
        }
      }
    }, {
      headers: {
        Authorization: 'Basic ' + readAuth
      }
    }).then(response => response.data)
  },

  filterChats: async (from, perpage, text, channelIds) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/chats/_search`, {
      sort: [
        {
          timestamp: 'desc'
        }
      ],
      from: from,
      size: perpage,
      query: {
        bool: {
          must: [
            {
              terms: {
                channel_id: channelIds
              }
            }
          ],
          should: [
            {
              match_phrase_prefix: {
                title: text
              }
            },
            {
              match_phrase: {
                title: text
              }
            },
            {
              wildcard: {
                'title.keyword': `*${text}*`
              }
            },
            {
              wildcard: {
                ext_id: `*${text}*`
              }
            },
            {
              wildcard: {
                username: `*${text}*`
              }
            }
          ],
          minimum_should_match: 1
        }
      }
    }, {
      headers: {
        Authorization: 'Basic ' + adminAuth
      }
    }).then(response => response.data.hits.hits)
  },

  addChat: async (data) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/chats/_doc`, data, {
      headers: {
        Authorization: 'Basic ' + adminAuth
      }
    }).then(response => response.data)
  },

  getChat: async (chatId) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/chats/_search`, {
      query: {
        term: {
          'id.keyword': chatId
        }
      },
      size: 1
    }, {
      headers: {
        Authorization: 'Basic ' + adminAuth
      }
    }).then(response => response.data.hits.hits)
  },

  updateChat: async (id, data) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/chats/_update/${id}`, {
      doc: data
    }, {
      headers: {
        Authorization: 'Basic ' + adminAuth
      }
    }).then(response => response.data)
  },

  SendLogMessage: async (data) => {
    return await axios.post(`${process.env.ELASTICSEARCH_CONN_STRING}/messages/_doc`, data, {
      headers: {
        Authorization: 'Basic ' + adminAuth
      }
    }).then(response => response.data)
  }
}

module.exports = ElasticsearchService