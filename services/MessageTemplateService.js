const { default: axios } = require('axios')
const pino = require('pino')()

const enums = require('../libs/enums')
const helpers = require('../libs/helpers')

const Channel = require('../models/Channel')
const Container = require('../models/Container')

const MessageTemplateService = {
  Register: (companyName, name, email, password, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/auth/register`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'X-Trace-ID': traceId
      },
      data: {
        company_name: companyName,
        email: email,
        name: name,
        password: password
      }
    }

    return axios.request(config).then(response => response.data)
  },
  Login: (email, password, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/auth/login`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'X-Trace-ID': traceId
      },
      data: {
        email: email,
        password: password
      }
    }

    return axios.request(config).then(response => response.data)
  },
  AddChannel: (messageTemplate, channel, container, agentLangCode, traceId) => {
    const data = {
      name: channel.name,
      number: channel.ext_id,
      whatsapp_account_id: channel.vSettings.getWabaId(),
      container: {
        domain: container.domain,
        port: container.port,
        number: container.number,
        code: container.code,
        password: helpers.decrypt(process.env.APP_SECRET_KEY, container.password_salt, container.password)
      }
    }

    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  AddCloudChannel: (messageTemplate, channel, agentLangCode, traceId) => {
    const data = {
      name: channel.name,
      number: channel.ext_id,
      whatsapp_account_id: channel.vSettings.getWabaId(),
      phone_number_id: channel.vSettings.getPhoneNumberId(),
    }

    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels/cloud`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  GetChannels: (messageTemplate, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },
  UpdateChannel: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels`,
      method: 'PUT',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  UpdateCloudChannel: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels/cloud`,
      method: 'PUT',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  GetMessageTemplateList: (messageTemplate, channelId, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates?channel_id=` + channelId,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },
  GetMessageTemplateListNonParameters: (messageTemplate, channelId, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/non-parameters?channel_id=` + channelId,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },
  CreateMessageTemplate: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  CreateMessageTemplateV2: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v2/message-templates`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  DeleteMessageTemplate: (messageTemplate, id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/` + id,
      method: 'DELETE',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: {}
    }

    return axios.request(config).then(response => response.data)
  },
  CheckTemplateName: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/check-name`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: {
        name: data.name,
        channel_id: data.channel_id
      }
    }

    return axios.request(config).then(response => response.data)
  },
  SendWizardStart: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/start`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  SendWizardStartV2: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v2/send-wizards/start`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  SendWizardGetReports: (messageTemplate, data, agentLangCode, traceId) => {
    const query = Object.entries(data).map(([key, value]) => `${key}=${value}`).join('&')

    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/reports?` + query,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },
  SendWizardGetReportDetails: (messageTemplate, id, data, agentLangCode, traceId) => {
    const query = Object.entries(data).map(([key, value]) => `${key}=${value}`).join('&')

    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/` + id + `/details?` + query,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },
  SendWizardGetAllReportDetails: (messageTemplate, id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/` + id + `/all-details`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },

  SingleMessage: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  SingleMessageV2: (messageTemplate, data, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v2/send`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: data
    }

    return axios.request(config).then(response => response.data)
  },
  GetChannel: (messageTemplate, phoneNumber, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels/phone-number/` + phoneNumber,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
    }

    return axios.request(config).then(response => response.data)
  },

  GetCategories: (messageTemplate, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/categories`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
    }

    return axios.request(config).then(response => response.data)
  },

  GetLangs: (messageTemplate, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/langs`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
    }

    return axios.request(config).then(response => response.data)
  },

  GetTemplate: (messageTemplate, template_id, channel_id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/` + template_id + `?channel_id=` + channel_id,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
    }

    return axios.request(config).then(response => response.data)
  },

  GetWabaStatus: (messageTemplate, channel_id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/waba-status?channel_id=` + channel_id,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
    }

    return axios.request(config).then(response => response.data)
  },

  SendWizardReportChart: (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/reports/chart`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  },

  SendWizardReportChartExcel: (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/reports/chart/excel`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  },

  SendWebhook: async (sendWizardId, messageData, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/webhooks/replies`,
      method: 'POST',
      headers: {
        'X-Trace-ID': traceId
      },
      data: {
        send_wizard_id: sendWizardId,
        message: messageData
      }
    }

    return axios.request(config).then(response => response.data)
  },

  GetChannelsByNumber: async (messageTemplate, numbers, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels/numbers`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: {
        numbers: numbers
      }
    }

    return axios.request(config).then(response => response.data)
  },

  SendMessageStatusWebhook: async (messageId, action, webhookResponse, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/webhooks/update-item`,
      method: 'POST',
      headers: {
        'X-Trace-ID': traceId
      },
      data: {
        message_id: messageId,
        message_status: action,
        webhook_response: webhookResponse
      }
    }

    axios.request(config).then(() => {
      pino.info({
        trace_id: traceId,
        message: 'Message Template Projesine gönderildi',
        webhook_data: JSON.stringify(webhookResponse)
      })
    }).catch(err => {
      pino.error({
        trace_id: traceId,
        config: JSON.stringify(config),
        message: 'Message Template Projesine gönderilemedi',
        webhook_data: JSON.stringify(webhookResponse),
        error: JSON.stringify(err.response?.data || { message: 'İstek Atılamadı' })
      })
    })
  },

  AddHeloRoboChannels: async (companyId, isMessageTemplateAccount, langcode, traceId) => {
    const channels = await Channel.find({ company_id: companyId, type: enums.channel_types.WHATSAPP_NUMBER, deleted_at: { $exists: false } })
    for (const item of channels) {
      if (!item.vSettings.getWabaId()) {
        pino.info({
          trace_id: traceId,
          message: item.id + ' waba Id bilgisi yok',
          timestamp: new Date(),
          data: JSON.stringify({
            channel_id: item.id,
            number: item.ext_id
          })
        })
        continue
      }

      if (item.provider === enums.channel_providers.TEKROM) {
        const container = await Container.findOne({ number: item.ext_id, deleted_at: { $exists: false } })
        if (!container) {
          pino.info({
            trace_id: traceId,
            message: item.id + ' container bulunamadı',
            timestamp: new Date(),
            data: JSON.stringify({
              channel_id: item.id,
              number: item.ext_id
            })
          })
          continue
        }

        await MessageTemplateService.AddChannel(isMessageTemplateAccount, item, container, langcode, traceId).catch((err) => {
          pino.error({
            trace_id: traceId,
            message: err.message,
            timestamp: new Date(),
            response: JSON.stringify(err.response?.data || {}),
            data: JSON.stringify({
              channel_id: item.id,
              number: item.ext_id
            })
          })
        })
      } else {
        await MessageTemplateService.AddCloudChannel(isMessageTemplateAccount, item, langcode, traceId).catch((err) => {
          pino.error({
            trace_id: traceId,
            message: err.message,
            timestamp: new Date(),
            response: JSON.stringify(err.response?.data || {}),
            data: JSON.stringify({
              channel_id: item.id,
              number: item.ext_id
            })
          })
        })
      }
    }
  },

  SendWizardResume: async (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/re-start`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  },

  SendWizardStop: async (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/send-wizards/stop`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  },

  DeleteChannel: async (messageTemplate, number, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/channels`,
      method: 'DELETE',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: {
        number: number
      }
    }

    return axios.request(config).then(response => response.data)
  },

  GetDailyStatistics: async (messageTemplate, channel_id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/message-templates/daily-statistics?channel_id=${channel_id}`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },

  CreateGoogleSheetContact: async (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/google-sheet`,
      method: 'POST',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  },

  GetGoogleSheetContacts: async (messageTemplate, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/google-sheet`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },

  GetGoogleSheetContact: async (messageTemplate, id, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/google-sheet/${id}`,
      method: 'GET',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      }
    }

    return axios.request(config).then(response => response.data)
  },

  DeleteGoogleSheetContact: async (messageTemplate, body, agentLangCode, traceId) => {
    const config = {
      url: `${process.env.MESSAGE_TEMPLATE_V3_URL}/api/v1/google-sheet`,
      method: 'DELETE',
      headers: {
        'Accept-Language': agentLangCode,
        'Authorization': `Bearer ${messageTemplate.token}`,
        'X-Trace-ID': traceId
      },
      data: body
    }

    return axios.request(config).then(response => response.data)
  }
}

module.exports = MessageTemplateService