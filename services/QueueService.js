const RabbitMQConnection = require('../modules/RabbitMQConnection')

const __publishToSocket = (data, language, queueName) => {

  data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

  data = Object.assign({ language: language }, data)

  const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

  return RabbitMQChannelWrapper.sendToQueue(queueName, data)

}

const QueueService = {

  /**
   * @param data
   * @param language
   * @param delayMs
   *
   * @return {Promise<void>}
   */
  publishToDelayedWorker: (data, language, delayMs) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.publish(process.env.RABBITMQ_WORKER_QUEUENAME + '.delayed', undefined, data, {
      headers: {
        'x-delay': delayMs
      }
    })

  },

  publishToCustomerEvaluation: (data, language, delayMs) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.publish(process.env.RABBITMQ_WORKER_QUEUENAME + '.delayed', undefined, data, {
      headers: {
        'x-delay': delayMs
      }
    })

  },

  publishToTeamMessage: (data, language, delayMs) => {
    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.publish(process.env.RABBITMQ_WORKER_QUEUENAME + '.delayed', undefined, data, {
      headers: {
        'x-delay': delayMs
      }
    })

  },

  publishToWorker: (data, language) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.sendToQueue(process.env.RABBITMQ_WORKER_QUEUENAME, data)

  },

  publishToAppSocket: (data, language) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    return __publishToSocket(data, language, process.env.RABBITMQ_SOCKET_QUEUENAME)

  },

  publishToLiveChatSocket: (data, language) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    return __publishToSocket(data, language, process.env.RABBITMQ_LIVE_CHAT_SOCKET_QUEUENAME)

  },

  publishToFifoWorker: (data = {}, language) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.sendToQueue(process.env.RABBITMQ_WORKER_FIFO_QUEUENAME, data)

  },

  publishToFifoMessageLog: (data = {}) => {
    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.sendToQueue(process.env.RABBITMQ_WORKER_FIFO_MESSAGE_LOG_QUEUENAME, data)
  },

  publishToFifoChat: (data = {}) => {
    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.sendToQueue(process.env.RABBITMQ_WORKER_FIFO_CHAT_QUEUENAME, data)
  },

  publishToCommentsWorker: (data = {}, language) => {

    data = JSON.parse(JSON.stringify(typeof data === 'object' ? data : {}))

    data = Object.assign({ language: language }, data)

    const RabbitMQChannelWrapper = RabbitMQConnection.getChannelWrapper()

    return RabbitMQChannelWrapper.sendToQueue(process.env.RABBITMQ_WORKER_MARKETING, data)

  }

}

module.exports = QueueService
