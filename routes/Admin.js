const express = require('express')
const CheckAuth = require('../middlewares/Noc/CheckAuth')
const CheckPermission = require('../middlewares/Noc/CheckPermission')
const Validation = require('../middlewares/Noc/Validation')

const router = express.Router()

/**
 * @type {*|Router}
 */

/**
 * Auth
 */
router.post('/api/v1/auth', require('../controllers/Noc/Auth'))
router.post('/api/v1/auth/2fa/status', require('../controllers/Noc/2FA/2FAStatus'))
router.post('/api/v1/auth/2fa', CheckAuth, CheckPermission, require('../controllers/Noc/2FA/Set2FA'))
router.post('/api/v1/2fa/qrcode', CheckAuth, CheckPermission, require('../controllers/Noc/2FA/GetQRCode'))

/**
 * Company Routes
 */
router.post('/api/v1/companies/list', Validation.CheckList, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').List)
router.get('/api/v1/companies/:id', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').Get)
router.post('/api/v1/companies', Validation.CompanyCreate, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').Create)
router.post('/api/v1/companies/:id/edit', Validation.CompanyEditPost, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').Edit)
router.get('/api/v1/companies/:id/edit', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').Edit)
router.get('/api/v1/companies/status-change/:id', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').StatusChange)
router.get('/api/v1/companies/teams/all', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').GetCompanyTeam)
router.get('/api/v1/companies/:id/package', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').GetPackage)
router.post('/api/v1/companies/package', Validation.SetPackage, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').SetPackage)
router.put('/api/v1/companies/package', Validation.EditPackage, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').EditPackage)
router.delete('/api/v1/companies/package', Validation.DeletePackage, CheckAuth, CheckPermission, require('../controllers/Noc/Companies').DeletePackage)
router.get('/api/v1/companies/:id/billtekrom/sync', CheckAuth, CheckPermission, require('../controllers/Noc/Companies').SyncBilltekrom)

/**
 * Channel Routes
 */
router.get('/api/v1/channels', Validation.ChannelListAll, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').ListAll)
router.get('/api/v1/companies/:id/channels', Validation.ChannelList, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').List)
router.get('/api/v1/companies/:id/channels/new', CheckAuth, CheckPermission, require('../controllers/Noc/NewChannel'))
router.delete('/api/v1/channels/:id', CheckAuth, CheckPermission, require('../controllers/Noc/Channels').Delete)
router.post('/api/v1/companies/:id/channels/new', Validation.NewChannelPost, CheckAuth, CheckPermission, require('../controllers/Noc/NewChannel'))
router.get('/api/v1/channels/:id/edit', CheckAuth, CheckPermission, require('../controllers/Noc/Channels').Edit)
router.post('/api/v1/channels/:id/edit', Validation.ChannelEditPost, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').Edit)
router.get('/api/v1/channels/uncompleted', Validation.ChannelUnCompleted, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').UnCompleted)
router.post('/api/v1/channels/:id/completed', Validation.ChannelCompleted, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').Completed)
router.post('/api/v1/channel/chat/archived', Validation.ChannelChatArchived, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').ChannelChatArchived)
router.get('/api/v1/companies/:id/remove-token/:channelId', CheckAuth, CheckPermission, require('../controllers/Noc/FacebookChannelRemoveToken'))
router.get('/api/v1/companies/:id/channels/:channelId/whitelist', CheckAuth, CheckPermission, require('../controllers/Noc/Channels').GetWhiteList)
router.post('/api/v1/companies/:id/channels/:channelId/whitelist', Validation.AddWhiteList, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').AddWhiteList)
router.delete('/api/v1/companies/:id/channels/:channelId/whitelist', Validation.DeleteWhiteList, CheckAuth, CheckPermission, require('../controllers/Noc/Channels').DeleteWhiteList)
router.post('/api/v1/companies/:id/channels/:channelId/two-step', CheckAuth, CheckPermission, require('../controllers/Noc/Channels').EnableTwoStep)

/**
 * Integration Routes
 */

router.get('/api/v1/integrations', Validation.IntegrationListAll, CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').ListAll)
router.get('/api/v1/companies/:id/integrations', Validation.IntegrationList, CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').List)
router.get('/api/v1/integrations/:integrationId/edit', CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').Edit)
router.post('/api/v1/integrations/:integrationId/edit', Validation.IntegrationEditPost, CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').Edit)
router.get('/api/v1/companies/:id/integrations/new', CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').New)
router.post('/api/v1/companies/:id/integrations/new', Validation.IntegrationNewPost, CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').New)
router.get('/api/v1/integrations/types', CheckAuth, CheckPermission, require('../controllers/Noc/Integrations').Types)

/***
 * Users Routes
 */
router.post('/api/v1/companies/:id/users', Validation.UsersList, CheckAuth, CheckPermission, require('../controllers/Noc/Users').List)
router.post('/api/v1/companies/:userId/users/edit', Validation.UsersEditPost, CheckAuth, CheckPermission, require('../controllers/Noc/Users').EditPost)
router.get('/api/v1/companies/:userId/users/edit', CheckAuth, CheckPermission, require('../controllers/Noc/Users').Edit)
router.get('/api/v1/companies/:id/users/new', CheckAuth, CheckPermission, require('../controllers/Noc/Users').New)
router.post('/api/v1/companies/:id/users/tester', CheckAuth, CheckPermission, require('../controllers/Noc/Users').NewTester)
router.post('/api/v1/companies/:id/users/new', Validation.UsersNewPost, CheckAuth, CheckPermission, require('../controllers/Noc/Users').New)
router.delete('/api/v1/companies/users/:userId', CheckAuth, CheckPermission, require('../controllers/Noc/Users').Delete)
router.delete('/api/v1/companies/users/:userId/2fa', CheckAuth, CheckPermission, require('../controllers/Noc/Users').Delete2FA)

/**
 * Containers Routes
 */
router.get('/api/v1/containers', Validation.ContainersList, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').List)
router.get('/api/v1/avaliable-containers', Validation.ListAvailableContainers, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').ListAvailableContainers)
router.post('/api/v1/container/create', Validation.ContainersNewPost, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').New)
router.post('/api/v1/container/send-confirm-code', Validation.ContainersSendConfirmCode, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').SendConfirmCode)
router.post('/api/v1/container/confirm-code', Validation.ContainersConfirmCode, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').ConfirmCode)
router.put('/api/v1/container', Validation.ContainerEdit, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').Edit)
router.delete('/api/v1/container', Validation.ContainerDelete, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').Delete)

//cloud api
router.post('/api/v1/cloud-api-transition/send-confirm-code', Validation.SendConfirmCode, CheckAuth, CheckPermission, require('../controllers/Noc/CloudApi/SendConfirmCode'))
router.post('/api/v1/cloud-api-transition/verify-confirm-code', Validation.VerifyConfirmCode, CheckAuth, CheckPermission, require('../controllers/Noc/CloudApi/VerifyCodeAndUpdateChannel'))

//Create Company
router.post('/api/v1/companies/check-integration', Validation.ChecknIntegration, CheckAuth, CheckPermission, require('../controllers/Noc/CompanySetup/CheckLogin'))
router.post('/api/v1/companies/check-container', Validation.CheckContainer, CheckAuth, CheckPermission, require('../controllers/Noc/CompanySetup/CheckContainer'))
router.post('/api/v1/companies/create-company', Validation.CreateCompany, CheckAuth, CheckPermission, require('../controllers/Noc/CompanySetup/CreateCompany'))
router.post('/api/v1/companies/container/send-confirm-code', Validation.SendConfirmCodeForCompany, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').SendConfirmCodeForCompany)
router.get('/api/v1/companies/incomplete-companies/list', Validation.IncompleteCompanies, CheckAuth, CheckPermission, require('../controllers/Noc/CompanySetup/IncompleteCompanies'))
router.post('/api/v1/companies/confirm-code', Validation.CompanyConfirmCode, CheckAuth, CheckPermission, require('../controllers/Noc/Containers').CompanyConfirmCode)
/**
 * Reports Routes
 */
router.post('/api/v1/message-count-report', CheckAuth, CheckPermission, require('../controllers/Noc/MessageCountReportNew'))
router.post('/api/v1/weekly-message-count-report', CheckAuth, CheckPermission, require('../controllers/Noc/MessageCountReportWeekly'))
router.post('/api/v1/facebook-conversation-report', Validation.FacebookConversationReport, CheckAuth, CheckPermission, require('../controllers/Noc/FacebookConversationReport'))
router.post('/api/v1/facebook-message-report', Validation.FacebookMessageReport, CheckAuth, CheckPermission, require('../controllers/Noc/FacebookMessageReport'))

// /***
//  * Seller Routes
//  */

// router.get('/api/v1/sellers', Validation.SellersList, CheckAuth, CheckPermission, require('../controllers/Noc/Sellers').List)
// router.post('/api/v1/sellers', Validation.SellersCreate, CheckAuth, CheckPermission, require('../controllers/Noc/Sellers').Create)

/**
 *
 */
router.get('/api/v1/shopify', Validation.CheckList, CheckAuth, CheckPermission, require('../controllers/Noc/Shopify').GetStatus)

/**
 * Webhook Routes - Message Redirect
 */
router.post('/api/v1/webhooks', Validation.WebhooksCreate, CheckAuth, CheckPermission, require('../controllers/Noc/Webhooks').Create)
router.get('/api/v1/webhooks', CheckAuth, CheckPermission, require('../controllers/Noc/Webhooks').GetWebhookUrls)
router.post('/api/v1/webhooks/delete', Validation.WebhooksDelete, CheckAuth, CheckPermission, require('../controllers/Noc/Webhooks').Delete)

/**
 * Onboarding Wizard Rotes
 */
router.get('/api/v1/onboarding-wizards', Validation.OnboardingWizardList, CheckAuth, CheckPermission, require('../controllers/Noc/OnBoardingWizards').List)

router.post('/api/v1/user/login-as', Validation.LoginAs, CheckAuth, CheckPermission, require('../controllers/Noc/LoginAs'))

// Mobile Versions
router.get('/api/v1/mobile/versions', CheckAuth, CheckPermission, require('../controllers/Noc/MobileVersionsList'))
router.post('/api/v1/mobile/versions', Validation.MobileVersionAdd, CheckAuth, CheckPermission, require('../controllers/Noc/MobileVersionsAdd'))
router.delete('/api/v1/mobile/versions', Validation.MobileVersionDelete, CheckAuth, CheckPermission, require('../controllers/Noc/MobileVersionsDelete'))
router.post('/api/v1/mobile/version/update', Validation.MobileVersionEdit, CheckAuth, CheckPermission, require('../controllers/Noc/MobileVersionsEdit'))

router.post('/api/v1/firebase/analytics', CheckAuth, CheckPermission, require('../controllers/Noc/Firebase'))

router.get('/api/v1/version/notes', CheckAuth, CheckPermission, require('../controllers/Noc/VersionNotes'))
router.post('/api/v1/version/notes', CheckAuth, CheckPermission, require('../controllers/Noc/AddVersionNote'))
router.delete('/api/v1/version/notes', CheckAuth, CheckPermission, require('../controllers/Noc/DeleteVersionNote'))
router.put('/api/v1/version/notes', CheckAuth, CheckPermission, require('../controllers/Noc/EditVersionNote'))

// company dashboard
router.get('/api/v1/companies/:id/dashboard/message-limit', CheckAuth, CheckPermission, require('../controllers/Noc/MessageLimit'))
router.get('/api/v1/companies/:id/dashboard/conversation-limit', CheckAuth, CheckPermission, require('../controllers/Noc/ConversationLimit'))
router.get('/api/v1/companies/:id/dashboard/chat', CheckAuth, CheckPermission, require('../controllers/Noc/CompanyDashboard/ChatReport'))
router.get('/api/v1/companies/:id/dashboard/message', CheckAuth, CheckPermission, require('../controllers/Noc/CompanyDashboard/MessageReport'))
router.get('/api/v1/companies/:id/dashboard/thinker', CheckAuth, CheckPermission, require('../controllers/Noc/CompanyDashboard/ThinkerReport'))

// helorobo dashboard
router.post('/api/v1/dashboard/conversation-count', Validation.ConversationCount, CheckAuth, CheckPermission, require('../controllers/Noc/Dashboard/ConversationCount'))
router.post('/api/v1/dashboard/conversation-count-search', Validation.ConversationCount, CheckAuth, CheckPermission, require('../controllers/Noc/Dashboard/ConversationCountSearch'))
router.post('/api/v1/dashboard/message-count', Validation.MessageCount, CheckAuth, CheckPermission, require('../controllers/Noc/Dashboard/MessageCount'))
router.post('/api/v1/dashboard/thinker-count', Validation.ThinkerCount, CheckAuth, CheckPermission, require('../controllers/Noc/Dashboard/ThinkerCount'))
router.post('/api/v1/dashboard/company-statistics', Validation.CompanyStatistics, CheckAuth, CheckPermission, require('../controllers/Noc/Dashboard/CompanyStatistics'))

// agent-app ads resimleri
router.get('/api/v1/ads-medias', CheckAuth, CheckPermission, require('../controllers/Noc/AdsMedias/GetAdsMedias'))
router.post('/api/v1/ads-medias', Validation.AddAdsMedias, CheckAuth, CheckPermission, require('../controllers/Noc/AdsMedias/AddAdsMedias'))
router.put('/api/v1/ads-medias', Validation.EditAdsMedias, CheckAuth, CheckPermission, require('../controllers/Noc/AdsMedias/EditAdsMedias'))
router.delete('/api/v1/ads-medias', Validation.DeleteAdsMedias, CheckAuth, CheckPermission, require('../controllers/Noc/AdsMedias/DeleteAdsMedias'))
router.put('/api/v1/ads-medias/order', Validation.OrderAdsMedias, CheckAuth, CheckPermission, require('../controllers/Noc/AdsMedias/OrderAdsMedias'))

//credit line
router.post('/api/v1/add-credit-line', Validation.AddCreditLine, CheckAuth, CheckPermission, require('../controllers/Noc/CreditLine/AddCreditLine'))
router.post('/api/v1/check-credit-line', Validation.CheckCreditLine, CheckAuth, CheckPermission, require('../controllers/Noc/CreditLine/CheckCreditLine'))
router.post('/api/v1/revoke-credit-line', Validation.RevokeCreditLine, CheckAuth, CheckPermission, require('../controllers/Noc/CreditLine/RevokeCreditLine'))

// logs
router.post('/api/v1/user-logs', CheckAuth, CheckPermission, require('../controllers/Noc/UserLogs'))
router.get('/api/v1/user-log-types', CheckAuth, CheckPermission, require('../controllers/Noc/UserLogTypes'))

// packages
router.get('/api/v1/package', CheckAuth, CheckPermission, require('../controllers/Noc/Package/GetPackages'))
router.get('/api/v1/package/:id', CheckAuth, CheckPermission, require('../controllers/Noc/Package/GetPackageById'))
router.post('/api/v1/package', CheckAuth, CheckPermission, require('../controllers/Noc/Package/AddPackage'))
router.put('/api/v1/package', CheckAuth, CheckPermission, require('../controllers/Noc/Package/EditPackage'))
router.delete('/api/v1/package', CheckAuth, CheckPermission, require('../controllers/Noc/Package/DeletePackage'))


router.get('/api/v1/e-newsletter', CheckAuth, CheckPermission, require('../controllers/Noc/Newsletter/GetNewsletter'))
router.post('/api/v1/e-newsletter', CheckAuth, CheckPermission, require('../controllers/Noc/Newsletter/SendNewsletter'))
router.post('/api/v1/e-newsletter/stop', CheckAuth, CheckPermission, require('../controllers/Noc/Newsletter/StopNewsletter'))


router.post('/api/v1/event/send', CheckAuth, CheckPermission, require('../controllers/Noc/Event/SendEvent'))


router.post('/api/v1/capi/list', CheckAuth, CheckPermission, require('../controllers/Noc/Capi/CapiList'))

module.exports = router
