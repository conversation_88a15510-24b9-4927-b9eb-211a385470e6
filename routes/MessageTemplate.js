const express = require('express')
const router = express.Router()

router.use(express.urlencoded({ extended: true, limit: '11Mb' }))
router.use(express.json({ limit: '11Mb' }))

const CheckAuth = require('../middlewares/AgentApp/CheckAuth')
const CheckPermission = require('./../middlewares/AgentApp/CheckPermission')
const CheckPackageStatus = require('./../middlewares/AgentApp/CheckPackageStatus')

router.post('/send-wizard/actions', CheckAuth, CheckPermission, require('../controllers/MessageTemplate/MessageTemplateActions'))

router.get('/status', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/Status'))
router.post('/register', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/Register'))
router.post('/channels', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/AddChannel'))
router.get('/channels', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetChannels'))
router.put('/channels', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/UpdateChannel'))
router.get('/template/get-by-id', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetTemplate'))
router.get('/template', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/TemplateList'))
router.get('/template/non-parameters', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/TemplateListNonParameters'))
router.post('/template', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/CreateTemplate'))
router.post('/templatev2', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/CreateTemplateV2'))
router.delete('/template', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/DeleteTemplate'))
router.post('/template/check-name', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/CheckTemplateName'))
router.post('/send-wizard/reports', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardReports'))
router.post('/send-wizard/reports/detail', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardReportDetail'))
router.post('/send-wizard/reports/all-details', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardReportAll'))
router.post('/send-wizard/reports/chart', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardReportChart'))
router.post('/send-wizard/reports/chart/excel', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardReportChartExcel'))
router.post('/send-wizard/stop', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardStop'))
router.post('/send-wizard/resume', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SendWizardResume'))
router.post('/single-message', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SingleMessage'))
router.post('/single-messagev2', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/SingleMessageV2'))
router.get('/template/categories', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetCategories'))
router.get('/template/langs', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetLangs'))
router.get('/template/waba-status', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetWabaStatus'))
router.get('/me', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/Me'))
router.post('/daily-statistics', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetDailyStatistics'))
router.post('/contact/google-sheet', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/CreateGoogleSheetContact'))
router.get('/contact/google-sheet', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetGoogleSheetContacts'))
router.delete('/contact/google-sheet', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/DeleteGoogleSheetContact'))
router.get('/contact/google-sheet/:id', CheckAuth, CheckPermission, CheckPackageStatus(true, 'message_template'), require('../controllers/MessageTemplate/GetGoogleSheetContact'))

module.exports = router
