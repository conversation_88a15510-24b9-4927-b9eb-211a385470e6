const express = require('express')

const CheckAuth = require('../middlewares/AgentApp/CheckAuth')
const CheckPermission = require('./../middlewares/AgentApp/CheckPermission')
const CheckPackageStatus = require('./../middlewares/AgentApp/CheckPackageStatus')

const router = express.Router()

router.post('/report/chats/tag-and-note-excel', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/CustomerTagAndNoteForExcel'))
router.post('/helobot/start-conversation', CheckAuth, CheckPermission, CheckPackageStatus(true, 'helobot'), require('../controllers/Helobot/StartConversation'))
router.post('/agent/report/chat-reply', CheckAuth, CheckPermission, CheckPackageStatus(true, 'reports'), require('../controllers/AgentApp/Report/ChatReplyReportExcel'))

module.exports = router