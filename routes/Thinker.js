const express = require('express')
const router = express.Router()

const CheckAuth = require('../middlewares/AgentApp/CheckAuth')
const CheckPermission = require('./../middlewares/AgentApp/CheckPermission')
const CheckOvertimeStatus = require('./../middlewares/AgentApp/CheckOvertimeStatus')
const CheckPackageStatus = require('./../middlewares/AgentApp/CheckPackageStatus')

router.post('/', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/Register'))
router.get('/', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/HasThinker'))
router.get('/flow', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetFlow'))
router.get('/flow/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetFlowUrl'))
router.get('/flow/channel', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetFlowByChannel'))
router.post('/flow', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/Flow'))
router.delete('/flow', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/DeleteFlow'))
router.post('/flow/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/WebhookUrlToFlow'))
router.delete('/flow/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/DeleteWebhookUrl'))
router.post('/flow/start', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), CheckOvertimeStatus, require('../controllers/Thinker/StartFlow'))
router.post('/flow/stop', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), CheckOvertimeStatus, require('../controllers/Thinker/StopFlow'))
router.get('/message/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/MessageUrlList'))
router.post('/message/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/MessageUrl'))
router.delete('/message/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/DeleteMessageUrl'))
router.post('/started-flow', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetStartedFlows'))
router.post('/flow/stop-all', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/FlowStopAll'))
router.get('/app', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetApps'))
router.post('/app/register', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/RegisterApp'))
router.delete('/app/unregister', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/UnRegisterApp'))
router.get('/company-channels', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetChannels'))
router.get('/flow-category', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetFlowCategories'))
router.post('/flow-store/get', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetFlowStore'))
router.post('/flow-store/flow', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/AddFlowFromStore'))
router.post('/started-flow/get', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetStartedFlowDetail'))
router.get('/started-flow-log-url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetStartedFlowLogUrl'))
router.get('/google/accounts', CheckAuth, CheckPermission, CheckPackageStatus(false, 'thinker'), require('../controllers/Thinker/GetGooogleAccountList'))
router.get('/google/oauth2/url', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/GetGooogleOauth2'))
router.delete('/google/account/:accountId', CheckAuth, CheckPermission, CheckPackageStatus(false, 'thinker'), require('../controllers/Thinker/DeleteGoogleAccount'))
router.post('/google/sheet', CheckAuth, CheckPermission, CheckPackageStatus(true, 'thinker'), require('../controllers/Thinker/CreateGoogleAccountSheet'))
router.get('/google/sheet', CheckAuth, CheckPermission, CheckPackageStatus(false, 'thinker'), require('../controllers/Thinker/GetGoogleSheet'))
router.get('/google/sheet/:accountId', CheckAuth, CheckPermission, CheckPackageStatus(false, 'thinker'), require('../controllers/Thinker/GetGoogleAccountSheetList'))

module.exports = router
