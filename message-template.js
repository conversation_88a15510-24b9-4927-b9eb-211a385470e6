require('dotenv').config()

const pino = require('pino')()
const cors = require('cors')
const nocache = require('nocache')
const express = require('express')
const mongoose = require('mongoose')
const moment = require('moment')

const i18next = require('./i18next')

const utils = require('./libs/utils')
const helpers = require('./libs/helpers')

Promise.resolve()
  .then(() => {

    pino.info('i18next inited')

    return helpers.retryProcessOnError(() => mongoose.connect(process.env.MONGODB_CONN_STRING))

  })
  .then(() => {

    pino.info('mongodb connected')

    const app = express()

    app.models = {
      AclRole: require('./models/AclRole'),
      AclAction: require('./models/AclAction'),
      AclResource: require('./models/AclResource'),
      AclRoleResource: require('./models/AclRoleResource'),
      AclResourceAction: require('./models/AclResourceAction'),

      Job: require('./models/Job'),
      Log: require('./models/Log'),
      File: require('./models/File'),
      User: require('./models/User'),
      Chat: require('./models/Chat'),
      Token: require('./models/Token'),
      Config: require('./models/Config'),
      Message: require('./models/Message'),
      Channel: require('./models/Channel'),
      Company: require('./models/Company'),
      Container: require('./models/Container'),
      LoginWith: require('./models/LoginWith'),
      WebpImage: require('./models/WebpImage'),
      Integration: require('./models/Integration'),
      FormMessage: require('./models/FormMessage'),
      WebPushToken: require('./models/WebPushToken'),
      WebhookBaseUrl: require('./models/WebhookBaseUrl'),
      UserPermission: require('./models/UserPermission'),
      ChatIntegration: require('./models/ChatIntegration'),
      UserIntegration: require('./models/UserIntegration'),
      OnboardingWizard: require('./models/OnboardingWizard')
    }

    app.services = {
      JobService: require('./services/JobService'),
      UserService: require('./services/UserService'),
      ChatService: require('./services/ChatService'),
      QueueService: require('./services/QueueService'),
      IntegrationService: require('./modules/AgentApp/IntegrationService'),
      LogService: require('./services/LogService'),
      TsoftAgentAppService: require('./integrations/Tsoft/AgentApp/TsoftService'),
      TsoftIntegrationService: require('./integrations/Tsoft/TsoftIntegrationService'),
      ShopifyIntegrationService: require('./integrations/Shopify/ShopifyIntegrationService')
    }

    app.set('etag', false)
    app.set('trust proxy', 'loopback')


    app.use(cors())
    app.use(express.urlencoded({
      extended: true
    }))
    app.use(express.json())
    app.use(nocache())

    app.use((req, res, next) => {
      req.trace_id = utils.generateHash(30)
      req.start_time = moment().valueOf()

      const url = `${req.protocol}://${req.hostname}:${process.env.APP_PORT}`
      const endpoint = req.originalUrl.split('?')[0]

      pino.info({
        message: `trace_id generated`,
        trace_id: req.trace_id,
        method: req.method,
        url: `${url}${endpoint}`,
        all_url: `${url}${req.originalUrl}`,
        timestamp: new Date(),
        start_time: req.start_time,
        request_body: JSON.stringify({
          body: req.headers['content-type'] === 'application/json' ? req.body : {},
          headers: req.headers
        })
      })
      res.modifiedResponse = function (statusCode, response) {

        pino.info({
          trace_id: req.trace_id,
          response: JSON.stringify(response),
          timestamp: new Date(),
          url: `${url}${endpoint}`,
          all_url: `${url}${req.originalUrl}`,
          end_time: Number(((moment().valueOf() - req.start_time) / 1000 / 60).toFixed(2)) // dakika olarak süre veriyor
        })

        return res.status(statusCode).json({
          trace_id: req.trace_id,
          ...response
        })
      }
      next()
    })

    app.use((req, res, next) => {

      req.i18n = {
        language: req.headers['x-app-lang-code'] || 'tr',
        changeLanguage: (value) => {
          req.i18n.language = value
        },
        store: {
          data: i18next.store.data
        }
      }

      req.language = req.i18n.language

      req.t = (value, data = {}) => i18next.t(value, { lng: req.i18n.language, ...data })

      next()
    })

    app.all('/status', (req, res, next) => {

      return helpers.getCpuUsage(req, res, next).then(cpuUsage => {

        const memUsage = process.memoryUsage().heapUsed / 1024 / 1024

        return res.json({
          success: true,
          name: 'Backend',
          data: {
            up_time: helpers.upTime(),
            mem_usage: `${Math.round(memUsage * 100) / 100} MB`,
            cpu_usage: `${cpuUsage} %`,
            nodejs_version: process.versions.node,
            tz_version: process.versions.tz
          }
        })
      })
    })

    app.get('/health', (req, res, next) => {
      return res.modifiedResponse(200, { success: true })
    })

    app.post('/message-template/createV2', require('./controllers/SendMessageTemplateV2'))
    app.post('/message-template/createV3', require('./controllers/SendMessageTemplateV3'))

    app.use((err, req, res, next) => {
      helpers.handleError(req, err, res)
    })

    return app

  }).then(async app => {

    const RabbitMQConnection = require('./modules/RabbitMQConnection')
    await RabbitMQConnection.connection()

    const RedisConnection = require('./modules/RedisConnection')
    await RedisConnection.connection()

    const port = 3033

    app.listen(port, () => pino.info(`message template app listening on port ${port}!`))

  })
  .catch(error => {

    pino.error('BACKEND: mongodb error')
    pino.error(error)

  })
